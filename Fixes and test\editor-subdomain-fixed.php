<?php
/**
 * Editor Subdomain Fixed Version
 * 
 * This file should be placed in WordPress root directory and accessed via subdomain
 * It properly loads WordPress and includes the editor template
 */

// WordPress root directory path (adjust if needed)
$wp_root = dirname(__FILE__);

// Load WordPress
if (!defined('ABSPATH')) {
    require_once($wp_root . '/wp-load.php');
}

// Define plugin constants if not already defined
if (!defined('CPD_PLUGIN_DIR')) {
    define('CPD_PLUGIN_DIR', WP_PLUGIN_DIR . '/church-programme-dashboard/');
}
if (!defined('CPD_PLUGIN_URL')) {
    define('CPD_PLUGIN_URL', plugins_url('/', WP_PLUGIN_DIR . '/church-programme-dashboard/church-programme-dashboard.php'));
}
if (!defined('CPD_VERSION')) {
    define('CPD_VERSION', '1.0.0');
}

// Include the original editor template
$editor_file = WP_PLUGIN_DIR . '/church-programme-dashboard/public/templates/editor.php';
if (file_exists($editor_file)) {
    include $editor_file;
} else {
    die('Church Programme Dashboard plugin not found. Please ensure the plugin is installed and activated.');
}
?>
