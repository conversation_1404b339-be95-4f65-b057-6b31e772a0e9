# Subdomain Setup Guide for Church Programme Dashboard

This guide explains how to set up the dashboard and editor pages to run on subdomains instead of WordPress paths.

## Method 1: Standalone Subdomain Handler (Recommended)

### Step 1: Upload Files
1. Upload `cpd-subdomain-standalone.php` to your **WordPress root directory** (same level as wp-config.php)
2. Ensure the Church Programme Dashboard plugin is installed and activated

### Step 2: Configure Server Redirects

#### For Apache (.htaccess)
Add these rules to your WordPress `.htaccess` file (before the WordPress rules):

```apache
# Subdomain redirects for Church Programme Dashboard
RewriteCond %{HTTP_HOST} ^churchprogramme\.jermesa\.com$ [NC]
RewriteRule ^(.*)$ /cpd-subdomain-standalone.php [L]

RewriteCond %{HTTP_HOST} ^churcheditor\.jermesa\.com$ [NC]
RewriteRule ^(.*)$ /cpd-subdomain-standalone.php [L]
```

#### For Nginx
Add these server blocks to your Nginx configuration:

```nginx
# Dashboard subdomain
server {
    server_name churchprogramme.jermesa.com;
    root /path/to/your/wordpress;
    
    location / {
        try_files $uri $uri/ /cpd-subdomain-standalone.php;
    }
    
    location ~ \.php$ {
        include fastcgi_params;
        fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
    }
}

# Editor subdomain
server {
    server_name churcheditor.jermesa.com;
    root /path/to/your/wordpress;
    
    location / {
        try_files $uri $uri/ /cpd-subdomain-standalone.php;
    }
    
    location ~ \.php$ {
        include fastcgi_params;
        fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
    }
}
```

### Step 3: Configure DNS
Make sure your DNS records point the subdomains to your server:
- `churchprogramme.jermesa.com` → Your server IP
- `churcheditor.jermesa.com` → Your server IP

## Method 2: Direct Template Access (Alternative)

If the standalone handler doesn't work, you can configure direct template access:

### For Apache (.htaccess)
```apache
# Direct template access
RewriteCond %{HTTP_HOST} ^churchprogramme\.jermesa\.com$ [NC]
RewriteRule ^(.*)$ /wp-content/plugins/church-programme-dashboard/public/templates/dashboard.php [L]

RewriteCond %{HTTP_HOST} ^churcheditor\.jermesa\.com$ [NC]
RewriteRule ^(.*)$ /wp-content/plugins/church-programme-dashboard/public/templates/editor.php [L]
```

### For Nginx
```nginx
# Dashboard subdomain
server {
    server_name churchprogramme.jermesa.com;
    root /path/to/your/wordpress;
    
    location / {
        try_files $uri $uri/ /wp-content/plugins/church-programme-dashboard/public/templates/dashboard.php;
    }
}

# Editor subdomain
server {
    server_name churcheditor.jermesa.com;
    root /path/to/your/wordpress;
    
    location / {
        try_files $uri $uri/ /wp-content/plugins/church-programme-dashboard/public/templates/editor.php;
    }
}
```

## Testing the Setup

1. **Test URLs:**
   - Dashboard: https://churchprogramme.jermesa.com/
   - Editor: https://churcheditor.jermesa.com/

2. **Check for Issues:**
   - Open browser developer tools
   - Check Console tab for JavaScript errors
   - Check Network tab for failed asset loading
   - Check for PHP errors in WordPress logs

3. **Verify Components:**
   - ✅ Carousel slider loads with programme data
   - ✅ Calendar view works with navigation
   - ✅ All CSS styles are applied
   - ✅ No PHP errors displayed

## Troubleshooting

### Common Issues:

1. **PHP Errors:**
   - Check WordPress error logs
   - Ensure plugin is activated
   - Verify file permissions

2. **Assets Not Loading:**
   - Check browser console for 404 errors
   - Verify CPD_PLUGIN_URL constant is correct
   - Ensure CSS and JS files exist

3. **REST API Issues:**
   - Check if WordPress REST API is working
   - Verify programme data exists
   - Check browser network requests

### Debug Steps:

1. Enable WordPress debug mode in wp-config.php:
   ```php
   define('WP_DEBUG', true);
   define('WP_DEBUG_LOG', true);
   define('WP_DEBUG_DISPLAY', false);
   ```

2. Check the WordPress debug log for errors

3. Test REST API endpoints directly:
   - `https://yourdomain.com/wp-json/cpd/v1/programmes/upcoming`
   - `https://yourdomain.com/wp-json/cpd/v1/programmes/2024/12`

## Benefits of This Approach

- ✅ Clean subdomain URLs (not WordPress paths)
- ✅ Proper WordPress integration
- ✅ All plugin functionality preserved
- ✅ REST API access maintained
- ✅ Theme-independent operation
- ✅ Better SEO and user experience

The standalone handler approach ensures that WordPress is properly loaded while maintaining clean subdomain URLs for your dashboard and editor pages.
