<?php
/**
 * WordPress Rewrite Solution for Church Programme Dashboard
 * 
 * This approach uses WordPress rewrite rules to create clean URLs
 * that work within hosting environments with subdomain restrictions.
 * 
 * Usage:
 * 1. Add this code to your theme's functions.php or as a plugin
 * 2. Configure the rewrite rules
 * 3. Access via: yourdomain.com/programme/ and yourdomain.com/editor/
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

class CPD_Rewrite_Solution {
    
    /**
     * Initialize the rewrite solution
     */
    public static function init() {
        add_action('init', array(__CLASS__, 'add_rewrite_rules'));
        add_action('template_redirect', array(__CLASS__, 'handle_rewrite_requests'));
        add_filter('query_vars', array(__CLASS__, 'add_query_vars'));
    }
    
    /**
     * Add rewrite rules
     */
    public static function add_rewrite_rules() {
        // Dashboard route
        add_rewrite_rule(
            '^programme/?$',
            'index.php?cpd_page=dashboard',
            'top'
        );
        
        // Editor route
        add_rewrite_rule(
            '^editor/?$',
            'index.php?cpd_page=editor',
            'top'
        );
    }
    
    /**
     * Add query variables
     */
    public static function add_query_vars($vars) {
        $vars[] = 'cpd_page';
        return $vars;
    }
    
    /**
     * Handle rewrite requests
     */
    public static function handle_rewrite_requests() {
        $cpd_page = get_query_var('cpd_page');
        
        if ($cpd_page === 'dashboard') {
            self::render_dashboard();
        } elseif ($cpd_page === 'editor') {
            self::render_editor();
        }
    }
    
    /**
     * Render dashboard template
     */
    public static function render_dashboard() {
        // Prevent theme from loading
        add_filter('template_include', '__return_false');
        add_filter('show_admin_bar', '__return_false');
        
        // Remove all theme actions
        remove_all_actions('wp_head');
        remove_all_actions('wp_footer');
        remove_all_actions('wp_enqueue_scripts');
        
        // Re-add essential WordPress actions
        add_action('wp_head', 'wp_enqueue_scripts', 1);
        add_action('wp_head', 'wp_print_styles', 8);
        add_action('wp_head', 'wp_print_head_scripts', 9);
        add_action('wp_footer', 'wp_print_footer_scripts', 20);
        
        // Check if plugin file exists
        $dashboard_file = WP_PLUGIN_DIR . '/church-programme-dashboard/public/templates/dashboard.php';
        
        if (file_exists($dashboard_file)) {
            // Include the dashboard template
            include $dashboard_file;
            exit;
        } else {
            wp_die('Church Programme Dashboard plugin not found.');
        }
    }
    
    /**
     * Render editor template
     */
    public static function render_editor() {
        // Prevent theme from loading
        add_filter('template_include', '__return_false');
        add_filter('show_admin_bar', '__return_false');
        
        // Remove all theme actions
        remove_all_actions('wp_head');
        remove_all_actions('wp_footer');
        remove_all_actions('wp_enqueue_scripts');
        
        // Re-add essential WordPress actions
        add_action('wp_head', 'wp_enqueue_scripts', 1);
        add_action('wp_head', 'wp_print_styles', 8);
        add_action('wp_head', 'wp_print_head_scripts', 9);
        add_action('wp_footer', 'wp_print_footer_scripts', 20);
        
        // Check if plugin file exists
        $editor_file = WP_PLUGIN_DIR . '/church-programme-dashboard/public/templates/editor.php';
        
        if (file_exists($editor_file)) {
            // Include the editor template
            include $editor_file;
            exit;
        } else {
            wp_die('Church Programme Dashboard plugin not found.');
        }
    }
}

// Initialize the rewrite solution
CPD_Rewrite_Solution::init();

// Flush rewrite rules on activation
register_activation_hook(__FILE__, function() {
    flush_rewrite_rules();
});

// Flush rewrite rules on deactivation
register_deactivation_hook(__FILE__, function() {
    flush_rewrite_rules();
});
?>
