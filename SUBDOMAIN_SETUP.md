# Subdomain Setup Guide

This plugin uses **true subdomains** for the dashboard and editor pages, not path-based URLs.

## Understanding Subdomains

Instead of:
- ❌ `yoursite.com/programme/` (path-based)
- ❌ `yoursite.com/editor/` (path-based)

You will have:
- ✅ `churchprogramme.yoursite.com` (subdomain)
- ✅ `churcheditor.yoursite.com` (subdomain)

## Quick Setup (3 Steps)

### Step 1: Configure Subdomains in Plugin
1. Go to **WordPress Admin** > **Church Programme** > **Settings**
2. Click the **"Subdomains"** tab
3. Set your subdomain names:
   - **Dashboard Subdomain:** `churchprogramme` (or your choice)
   - **Editor Subdomain:** `churcheditor` (or your choice)
4. Click **"Save All Settings"**

### Step 2: Configure DNS Records
You need to add DNS records for each subdomain.

**Where to do this:**
- Your domain registrar (GoDaddy, Namecheap, etc.)
- Your DNS provider (Cloudflare, etc.)
- Your hosting control panel (cPanel, Plesk, etc.)

**What to add:**

#### Option A: A Record (Recommended)
```
Type: A
Name: churchprogramme
Value: YOUR_SERVER_IP_ADDRESS
TTL: 3600 (or Auto)

Type: A
Name: churcheditor
Value: YOUR_SERVER_IP_ADDRESS
TTL: 3600 (or Auto)
```

#### Option B: CNAME Record
```
Type: CNAME
Name: churchprogramme
Value: yoursite.com
TTL: 3600 (or Auto)

Type: CNAME
Name: churcheditor
Value: yoursite.com
TTL: 3600 (or Auto)
```

### Step 3: Wait for DNS Propagation
- DNS changes can take **5 minutes to 48 hours** to propagate
- Usually takes 15-30 minutes
- Check status at: https://dnschecker.org

## Detailed Setup Instructions

### Finding Your Server IP Address

#### Method 1: Command Line
```bash
ping yoursite.com
```

#### Method 2: Online Tool
Visit: https://www.whatismyip.com/hostname-to-ip-lookup/

#### Method 3: Hosting Control Panel
- cPanel: Home > Server Information
- Plesk: Tools & Settings > Server Information

### DNS Configuration by Provider

#### GoDaddy
1. Log in to GoDaddy
2. Go to **My Products** > **DNS**
3. Click **Add** under Records
4. Select **A** or **CNAME**
5. Enter subdomain name (e.g., `churchprogramme`)
6. Enter your server IP or domain
7. Click **Save**
8. Repeat for editor subdomain

#### Namecheap
1. Log in to Namecheap
2. Go to **Domain List** > **Manage**
3. Click **Advanced DNS**
4. Click **Add New Record**
5. Select **A Record** or **CNAME Record**
6. Host: `churchprogramme`
7. Value: Your server IP or domain
8. Click **Save**
9. Repeat for editor subdomain

#### Cloudflare
1. Log in to Cloudflare
2. Select your domain
3. Go to **DNS** tab
4. Click **Add record**
5. Type: **A** or **CNAME**
6. Name: `churchprogramme`
7. Content: Your server IP or domain
8. Proxy status: **Proxied** (orange cloud) or **DNS only** (gray cloud)
9. Click **Save**
10. Repeat for editor subdomain

#### cPanel
1. Log in to cPanel
2. Go to **Domains** > **Zone Editor**
3. Click **+ A Record** or **+ CNAME Record**
4. Name: `churchprogramme.yoursite.com`
5. Address/Target: Your server IP or domain
6. Click **Add Record**
7. Repeat for editor subdomain

### Server Configuration

#### Apache (.htaccess)
Your WordPress `.htaccess` should already handle subdomains. If not, add:

```apache
<IfModule mod_rewrite.c>
RewriteEngine On
RewriteBase /
RewriteRule ^index\.php$ - [L]
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule . /index.php [L]
</IfModule>
```

#### Nginx
Add to your server block:

```nginx
server {
    listen 80;
    server_name yoursite.com *.yoursite.com;
    
    root /path/to/wordpress;
    index index.php;
    
    location / {
        try_files $uri $uri/ /index.php?$args;
    }
    
    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php7.4-fpm.sock;
        fastcgi_index index.php;
        include fastcgi_params;
    }
}
```

#### WordPress Multisite
If using WordPress Multisite, you may need to configure subdomain mapping differently. Consult your hosting provider.

## Wildcard SSL Certificate

For HTTPS to work on subdomains, you need a wildcard SSL certificate.

### Free Option: Let's Encrypt
```bash
certbot certonly --manual --preferred-challenges=dns --email <EMAIL> --server https://acme-v02.api.letsencrypt.org/directory --agree-tos -d yoursite.com -d *.yoursite.com
```

### Paid Options:
- Cloudflare (Free with account)
- Your hosting provider
- SSL certificate vendors

### Cloudflare SSL (Easiest)
1. Add your domain to Cloudflare
2. Change nameservers at your registrar
3. Enable **Full** or **Full (Strict)** SSL in Cloudflare
4. Subdomains automatically get SSL

## Testing Your Setup

### Step 1: Check DNS Propagation
Visit: https://dnschecker.org
- Enter: `churchprogramme.yoursite.com`
- Should show your server IP

### Step 2: Test Subdomain Access
1. Open browser
2. Visit: `http://churchprogramme.yoursite.com`
3. Should load the dashboard
4. Visit: `http://churcheditor.yoursite.com`
5. Should load the editor login

### Step 3: Test HTTPS
1. Visit: `https://churchprogramme.yoursite.com`
2. Should load with valid SSL certificate
3. No browser warnings

## Troubleshooting

### Subdomain Shows 404 Error
**Cause:** DNS not configured or not propagated yet

**Solution:**
1. Check DNS records are correct
2. Wait longer for propagation (up to 48 hours)
3. Clear browser cache
4. Try incognito/private browsing

### Subdomain Shows Main Site
**Cause:** DNS pointing to wrong location or server not configured

**Solution:**
1. Verify DNS records point to correct IP
2. Check server configuration (Apache/Nginx)
3. Ensure WordPress is handling the subdomain

### SSL Certificate Error
**Cause:** No wildcard SSL certificate

**Solution:**
1. Install wildcard SSL certificate
2. Or use Cloudflare for free SSL
3. Or get Let's Encrypt wildcard certificate

### "This site can't be reached"
**Cause:** DNS not configured or firewall blocking

**Solution:**
1. Verify DNS records exist
2. Check server firewall allows port 80/443
3. Check hosting provider allows subdomains

### Subdomain Works Locally But Not Remotely
**Cause:** Local DNS cache or hosts file

**Solution:**
1. Clear DNS cache:
   - Windows: `ipconfig /flushdns`
   - Mac: `sudo dscacheutil -flushcache`
   - Linux: `sudo systemd-resolve --flush-caches`
2. Check `/etc/hosts` file for local overrides

## Advanced Configuration

### Custom Subdomain Names
You can use any subdomain names you want:
- `programme.yoursite.com`
- `schedule.yoursite.com`
- `calendar.yoursite.com`
- `admin.yoursite.com`

Just configure them in the plugin settings and add corresponding DNS records.

### Multiple Domains
If you have multiple domains pointing to the same WordPress:
1. Add DNS records for each domain
2. Configure WordPress to accept multiple domains
3. Plugin will work on all domains

### Subdomain Redirects
To redirect old URLs to new subdomains, add to `.htaccess`:

```apache
RewriteEngine On
RewriteCond %{HTTP_HOST} ^yoursite\.com$ [NC]
RewriteRule ^programme/?$ https://churchprogramme.yoursite.com/ [R=301,L]
RewriteRule ^editor/?$ https://churcheditor.yoursite.com/ [R=301,L]
```

## Security Considerations

### 1. Use HTTPS
Always use HTTPS for both subdomains, especially the editor.

### 2. Firewall Rules
Configure firewall to allow traffic to subdomains.

### 3. Rate Limiting
Consider rate limiting on editor subdomain to prevent brute force attacks.

### 4. Access Control
The editor subdomain requires login, but consider additional IP restrictions if needed.

## Support

### Common Questions

**Q: Can I use the same subdomain for both dashboard and editor?**
A: No, they must be different subdomains.

**Q: Can I change subdomain names later?**
A: Yes, just update in plugin settings and update DNS records.

**Q: Do I need to configure WordPress multisite?**
A: No, this plugin works with regular WordPress installations.

**Q: Will this work on shared hosting?**
A: Yes, as long as your host allows subdomains and you can configure DNS.

**Q: Can I use a subdomain from a different domain?**
A: No, subdomains must be under the same base domain as your WordPress installation.

### Getting Help

If you encounter issues:
1. Check this guide thoroughly
2. Verify DNS configuration at https://dnschecker.org
3. Check WordPress debug.log for errors
4. Contact your hosting provider for server-specific help
5. Visit https://www.jermesa.com for support

## Quick Reference

### Your Configuration
- **Base Domain:** [Your domain will be detected automatically]
- **Dashboard Subdomain:** [Set in plugin settings]
- **Editor Subdomain:** [Set in plugin settings]

### DNS Records Template
```
Type: A
Name: [your-dashboard-subdomain]
Value: [your-server-ip]

Type: A
Name: [your-editor-subdomain]
Value: [your-server-ip]
```

### Testing URLs
- Dashboard: `http://[dashboard-subdomain].[your-domain]/`
- Editor: `http://[editor-subdomain].[your-domain]/`

---

**Need Help?** Visit https://www.jermesa.com

**Plugin Version:** 1.0.0  
**Last Updated:** 2025-09-30

