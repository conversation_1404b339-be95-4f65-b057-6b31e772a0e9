<?php
/**
 * Admin Class - Rebuilt from Scratch
 */

if (!defined('ABSPATH')) {
    exit;
}

class CPD_Admin {
    
    /**
     * Initialize
     */
    public static function init() {
        add_action('admin_menu', array(__CLASS__, 'add_menu'));
        add_action('admin_enqueue_scripts', array(__CLASS__, 'load_scripts'), 999);
        error_log('CPD_Admin::init() called');
    }
    
    /**
     * Add menu
     */
    public static function add_menu() {
        add_menu_page(
            'Church Programme Dashboard',
            'Church Programme',
            'manage_options',
            'church-programme-dashboard',
            array(__CLASS__, 'settings_page'),
            'dashicons-calendar-alt',
            30
        );
        
        add_submenu_page(
            'church-programme-dashboard',
            'Settings',
            'Settings',
            'manage_options',
            'church-programme-dashboard',
            array(__CLASS__, 'settings_page')
        );
        
        add_submenu_page(
            'church-programme-dashboard',
            'Editor Users',
            'Editor Users',
            'manage_options',
            'cpd-editor-users',
            array(__CLASS__, 'users_page')
        );
        
        error_log('CPD_Admin: Menu added');
    }
    
    /**
     * Load scripts
     */
    public static function load_scripts($hook) {
        error_log('CPD_Admin: load_scripts hook=' . $hook);
        
        // Only load on our pages
        if (strpos($hook, 'church-programme') === false && strpos($hook, 'cpd-') === false) {
            return;
        }
        
        error_log('CPD_Admin: Loading scripts for our page');
        
        // WordPress dependencies
        wp_enqueue_style('wp-color-picker');
        wp_enqueue_script('wp-color-picker');
        wp_enqueue_media();
        
        // Our styles
        wp_enqueue_style(
            'cpd-admin-css',
            CPD_PLUGIN_URL . 'admin/css/admin-style.css',
            array(),
            time()
        );
        
        // Our scripts
        wp_enqueue_script(
            'cpd-admin-js',
            CPD_PLUGIN_URL . 'admin/js/admin-script.js',
            array('jquery', 'wp-color-picker'),
            time(),
            true
        );
        
        wp_enqueue_script(
            'cpd-programmes-js',
            CPD_PLUGIN_URL . 'admin/js/admin-programmes.js',
            array('jquery', 'cpd-admin-js'),
            time(),
            true
        );
        
        // Create cpdAdmin object
        wp_localize_script('cpd-admin-js', 'cpdAdmin', array(
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('cpd_ajax_nonce'),
            'restUrl' => rest_url('cpd/v1/'),
            'restNonce' => wp_create_nonce('wp_rest'),
            'dashboardUrl' => CPD_Subdomain::get_dashboard_url(),
            'editorUrl' => CPD_Subdomain::get_editor_url(),
        ));
        
        wp_localize_script('cpd-programmes-js', 'cpdProgrammes', array(
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('cpd_ajax_nonce'),
            'restUrl' => rest_url('cpd/v1/'),
            'restNonce' => wp_create_nonce('wp_rest'),
        ));
        
        error_log('CPD_Admin: Scripts loaded and localized');
    }
    
    /**
     * Settings page
     */
    public static function settings_page() {
        include CPD_PLUGIN_DIR . 'admin/views/settings-page.php';
    }
    
    /**
     * Users page
     */
    public static function users_page() {
        include CPD_PLUGIN_DIR . 'admin/views/users-page.php';
    }
}

