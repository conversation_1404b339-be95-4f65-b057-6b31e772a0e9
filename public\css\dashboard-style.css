/**
 * Dashboard Styles - Mobile First Design
 */

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body.cpd-dashboard-body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    color: var(--text-color);
    background: #f7fafc;
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.cpd-dashboard-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header */
.cpd-header {
    background: var(--header-bg-color);
    padding: 2rem 1rem;
    text-align: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    position: relative;
}

.cpd-header-content {
    max-width: 1200px;
    margin: 0 auto;
}

.cpd-header-title {
    font-family: 'Poppins', sans-serif;
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--primary-color);
    margin: 0;
}

/* Carousel Section */
.cpd-carousel-section {
    padding: 1.5rem 1rem;
    background: #fff;
}

.cpd-carousel-container {
    max-width: 600px;
    margin: 0 auto;
    position: relative;
}

.cpd-carousel-slides {
    position: relative;
    overflow: hidden;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    --animation-speed: 500ms;
    --slide-direction: right-to-left;
}

/* Animation Styles */
.cpd-animation-slide .cpd-carousel-slide {
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    transition: transform var(--animation-speed) ease;
}

.cpd-animation-slide .cpd-carousel-slide.active {
    transform: translateX(0);
}

.cpd-animation-slide .cpd-carousel-slide:not(.active) {
    transform: translateX(100%);
}

.cpd-animation-fade .cpd-carousel-slide {
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: opacity var(--animation-speed) ease;
}

.cpd-animation-fade .cpd-carousel-slide.active {
    opacity: 1;
}

.cpd-animation-zoom .cpd-carousel-slide {
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transform: scale(0.8);
    transition: all var(--animation-speed) ease;
}

.cpd-animation-zoom .cpd-carousel-slide.active {
    opacity: 1;
    transform: scale(1);
}

.cpd-animation-flip .cpd-carousel-slide {
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    backface-visibility: hidden;
    transform-style: preserve-3d;
    transition: transform var(--animation-speed) ease;
}

.cpd-animation-flip .cpd-carousel-slide:not(.active) {
    transform: rotateY(180deg);
}

.cpd-animation-flip .cpd-carousel-slide.active {
    transform: rotateY(0deg);
}

.cpd-animation-continuous-scroll .cpd-carousel-slide {
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.cpd-animation-continuous-scroll .cpd-carousel-slides {
    display: flex;
    transition: transform var(--animation-speed) ease;
}

.cpd-animation-continuous-scroll .cpd-carousel-slide {
    flex: 0 0 100%;
    position: relative;
}

/* Default slide styles for non-animated carousel */
.cpd-carousel-slide {
    display: none;
    padding: 2rem 1.5rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #fff;
    position: relative;
    overflow: hidden;
}

.cpd-carousel-slide.active {
    display: block;
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.cpd-slide-bg-text {
    position: absolute;
    left: 70%;
    font-size: 5rem;
    font-weight: 700;
    opacity: 0.1;
    white-space: nowrap;
    pointer-events: none;
    font-family: 'Poppins', sans-serif;
    padding-top: 25px;
}

.cpd-slide-content {
    position: relative;
    z-index: 1;
}

.cpd-slide-title {
    font-family: 'Poppins', sans-serif;
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.cpd-slide-time {
    font-size: 1.125rem;
    margin-bottom: 1rem;
    opacity: 0.9;
}

.cpd-slide-date {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid rgba(255, 255, 255, 0.3);
}

.cpd-slide-details {
    font-size: 0.95rem;
    line-height: 1.8;
}

.cpd-slide-details p {
    margin-bottom: 0.75rem;
}

.cpd-slide-details strong {
    font-weight: 600;
}

/* Carousel Navigation */
.cpd-carousel-nav {
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    transform: translateY(-50%);
    display: flex;
    justify-content: space-between;
    padding: 0 0.5rem;
    pointer-events: none;
    z-index: 10;
}

.cpd-carousel-btn {
    pointer-events: all;
    background: rgba(255, 255, 255, 0.068);
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.cpd-carousel-btn:hover {
    background: #fff;
    transform: scale(1.1);
}

.cpd-carousel-btn svg {
    color: var(--primary-color);
}

/* Carousel Indicators */
.cpd-carousel-indicators {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    margin-top: 1rem;
}

.cpd-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #cbd5e0;
    cursor: pointer;
    transition: all 0.3s ease;
}

.cpd-indicator.active {
    background: var(--accent-color);
    width: 24px;
    border-radius: 4px;
}

/* Loading Spinner */
.cpd-carousel-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 300px;
    color: var(--secondary-color);
}

.cpd-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #e2e8f0;
    border-top-color: var(--accent-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Notice Board */
.cpd-notice-board {
    padding: 1.5rem 1rem;
    background: #fff5e6;
}

.cpd-notice-board-container {
    max-width: 800px;
    margin: 0 auto;
    display: flex;
    gap: 1rem;
    padding: 1.5rem;
    background: #fff;
    border-radius: 12px;
    border-left: 4px solid #f59e0b;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.cpd-notice-icon {
    flex-shrink: 0;
    color: #f59e0b;
}

.cpd-notice-content {
    flex: 1;
}

.cpd-notice-title {
    font-family: 'Poppins', sans-serif;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.cpd-notice-text {
    color: var(--text-color);
    font-size: 0.95rem;
}

/* Calendar Section */
.cpd-calendar-section {
    padding: 2rem 1rem;
    flex: 1;
}

.cpd-calendar-container {
    max-width: 800px;
    margin: 0 auto;
    background: #fff;
    border-radius: 16px;
    padding: 1.5rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.cpd-calendar-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1.5rem;
}

.cpd-calendar-title {
    font-family: 'Poppins', sans-serif;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--primary-color);
}

.cpd-calendar-nav-btn {
    background: #f7fafc;
    border: none;
    width: 36px;
    height: 36px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.cpd-calendar-nav-btn:hover {
    background: #edf2f7;
}

.cpd-calendar-nav-btn svg {
    color: #e2e8f0;
}

.cpd-calendar-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 0.5rem;
}

.cpd-calendar-day-header {
    text-align: center;
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--secondary-color);
    padding: 0.5rem;
    text-transform: uppercase;
}

.cpd-calendar-day {
    aspect-ratio: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50px;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.cpd-calendar-day.empty {
    cursor: default;
}

.cpd-calendar-day.current-month {
    color: var(--text-color);
}

.cpd-calendar-day.other-month {
    color: #cbd5e0;
}

.cpd-calendar-day.today {
    background: #e6f2ff;
    font-weight: 600;
}

.cpd-calendar-day.has-programme {
    background: var(--accent-color);
    color: #fff;
    font-weight: 600;
}

.cpd-calendar-day.has-programme:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(49, 130, 206, 0.3);
}

.cpd-calendar-day.has-programme::after {
    content: '';
    position: absolute;
    bottom: 4px;
    left: 50%;
    transform: translateX(-50%);
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background: #fff;
}

/* Modal */
.cpd-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1000;
}

.cpd-modal.active {
    display: block;
}

.cpd-modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
}

.cpd-modal-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: #fff;
    border-radius: 16px;
    max-width: 90%;
    width: 500px;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translate(-50%, -45%);
    }
    to {
        opacity: 1;
        transform: translate(-50%, -50%);
    }
}

.cpd-modal-close {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: #f7fafc;
    border: none;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 10;
}

.cpd-modal-close:hover {
    background: #edf2f7;
    transform: rotate(90deg);
}

.cpd-modal-body {
    padding: 2rem;
}

/* Footer */
.cpd-footer {
    background: var(--primary-color);
    color: #fff;
    padding: 2rem 1rem;
    margin-top: auto;
}

.cpd-footer-content {
    max-width: 1200px;
    margin: 0 auto;
    text-align: center;
}

.cpd-footer-attribution {
    font-size: 0.875rem;
    opacity: 0.9;
}

.cpd-footer-attribution p {
    margin-bottom: 0.5rem;
}

.cpd-footer-attribution a {
    color: #fff;
    text-decoration: underline;
}

.cpd-footer-attribution a:hover {
    opacity: 0.8;
}

/* Cookie Consent */
.cpd-cookie-consent {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: var(--primary-color);
    color: #fff;
    padding: 1rem;
    box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.15);
    z-index: 999;
    transform: translateY(100%);
    transition: transform 0.3s ease;
}

.cpd-cookie-consent.show {
    transform: translateY(0);
}

.cpd-cookie-content {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 1rem;
    flex-wrap: wrap;
}

.cpd-cookie-content p {
    margin: 0;
    font-size: 0.9rem;
    flex: 1;
}

.cpd-cookie-accept {
    background: #fff;
    color: var(--primary-color);
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.cpd-cookie-accept:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Responsive Design */
@media (min-width: 768px) {
    .cpd-header-title {
        font-size: 2.5rem;
    }
    
    
    .cpd-slide-bg-text {
        font-size: 7rem;
    }
    
    .cpd-calendar-grid {
        gap: 0.75rem;
    }
    
    .cpd-calendar-day {
        font-size: 1rem;
    }
}

@media (max-width: 480px) {
    .cpd-slide-bg-text {
        font-size: 3rem;
    }
    
    .cpd-calendar-day-header {
        font-size: 0.65rem;
    }
    
    .cpd-calendar-day {
        font-size: 0.8rem;
    }
}
