<?php
/**
 * Verify Admin Rebuild
 * Run this to verify all new files are in place
 */

echo "<!DOCTYPE html><html><head><title>Verify Admin Rebuild</title>";
echo "<style>body{font-family:Arial;padding:20px;background:#f5f5f5;}";
echo ".pass{color:green;font-weight:bold;}.fail{color:red;font-weight:bold;}";
echo "table{background:white;border-collapse:collapse;width:100%;margin:20px 0;}";
echo "th,td{border:1px solid #ddd;padding:12px;text-align:left;}";
echo "th{background:#4CAF50;color:white;}</style></head><body>";

echo "<h1>🔍 Admin Rebuild Verification</h1>";
echo "<p>Checking if all new admin files are in place...</p>";

$base_dir = __DIR__;
$checks = array();

// Check files exist
$files = array(
    'admin/class-cpd-admin.php',
    'admin/class-cpd-admin-settings.php',
    'admin/js/admin-script.js',
    'admin/js/admin-programmes.js',
    'admin/css/admin-style.css',
    'admin/views/settings-page.php',
    'admin/views/users-page.php',
);

echo "<h2>File Existence Check</h2>";
echo "<table><tr><th>File</th><th>Status</th><th>Size</th><th>Modified</th></tr>";

foreach ($files as $file) {
    $path = $base_dir . '/' . $file;
    $exists = file_exists($path);
    $size = $exists ? filesize($path) : 0;
    $modified = $exists ? date('Y-m-d H:i:s', filemtime($path)) : 'N/A';
    
    echo "<tr>";
    echo "<td><code>" . htmlspecialchars($file) . "</code></td>";
    echo "<td class='" . ($exists ? 'pass' : 'fail') . "'>" . ($exists ? '✅ EXISTS' : '❌ MISSING') . "</td>";
    echo "<td>" . ($exists ? number_format($size) . ' bytes' : '-') . "</td>";
    echo "<td>" . htmlspecialchars($modified) . "</td>";
    echo "</tr>";
    
    $checks[] = array('check' => $file, 'pass' => $exists);
}

echo "</table>";

// Check file contents
echo "<h2>File Content Verification</h2>";
echo "<table><tr><th>File</th><th>Check</th><th>Status</th></tr>";

// Check admin class
$admin_file = $base_dir . '/admin/class-cpd-admin.php';
if (file_exists($admin_file)) {
    $content = file_get_contents($admin_file);
    
    $has_init = strpos($content, 'public static function init()') !== false;
    $has_menu = strpos($content, 'public static function add_menu()') !== false;
    $has_scripts = strpos($content, 'public static function load_scripts(') !== false;
    $has_localize = strpos($content, 'wp_localize_script') !== false;
    $has_cpdadmin = strpos($content, "'cpdAdmin'") !== false;
    
    echo "<tr><td rowspan='5'><code>class-cpd-admin.php</code></td>";
    echo "<td>Has init() method</td><td class='" . ($has_init ? 'pass' : 'fail') . "'>" . ($has_init ? '✅' : '❌') . "</td></tr>";
    echo "<tr><td>Has add_menu() method</td><td class='" . ($has_menu ? 'pass' : 'fail') . "'>" . ($has_menu ? '✅' : '❌') . "</td></tr>";
    echo "<tr><td>Has load_scripts() method</td><td class='" . ($has_scripts ? 'pass' : 'fail') . "'>" . ($has_scripts ? '✅' : '❌') . "</td></tr>";
    echo "<tr><td>Has wp_localize_script</td><td class='" . ($has_localize ? 'pass' : 'fail') . "'>" . ($has_localize ? '✅' : '❌') . "</td></tr>";
    echo "<tr><td>Creates cpdAdmin object</td><td class='" . ($has_cpdadmin ? 'pass' : 'fail') . "'>" . ($has_cpdadmin ? '✅' : '❌') . "</td></tr>";
    
    $checks[] = array('check' => 'Admin class structure', 'pass' => $has_init && $has_menu && $has_scripts && $has_localize && $has_cpdadmin);
}

// Check JavaScript
$js_file = $base_dir . '/admin/js/admin-script.js';
if (file_exists($js_file)) {
    $content = file_get_contents($js_file);
    
    $has_jquery = strpos($content, 'jQuery') !== false;
    $has_form_handler = strpos($content, "#cpd-settings-form") !== false;
    $has_submit = strpos($content, ".on('submit'") !== false;
    $has_ajax = strpos($content, '$.ajax') !== false;
    $has_cpdadmin_check = strpos($content, 'typeof cpdAdmin') !== false;
    $has_logging = strpos($content, 'console.log') !== false;
    
    echo "<tr><td rowspan='6'><code>admin-script.js</code></td>";
    echo "<td>Uses jQuery</td><td class='" . ($has_jquery ? 'pass' : 'fail') . "'>" . ($has_jquery ? '✅' : '❌') . "</td></tr>";
    echo "<tr><td>Has form handler</td><td class='" . ($has_form_handler ? 'pass' : 'fail') . "'>" . ($has_form_handler ? '✅' : '❌') . "</td></tr>";
    echo "<tr><td>Has submit event</td><td class='" . ($has_submit ? 'pass' : 'fail') . "'>" . ($has_submit ? '✅' : '❌') . "</td></tr>";
    echo "<tr><td>Has AJAX call</td><td class='" . ($has_ajax ? 'pass' : 'fail') . "'>" . ($has_ajax ? '✅' : '❌') . "</td></tr>";
    echo "<tr><td>Checks for cpdAdmin</td><td class='" . ($has_cpdadmin_check ? 'pass' : 'fail') . "'>" . ($has_cpdadmin_check ? '✅' : '❌') . "</td></tr>";
    echo "<tr><td>Has console logging</td><td class='" . ($has_logging ? 'pass' : 'fail') . "'>" . ($has_logging ? '✅' : '❌') . "</td></tr>";
    
    $checks[] = array('check' => 'JavaScript structure', 'pass' => $has_jquery && $has_form_handler && $has_submit && $has_ajax);
}

echo "</table>";

// Summary
$total = count($checks);
$passed = count(array_filter($checks, function($c) { return $c['pass']; }));
$failed = $total - $passed;

echo "<h2>Summary</h2>";
echo "<table>";
echo "<tr><th>Total Checks</th><td>" . $total . "</td></tr>";
echo "<tr><th>Passed</th><td class='pass'>" . $passed . "</td></tr>";
echo "<tr><th>Failed</th><td class='" . ($failed > 0 ? 'fail' : 'pass') . "'>" . $failed . "</td></tr>";
echo "<tr><th>Success Rate</th><td>" . round(($passed / $total) * 100, 1) . "%</td></tr>";
echo "</table>";

if ($failed === 0) {
    echo "<div style='background:#4CAF50;color:white;padding:20px;margin:20px 0;border-radius:5px;'>";
    echo "<h2 style='margin:0;'>✅ ALL CHECKS PASSED!</h2>";
    echo "<p style='margin:10px 0 0 0;'>The admin panel has been successfully rebuilt. All files are in place and properly structured.</p>";
    echo "</div>";
    
    echo "<h3>Next Steps:</h3>";
    echo "<ol>";
    echo "<li>Clear your browser cache (Ctrl+Shift+Delete)</li>";
    echo "<li>Hard reload the page (Ctrl+Shift+R)</li>";
    echo "<li>Go to: WordPress Admin → Church Programme → Settings</li>";
    echo "<li>Open browser console (F12)</li>";
    echo "<li>Look for: <code>=== Admin Script Loaded Successfully ===</code></li>";
    echo "<li>Type: <code>console.log(cpdAdmin)</code></li>";
    echo "<li>Try saving a setting</li>";
    echo "</ol>";
} else {
    echo "<div style='background:#f44336;color:white;padding:20px;margin:20px 0;border-radius:5px;'>";
    echo "<h2 style='margin:0;'>❌ SOME CHECKS FAILED</h2>";
    echo "<p style='margin:10px 0 0 0;'>Please review the failed checks above and ensure all files are properly created.</p>";
    echo "</div>";
}

echo "<hr>";
echo "<p><small>Verification completed at: " . date('Y-m-d H:i:s') . "</small></p>";
echo "</body></html>";
?>

