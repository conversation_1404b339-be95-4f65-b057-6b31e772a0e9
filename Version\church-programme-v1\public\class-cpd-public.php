<?php
/**
 * Public Class
 * 
 * Handles public-facing functionality
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

class CPD_Public {
    
    /**
     * Initialize
     */
    public static function init() {
        add_action('wp_enqueue_scripts', array(__CLASS__, 'enqueue_scripts'));
    }
    
    /**
     * Enqueue public scripts
     */
    public static function enqueue_scripts() {
        // Only enqueue on our custom pages
        $cpd_page = get_query_var('cpd_page');
        
        if (!$cpd_page) {
            return;
        }
        
        // Enqueue Google Fonts (Open SIL License)
        wp_enqueue_style(
            'cpd-google-fonts',
            'https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@300;400;500;600;700&display=swap',
            array(),
            null
        );
        
        // Enqueue main styles
        wp_enqueue_style(
            'cpd-public-style',
            CPD_PLUGIN_URL . 'public/css/public-style.css',
            array(),
            CPD_VERSION
        );
        
        // Enqueue main scripts
        wp_enqueue_script(
            'cpd-public-script',
            CPD_PLUGIN_URL . 'public/js/public-script.js',
            array(),
            CPD_VERSION,
            true
        );
        
        // Localize script
        wp_localize_script('cpd-public-script', 'cpdPublic', array(
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'restUrl' => rest_url('cpd/v1/'),
            'nonce' => wp_create_nonce('wp_rest'),
        ));
    }
}

