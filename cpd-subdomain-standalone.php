<?php
/**
 * Standalone Subdomain Handler for Church Programme Dashboard
 * 
 * This file should be placed in the WordPress root directory and used with
 * server-level redirects (Apache .htaccess or Nginx configuration).
 * 
 * Usage:
 * 1. Place this file in your WordPress root directory
 * 2. Configure your web server to redirect subdomains to this file
 * 3. This file will handle the WordPress integration and template loading
 */

// WordPress root directory path
$wp_root = dirname(__FILE__);

// Load WordPress
if (!defined('ABSPATH')) {
    require_once($wp_root . '/wp-load.php');
}

/**
 * Handle subdomain requests
 */
function cpd_handle_subdomain_request() {
    // Get current host
    $current_host = isset($_SERVER['HTTP_HOST']) ? $_SERVER['HTTP_HOST'] : '';
    
    // Remove port if present
    $current_host = preg_replace('/:\d+$/', '', $current_host);
    
    // Determine which template to load based on subdomain
    if ($current_host === 'churchprogramme.jermesa.com' || 
        preg_match('/^churchprogramme\./i', $current_host)) {
        cpd_render_dashboard();
    } elseif ($current_host === 'churcheditor.jermesa.com' || 
              preg_match('/^churcheditor\./i', $current_host)) {
        cpd_render_editor();
    } else {
        // Not a recognized subdomain, redirect to main site
        wp_redirect(home_url());
        exit;
    }
}

/**
 * Render dashboard template
 */
function cpd_render_dashboard() {
    // Add CORS headers
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
    header('Access-Control-Allow-Headers: Content-Type, Authorization, X-WP-Nonce');
    header('Access-Control-Allow-Credentials: true');
    
    // Ensure plugin constants are defined
    if (!defined('CPD_PLUGIN_DIR')) {
        define('CPD_PLUGIN_DIR', WP_PLUGIN_DIR . '/church-programme-dashboard/');
    }
    if (!defined('CPD_PLUGIN_URL')) {
        define('CPD_PLUGIN_URL', plugins_url('/', WP_PLUGIN_DIR . '/church-programme-dashboard/church-programme-dashboard.php'));
    }
    if (!defined('CPD_VERSION')) {
        define('CPD_VERSION', '1.0.0');
    }
    
    // Check if plugin file exists
    $dashboard_file = WP_PLUGIN_DIR . '/church-programme-dashboard/public/templates/dashboard.php';
    
    if (file_exists($dashboard_file)) {
        // Include the dashboard template
        include $dashboard_file;
    } else {
        wp_die('Church Programme Dashboard plugin not found. Please ensure the plugin is installed and activated.');
    }
    exit;
}

/**
 * Render editor template
 */
function cpd_render_editor() {
    // Add CORS headers
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
    header('Access-Control-Allow-Headers: Content-Type, Authorization, X-WP-Nonce');
    header('Access-Control-Allow-Credentials: true');
    
    // Ensure plugin constants are defined
    if (!defined('CPD_PLUGIN_DIR')) {
        define('CPD_PLUGIN_DIR', WP_PLUGIN_DIR . '/church-programme-dashboard/');
    }
    if (!defined('CPD_PLUGIN_URL')) {
        define('CPD_PLUGIN_URL', plugins_url('/', WP_PLUGIN_DIR . '/church-programme-dashboard/church-programme-dashboard.php'));
    }
    if (!defined('CPD_VERSION')) {
        define('CPD_VERSION', '1.0.0');
    }
    
    // Check if plugin file exists
    $editor_file = WP_PLUGIN_DIR . '/church-programme-dashboard/public/templates/editor.php';
    
    if (file_exists($editor_file)) {
        // Include the editor template
        include $editor_file;
    } else {
        wp_die('Church Programme Dashboard plugin not found. Please ensure the plugin is installed and activated.');
    }
    exit;
}

// Handle the request
cpd_handle_subdomain_request();
?>
