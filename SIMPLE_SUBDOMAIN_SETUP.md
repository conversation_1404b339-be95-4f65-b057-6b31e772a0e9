# Simple Subdomain Setup for Church Programme Dashboard

This is the **simplest and most reliable** method to run your dashboard and editor on subdomains.

## Step 1: Upload Files

Upload these files to your **WordPress root directory** (same level as wp-config.php):
- `dashboard-subdomain.php`
- `editor-subdomain.php`

## Step 2: Configure Server Redirects

### For Apache (.htaccess)
Add these rules to your WordPress `.htaccess` file:

```apache
# Dashboard subdomain
RewriteCond %{HTTP_HOST} ^churchprogramme\.jermesa\.com$ [NC]
RewriteRule ^(.*)$ /dashboard-subdomain.php [L]

# Editor subdomain
RewriteCond %{HTTP_HOST} ^churcheditor\.jermesa\.com$ [NC]
RewriteRule ^(.*)$ /editor-subdomain.php [L]
```

### For Nginx
Add these server blocks to your Nginx configuration:

```nginx
# Dashboard subdomain
server {
    server_name churchprogramme.jermesa.com;
    root /path/to/your/wordpress;
    
    location / {
        try_files $uri $uri/ /dashboard-subdomain.php;
    }
    
    location ~ \.php$ {
        include fastcgi_params;
        fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
    }
}

# Editor subdomain
server {
    server_name churcheditor.jermesa.com;
    root /path/to/your/wordpress;
    
    location / {
        try_files $uri $uri/ /editor-subdomain.php;
    }
    
    location ~ \.php$ {
        include fastcgi_params;
        fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
    }
}
```

## Step 3: Remove Old Handler

**IMPORTANT:** Remove or disable the old must-use plugin:
- Delete `/wp-content/mu-plugins/cpd-subdomain-handler.php` if it exists

## Step 4: Test

Test the subdomains:
- **Dashboard:** https://churchprogramme.jermesa.com/
- **Editor:** https://churcheditor.jermesa.com/

## How This Works

1. **Direct WordPress Loading**: The files use `wp-load.php` to properly load WordPress
2. **Plugin Constants**: All plugin constants are defined before including templates
3. **REST API Access**: WordPress REST API functions work correctly
4. **Asset Loading**: CSS and JavaScript files load properly

## Troubleshooting

If you still see issues:

1. **Check WordPress is loaded**: The files should load WordPress without errors
2. **Verify plugin activation**: Ensure Church Programme Dashboard plugin is active
3. **Check file permissions**: Ensure PHP files are readable by web server
4. **Test REST API**: Visit `https://yourdomain.com/wp-json/cpd/v1/programmes/upcoming` to check if REST API works

## Benefits

- ✅ **Simple**: No complex WordPress plugin hooks
- ✅ **Reliable**: Direct WordPress loading works consistently
- ✅ **Clean URLs**: True subdomains, not WordPress paths
- ✅ **Full Functionality**: All features work including carousel and calendar
- ✅ **Easy Debugging**: Simple file structure makes troubleshooting easy

This approach bypasses all the WordPress plugin complexity and provides a clean, working subdomain solution.
