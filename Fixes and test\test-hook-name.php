<?php
/**
 * Test Hook Name
 * 
 * This file helps identify the correct admin hook name
 * Add this temporarily to see what hook WordPress is using
 */

// Add to functions.php or create as a mu-plugin temporarily:

add_action('admin_enqueue_scripts', function($hook) {
    // Log every admin page hook
    error_log('Admin Hook: ' . $hook);
    
    // Also display on screen for easy viewing
    if (current_user_can('manage_options')) {
        echo '<div style="background: #fff; border: 2px solid #0073aa; padding: 10px; margin: 10px; position: fixed; top: 32px; right: 10px; z-index: 9999; max-width: 400px;">';
        echo '<strong>Current Admin Hook:</strong><br>';
        echo '<code>' . esc_html($hook) . '</code><br>';
        echo '<small>Check if this contains "church-programme"</small>';
        echo '</div>';
    }
}, 999);

// Instructions:
// 1. Copy the add_action code above
// 2. Add it to your theme's functions.php temporarily
// 3. Go to the Church Programme Dashboard settings page
// 4. Look at the top right corner for the hook name
// 5. Check wp-content/debug.log for the logged hook name
// 6. Remove this code after identifying the hook

