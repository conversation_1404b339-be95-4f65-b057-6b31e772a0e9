# 🚨 CRITICAL FIX - Subdomain, Auth, and Settings Issues

## Date: 2025-09-30

---

## 🔍 ALL ISSUES IDENTIFIED:

### Issue 1: Dashboard Stuck at "Loading programmes..."
**Symptom:** Subdomain dashboard shows infinite loading spinner

**Root Cause:**
- Dashboard JavaScript expects flat array of programmes
- API returns programmes grouped by date: `{"2025-10-05": [...], "2025-10-15": [...]}`
- JavaScript can't process this format

**From dashboard-script.js line 50:**
```javascript
fetch(window.cpdData.restUrl + 'programmes/upcoming')
    .then(response => response.json())
    .then(data => {
        slides = processUpcomingProgrammes(data);  // ❌ Expects flat array
    })
```

**From class-cpd-rest-api.php line 208-224:**
```php
// Returns grouped by date
$formatted[$date][] = array(...);  // ❌ Wrong format
return rest_ensure_response($formatted);
```

### Issue 2: Editor 401 Unauthorized When WordPress Logged Out
**Symptom:** `GET /wp-json/cpd/v1/editor/programmes 401 (Unauthorized)`

**Root Cause:**
- Editor authentication uses PHP sessions (`$_SESSION`)
- Sessions are browser-specific and don't work across devices
- When WordPress admin logs out, session is destroyed
- Editor login should be INDEPENDENT of WordPress login

**From class-cpd-auth.php line 35:**
```php
public static function is_editor_logged_in() {
    return isset($_SESSION['cpd_editor_user_id']);  // ❌ Session-based
}
```

**The Problem:**
- Sessions don't work across different browsers
- Sessions don't work on different devices
- Sessions are destroyed when WordPress logs out
- Editor should work independently!

### Issue 3: Settings Not Reflecting on Dashboard
**Symptom:** Changes in admin panel don't appear on dashboard

**Root Cause:**
- Dashboard template loads settings directly from PHP `get_option()`
- PHP `get_option()` is heavily cached by WordPress
- Cache includes: Object cache, Transient cache, Opcode cache
- Settings ARE saving, but cache prevents them from showing

**From dashboard.php line 42:**
```php
<h1><?php echo esc_html(get_option('cpd_dashboard_title', 'Church Programme')); ?></h1>
```

**The Problem:**
- `get_option()` is cached
- Even after clearing browser cache, PHP cache remains
- Need to load settings via REST API (not cached)

---

## ✅ ALL FIXES:

### Fix 1: Flatten Upcoming Programmes Response

**Change in class-cpd-rest-api.php:**

**OLD (WRONG):**
```php
public static function get_upcoming_programmes($request) {
    $today = current_time('Y-m-d');
    $end_of_week = date('Y-m-d', strtotime($today . ' +7 days'));
    
    $programmes = CPD_Database::get_programmes_by_date_range($today, $end_of_week);
    
    // Format and organize by date
    $formatted = array();
    foreach ($programmes as $programme) {
        $date = $programme->programme_date;
        if (!isset($formatted[$date])) {
            $formatted[$date] = array();
        }
        
        $formatted[$date][] = array(
            'id' => $programme->id,
            'type' => $programme->programme_type,
            'date' => $programme->programme_date,
            'time' => $programme->programme_time,
            'data' => json_decode($programme->programme_data, true),
        );
    }
    
    return rest_ensure_response($formatted);  // ❌ Grouped by date
}
```

**NEW (CORRECT):**
```php
public static function get_upcoming_programmes($request) {
    $today = current_time('Y-m-d');
    $end_of_week = date('Y-m-d', strtotime($today . ' +7 days'));
    
    $programmes = CPD_Database::get_programmes_by_date_range($today, $end_of_week);
    
    // Format as flat array
    $formatted = array();
    foreach ($programmes as $programme) {
        $formatted[] = array(
            'id' => $programme->id,
            'type' => $programme->programme_type,
            'date' => $programme->programme_date,
            'time' => $programme->programme_time,
            'data' => json_decode($programme->programme_data, true),
        );
    }
    
    return rest_ensure_response($formatted);  // ✅ Flat array
}
```

### Fix 2: Token-Based Authentication for Editor

**Problem:** Sessions don't work across browsers/devices

**Solution:** Use WordPress REST API nonce + custom token system

**Change in class-cpd-rest-api.php:**

**Add new method to generate editor token:**
```php
public static function editor_login($request) {
    $username = $request->get_param('username');
    $password = $request->get_param('password');
    
    if (empty($username) || empty($password)) {
        return new WP_Error('missing_credentials', __('Username and password are required.'), array('status' => 400));
    }
    
    $result = CPD_Auth::login($username, $password);
    
    if (is_wp_error($result)) {
        return $result;
    }
    
    // Generate token for this session
    $user = CPD_Auth::get_current_editor_user();
    $token = wp_hash($user->username . time() . wp_rand());
    
    // Store token in database with expiration
    update_option('cpd_editor_token_' . $user->id, array(
        'token' => $token,
        'expires' => time() + (7 * DAY_IN_SECONDS),  // 7 days
    ));
    
    return rest_ensure_response(array(
        'success' => true,
        'message' => __('Login successful.'),
        'user' => $user,
        'token' => $token,  // ✅ Return token to client
    ));
}
```

**Change permission check:**
```php
public static function check_editor_permission() {
    // Allow WordPress admins
    if (current_user_can('manage_options')) {
        return true;
    }
    
    // Check for editor token in header
    $token = isset($_SERVER['HTTP_X_CPD_TOKEN']) ? $_SERVER['HTTP_X_CPD_TOKEN'] : '';
    
    if ($token) {
        // Validate token
        global $wpdb;
        $table = $wpdb->prefix . 'cpd_editor_users';
        $users = $wpdb->get_results("SELECT * FROM $table");
        
        foreach ($users as $user) {
            $stored = get_option('cpd_editor_token_' . $user->id);
            if ($stored && $stored['token'] === $token && $stored['expires'] > time()) {
                // Token valid
                $_SESSION['cpd_editor_user_id'] = $user->id;  // Set session for compatibility
                return true;
            }
        }
    }
    
    // Fallback to session-based auth
    return CPD_Auth::is_editor_logged_in();
}
```

**Change editor-script.js to send token:**
```javascript
// Store token after login
let editorToken = localStorage.getItem('cpd_editor_token');

// Send token with every request
fetch(url, {
    headers: {
        'X-WP-Nonce': window.cpdEditor.nonce,
        'X-CPD-Token': editorToken  // ✅ Send token
    }
})
```

### Fix 3: Load Settings via REST API (Not PHP Cache)

**Change dashboard.php to load settings dynamically:**

**OLD (CACHED):**
```php
<h1><?php echo esc_html(get_option('cpd_dashboard_title', 'Church Programme')); ?></h1>
```

**NEW (DYNAMIC):**
```php
<h1 id="cpd-dashboard-title">Church Programme</h1>

<script>
// Load settings from REST API (not cached)
fetch('<?php echo rest_url('cpd/v1/settings/public'); ?>')
    .then(response => response.json())
    .then(settings => {
        // Update title
        document.getElementById('cpd-dashboard-title').textContent = settings.dashboard_title || 'Church Programme';
        
        // Update colors
        document.documentElement.style.setProperty('--primary-color', settings.primary_color);
        document.documentElement.style.setProperty('--secondary-color', settings.secondary_color);
        
        // Show notice board if enabled
        if (settings.notice_board_enabled === '1') {
            showNoticeBoard(settings.notice_board_title, settings.notice_board_content);
        }
    });
</script>
```

---

## 📤 FILES TO MODIFY:

### 1. includes/class-cpd-rest-api.php
**Changes:**
- Fix `get_upcoming_programmes()` to return flat array
- Add token generation to `editor_login()`
- Update `check_editor_permission()` to validate tokens
- Add token cleanup method

### 2. public/js/editor-script.js
**Changes:**
- Store token in localStorage after login
- Send token with every API request
- Clear token on logout

### 3. public/templates/dashboard.php
**Changes:**
- Load settings via REST API instead of `get_option()`
- Update DOM dynamically with settings
- Show notice board dynamically

### 4. includes/class-cpd-ajax.php
**Changes:**
- Add cache busting after settings save
- Delete transients after save
- Flush object cache after save

---

## 🚀 IMPLEMENTATION PRIORITY:

### CRITICAL (Do First):
1. ✅ Fix `get_upcoming_programmes()` - Dashboard will work immediately
2. ✅ Add token-based auth - Editor will work independently

### IMPORTANT (Do Second):
3. ✅ Load settings via REST API - Settings will update immediately
4. ✅ Add cache busting to settings save

---

## 📊 SUMMARY:

**What was wrong:**
1. ❌ API returning grouped data instead of flat array
2. ❌ Session-based auth doesn't work across browsers/devices
3. ❌ Settings loaded from cached `get_option()`

**What needs to be fixed:**
1. ✅ Return flat array from `/programmes/upcoming`
2. ✅ Implement token-based authentication
3. ✅ Load settings via REST API
4. ✅ Add cache busting to settings save

**Expected results:**
1. ✅ Dashboard loads programmes immediately
2. ✅ Editor works on any device without WordPress login
3. ✅ Settings update immediately without cache issues

---

**This is a complex fix that requires modifying multiple files. I'll create the complete fixed files for you.**

