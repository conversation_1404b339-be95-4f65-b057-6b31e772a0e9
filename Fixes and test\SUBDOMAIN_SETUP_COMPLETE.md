# 🌐 Complete Subdomain Setup Guide

## Overview

This guide will help you set up TRUE subdomains for your Church Programme Dashboard:
- **Dashboard:** `churchprogramme.jermesa.com`
- **Editor:** `churcheditor.jermesa.com`

---

## ✅ Prerequisites

Before starting, ensure:
- [ ] DNS records are configured (A records pointing to your server)
- [ ] You have access to your web server configuration (cPanel, Apache, or Nginx)
- [ ] Plugin is installed and activated
- [ ] You're logged in as WordPress admin

---

## 📋 Step-by-Step Setup

### Step 1: Configure DNS Records

**In your DNS provider (e.g., Cloudflare, GoDaddy, Namecheap):**

1. **Add A Record for Dashboard:**
   - Type: `A`
   - Name: `churchprogramme`
   - Value: Your server IP address (same as jermesa.com)
   - TTL: Auto or 3600

2. **Add A Record for Editor:**
   - Type: `A`
   - Name: `churcheditor`
   - Value: Your server IP address (same as jermesa.com)
   - TTL: Auto or 3600

**Wait 5-15 minutes for DNS propagation.**

**Verify DNS:**
```bash
ping churchprogramme.jermesa.com
ping churcheditor.jermesa.com
```

Both should resolve to your server IP.

---

### Step 2: Install MU-Plugin Handler

**Option A: Using FTP/SFTP**

1. Connect to your server via FTP
2. Navigate to: `/public_html/wp-content/mu-plugins/`
3. If `mu-plugins` folder doesn't exist, create it
4. Upload `cpd-subdomain-handler.php` to this folder
5. Rename it if needed (keep the .php extension)

**Option B: Using cPanel File Manager**

1. Login to cPanel
2. Open File Manager
3. Navigate to: `public_html/wp-content/`
4. Create folder named `mu-plugins` if it doesn't exist
5. Enter the `mu-plugins` folder
6. Upload `cpd-subdomain-handler.php`

**Option C: Using SSH**

```bash
cd /path/to/wordpress/wp-content/
mkdir -p mu-plugins
cp /path/to/church-programme-dashboard/cpd-subdomain-handler.php mu-plugins/
```

**Verify Installation:**
- File should be at: `wp-content/mu-plugins/cpd-subdomain-handler.php`
- WordPress will automatically load it (no activation needed)

---

### Step 3: Configure Web Server

Choose your web server type:

#### Option A: Apache (.htaccess)

**If using cPanel or Apache:**

1. **Open .htaccess file** in your WordPress root directory
2. **Add these rules BEFORE the WordPress rules:**

```apache
# Church Programme Dashboard Subdomains
<IfModule mod_rewrite.c>
RewriteEngine On

# Dashboard Subdomain
RewriteCond %{HTTP_HOST} ^churchprogramme\.jermesa\.com$ [NC]
RewriteCond %{REQUEST_URI} !^/wp-content/plugins/church-programme-dashboard/
RewriteRule ^(.*)$ /index.php [L]

# Editor Subdomain
RewriteCond %{HTTP_HOST} ^churcheditor\.jermesa\.com$ [NC]
RewriteCond %{REQUEST_URI} !^/wp-content/plugins/church-programme-dashboard/
RewriteRule ^(.*)$ /index.php [L]
</IfModule>
```

3. **Save the file**

**Full .htaccess example:**

```apache
# BEGIN Church Programme Dashboard
<IfModule mod_rewrite.c>
RewriteEngine On

# Dashboard Subdomain
RewriteCond %{HTTP_HOST} ^churchprogramme\.jermesa\.com$ [NC]
RewriteCond %{REQUEST_URI} !^/wp-content/plugins/church-programme-dashboard/
RewriteRule ^(.*)$ /index.php [L]

# Editor Subdomain
RewriteCond %{HTTP_HOST} ^churcheditor\.jermesa\.com$ [NC]
RewriteCond %{REQUEST_URI} !^/wp-content/plugins/church-programme-dashboard/
RewriteRule ^(.*)$ /index.php [L]
</IfModule>
# END Church Programme Dashboard

# BEGIN WordPress
<IfModule mod_rewrite.c>
RewriteEngine On
RewriteRule .* - [E=HTTP_AUTHORIZATION:%{HTTP:Authorization}]
RewriteBase /
RewriteRule ^index\.php$ - [L]
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule . /index.php [L]
</IfModule>
# END WordPress
```

#### Option B: Nginx

**If using Nginx:**

1. **Edit your Nginx configuration file**
2. **Add these server blocks:**

```nginx
# Dashboard Subdomain
server {
    listen 80;
    listen [::]:80;
    server_name churchprogramme.jermesa.com;
    
    root /path/to/wordpress;
    index index.php;
    
    # CORS Headers
    add_header 'Access-Control-Allow-Origin' '*' always;
    add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
    add_header 'Access-Control-Allow-Headers' 'Content-Type, Authorization, X-WP-Nonce' always;
    
    location / {
        try_files $uri $uri/ /index.php?$args;
    }
    
    location ~ \.php$ {
        include fastcgi_params;
        fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
    }
    
    location ~ /\.ht {
        deny all;
    }
}

# Editor Subdomain
server {
    listen 80;
    listen [::]:80;
    server_name churcheditor.jermesa.com;
    
    root /path/to/wordpress;
    index index.php;
    
    # CORS Headers
    add_header 'Access-Control-Allow-Origin' '*' always;
    add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
    add_header 'Access-Control-Allow-Headers' 'Content-Type, Authorization, X-WP-Nonce' always;
    
    location / {
        try_files $uri $uri/ /index.php?$args;
    }
    
    location ~ \.php$ {
        include fastcgi_params;
        fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
    }
    
    location ~ /\.ht {
        deny all;
    }
}
```

3. **Test configuration:**
```bash
sudo nginx -t
```

4. **Reload Nginx:**
```bash
sudo systemctl reload nginx
```

#### Option C: cPanel (Subdomains)

**If using cPanel:**

1. **Login to cPanel**
2. **Go to:** Domains > Subdomains
3. **Create Dashboard Subdomain:**
   - Subdomain: `churchprogramme`
   - Domain: `jermesa.com`
   - Document Root: `/public_html` (same as main site)
   - Click "Create"

4. **Create Editor Subdomain:**
   - Subdomain: `churcheditor`
   - Domain: `jermesa.com`
   - Document Root: `/public_html` (same as main site)
   - Click "Create"

5. **Important:** Both subdomains should point to the SAME document root as your main WordPress installation

---

### Step 4: Test Subdomains

**Test Dashboard:**
1. Visit: `http://churchprogramme.jermesa.com`
2. Should show the dashboard page
3. Check browser console for errors

**Test Editor:**
1. Visit: `http://churcheditor.jermesa.com`
2. Should show the editor login page
3. Check browser console for errors

**If you see errors:**
- Check DNS propagation: `nslookup churchprogramme.jermesa.com`
- Check web server logs
- Verify MU-plugin is installed correctly
- Clear browser cache

---

### Step 5: Enable SSL (HTTPS)

**Option A: Using cPanel (AutoSSL)**

1. **Go to:** cPanel > Security > SSL/TLS Status
2. **Check boxes** for both subdomains
3. **Click:** "Run AutoSSL"
4. **Wait** for SSL certificates to be issued

**Option B: Using Let's Encrypt (Certbot)**

```bash
sudo certbot --nginx -d churchprogramme.jermesa.com -d churcheditor.jermesa.com
```

**Option C: Using Cloudflare**

1. **Login to Cloudflare**
2. **Go to:** SSL/TLS > Overview
3. **Set to:** Full or Full (Strict)
4. **Subdomains** will automatically get SSL

---

## ✅ Verification Checklist

After setup, verify:

- [ ] `churchprogramme.jermesa.com` loads dashboard
- [ ] `churcheditor.jermesa.com` loads editor
- [ ] No CORS errors in browser console
- [ ] Dashboard shows programmes correctly
- [ ] Editor login works
- [ ] SSL certificate is valid (https://)
- [ ] No mixed content warnings

---

## 🐛 Troubleshooting

### Issue 1: Subdomain shows main site

**Problem:** Subdomain loads the main WordPress site instead of dashboard/editor

**Solutions:**
1. Check MU-plugin is installed: `wp-content/mu-plugins/cpd-subdomain-handler.php`
2. Check web server configuration is correct
3. Clear WordPress cache
4. Flush permalinks: Settings > Permalinks > Save Changes

### Issue 2: 404 Not Found

**Problem:** Subdomain shows 404 error

**Solutions:**
1. Verify DNS is propagated: `ping churchprogramme.jermesa.com`
2. Check web server configuration
3. Verify document root is correct
4. Check file permissions

### Issue 3: CORS Errors

**Problem:** Browser console shows CORS errors

**Solutions:**
1. Verify MU-plugin is installed
2. Check web server CORS headers
3. Clear browser cache
4. Try incognito mode

### Issue 4: SSL Certificate Error

**Problem:** Browser shows "Not Secure" or certificate error

**Solutions:**
1. Wait for SSL certificate to be issued (can take 5-15 minutes)
2. Force SSL renewal in cPanel
3. Check Cloudflare SSL settings
4. Verify certificate covers subdomains

---

## 📊 Testing Tools

### Test DNS:
```bash
nslookup churchprogramme.jermesa.com
nslookup churcheditor.jermesa.com
```

### Test HTTP Response:
```bash
curl -I http://churchprogramme.jermesa.com
curl -I http://churcheditor.jermesa.com
```

### Test HTTPS:
```bash
curl -I https://churchprogramme.jermesa.com
curl -I https://churcheditor.jermesa.com
```

---

## 🎯 Summary

**What you did:**
1. ✅ Configured DNS A records
2. ✅ Installed MU-plugin handler
3. ✅ Configured web server
4. ✅ Enabled SSL certificates
5. ✅ Tested subdomains

**What works now:**
- ✅ Dashboard accessible at `churchprogramme.jermesa.com`
- ✅ Editor accessible at `churcheditor.jermesa.com`
- ✅ No CORS errors
- ✅ SSL enabled (https://)
- ✅ Fallback URLs still work (`/cpd-dashboard/` and `/cpd-editor/`)

---

## 🆘 Need Help?

If subdomains still don't work:

1. **Check DNS:** Use online DNS checker tools
2. **Check Web Server:** Review error logs
3. **Check MU-Plugin:** Verify file exists and has correct permissions
4. **Check WordPress:** Ensure plugin is activated
5. **Contact Host:** Some hosts require special subdomain configuration

**Provide this information when asking for help:**
- Hosting provider name
- Web server type (Apache/Nginx/cPanel)
- DNS provider
- Error messages from browser console
- Error messages from server logs

---

**Time Required:** 15-30 minutes  
**Difficulty:** Intermediate  
**Prerequisites:** DNS access, server access

