<?php
/**
 * Database Check and Repair Tool
 * 
 * Access this file to verify and repair database tables
 * URL: https://jermesa.com/wp-content/plugins/church-programme-dashboard/database-check.php
 */

// Load WordPress
$wp_load_path = dirname(dirname(dirname(dirname(__FILE__)))) . '/wp-load.php';
if (file_exists($wp_load_path)) {
    require_once($wp_load_path);
} else {
    die('WordPress not found. Please access this file through WordPress.');
}

// Security check - only admins can access
if (!current_user_can('manage_options')) {
    wp_die('You do not have permission to access this page.');
}

global $wpdb;

?>
<!DOCTYPE html>
<html>
<head>
    <title>Church Programme Dashboard - Database Check</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            max-width: 1000px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }
        .status {
            padding: 15px;
            margin: 15px 0;
            border-radius: 4px;
        }
        .success {
            background: #d4edda;
            border-left: 4px solid #28a745;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border-left: 4px solid #dc3545;
            color: #721c24;
        }
        .warning {
            background: #fff3cd;
            border-left: 4px solid #ffc107;
            color: #856404;
        }
        .info {
            background: #d1ecf1;
            border-left: 4px solid #17a2b8;
            color: #0c5460;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background: #f8f9fa;
            font-weight: 600;
        }
        .btn {
            display: inline-block;
            padding: 10px 20px;
            background: #3498db;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            border: none;
            cursor: pointer;
            font-size: 14px;
        }
        .btn:hover {
            background: #2980b9;
        }
        .btn-danger {
            background: #dc3545;
        }
        .btn-danger:hover {
            background: #c82333;
        }
        .btn-success {
            background: #28a745;
        }
        .btn-success:hover {
            background: #218838;
        }
        code {
            background: #f4f4f4;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🗄️ Church Programme Dashboard - Database Check</h1>
        
        <div class="status info">
            <strong>Check Time:</strong> <?php echo date('Y-m-d H:i:s'); ?><br>
            <strong>Database:</strong> <?php echo $wpdb->dbname; ?><br>
            <strong>Table Prefix:</strong> <?php echo $wpdb->prefix; ?>
        </div>
        
        <?php
        // Handle repair action
        if (isset($_GET['action']) && $_GET['action'] === 'repair' && isset($_GET['_wpnonce']) && wp_verify_nonce($_GET['_wpnonce'], 'cpd_repair_db')) {
            echo '<div class="status info"><strong>🔧 Repairing Database...</strong></div>';
            
            require_once(CPD_PLUGIN_DIR . 'includes/class-cpd-database.php');
            CPD_Database::create_tables();
            
            echo '<div class="status success"><strong>✅ Database repair completed!</strong> Refresh this page to see results.</div>';
            echo '<meta http-equiv="refresh" content="2">';
        }
        
        // Check tables
        $required_tables = array(
            'cpd_programmes' => 'Programme Data',
            'cpd_editor_users' => 'Editor Users',
            'cpd_ai_settings' => 'AI Settings',
            'cpd_extraction_history' => 'Extraction History',
        );
        
        $tables_status = array();
        $all_tables_exist = true;
        
        foreach ($required_tables as $table_suffix => $description) {
            $table_name = $wpdb->prefix . $table_suffix;
            $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") === $table_name;
            
            $row_count = 0;
            if ($table_exists) {
                $row_count = $wpdb->get_var("SELECT COUNT(*) FROM $table_name");
            } else {
                $all_tables_exist = false;
            }
            
            $tables_status[] = array(
                'name' => $table_name,
                'description' => $description,
                'exists' => $table_exists,
                'rows' => $row_count,
            );
        }
        ?>
        
        <h2>📊 Table Status</h2>
        
        <?php if ($all_tables_exist): ?>
            <div class="status success">
                <strong>✅ All Required Tables Exist</strong><br>
                All database tables are present.
            </div>
        <?php else: ?>
            <div class="status error">
                <strong>❌ Missing Tables Detected</strong><br>
                Some database tables are missing. Click the repair button below to create them.
            </div>
        <?php endif; ?>
        
        <table>
            <thead>
                <tr>
                    <th>Table Name</th>
                    <th>Description</th>
                    <th>Status</th>
                    <th>Row Count</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($tables_status as $table): ?>
                    <tr>
                        <td><code><?php echo esc_html($table['name']); ?></code></td>
                        <td><?php echo esc_html($table['description']); ?></td>
                        <td>
                            <?php if ($table['exists']): ?>
                                <span style="color: #28a745;">✅ Exists</span>
                            <?php else: ?>
                                <span style="color: #dc3545;">❌ Missing</span>
                            <?php endif; ?>
                        </td>
                        <td><?php echo $table['exists'] ? number_format($table['rows']) : 'N/A'; ?></td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
        
        <?php if (!$all_tables_exist): ?>
            <div style="margin: 20px 0;">
                <a href="?action=repair&_wpnonce=<?php echo wp_create_nonce('cpd_repair_db'); ?>" class="btn btn-danger">
                    🔧 Repair Database (Create Missing Tables)
                </a>
            </div>
        <?php endif; ?>
        
        <h2>🔍 Programme Data Check</h2>
        
        <?php
        $programmes_table = $wpdb->prefix . 'cpd_programmes';
        if ($wpdb->get_var("SHOW TABLES LIKE '$programmes_table'") === $programmes_table) {
            $total_programmes = $wpdb->get_var("SELECT COUNT(*) FROM $programmes_table");
            $programmes_by_type = $wpdb->get_results("
                SELECT programme_type, COUNT(*) as count 
                FROM $programmes_table 
                GROUP BY programme_type
            ");
            
            if ($total_programmes > 0) {
                echo '<div class="status success">';
                echo '<strong>✅ Programmes Found:</strong> ' . number_format($total_programmes) . ' total programmes';
                echo '</div>';
                
                echo '<table>';
                echo '<thead><tr><th>Programme Type</th><th>Count</th></tr></thead>';
                echo '<tbody>';
                foreach ($programmes_by_type as $row) {
                    echo '<tr>';
                    echo '<td>' . esc_html($row->programme_type) . '</td>';
                    echo '<td>' . number_format($row->count) . '</td>';
                    echo '</tr>';
                }
                echo '</tbody></table>';
                
                // Show recent programmes
                $recent_programmes = $wpdb->get_results("
                    SELECT * FROM $programmes_table 
                    ORDER BY created_at DESC 
                    LIMIT 5
                ");
                
                echo '<h3>Recent Programmes</h3>';
                echo '<table>';
                echo '<thead><tr><th>ID</th><th>Type</th><th>Date</th><th>Time</th><th>Created</th></tr></thead>';
                echo '<tbody>';
                foreach ($recent_programmes as $prog) {
                    echo '<tr>';
                    echo '<td>' . $prog->id . '</td>';
                    echo '<td>' . esc_html($prog->programme_type) . '</td>';
                    echo '<td>' . esc_html($prog->programme_date) . '</td>';
                    echo '<td>' . esc_html($prog->programme_time) . '</td>';
                    echo '<td>' . esc_html($prog->created_at) . '</td>';
                    echo '</tr>';
                }
                echo '</tbody></table>';
            } else {
                echo '<div class="status warning">';
                echo '<strong>⚠️ No Programmes Found</strong><br>';
                echo 'The programmes table exists but contains no data. Try adding a programme from the admin panel or editor.';
                echo '</div>';
            }
        } else {
            echo '<div class="status error">';
            echo '<strong>❌ Programmes Table Missing</strong><br>';
            echo 'Click the repair button above to create the table.';
            echo '</div>';
        }
        ?>
        
        <h2>👥 Editor Users Check</h2>
        
        <?php
        $users_table = $wpdb->prefix . 'cpd_editor_users';
        if ($wpdb->get_var("SHOW TABLES LIKE '$users_table'") === $users_table) {
            $total_users = $wpdb->get_var("SELECT COUNT(*) FROM $users_table");
            
            if ($total_users > 0) {
                echo '<div class="status success">';
                echo '<strong>✅ Editor Users Found:</strong> ' . number_format($total_users) . ' users';
                echo '</div>';
                
                $users = $wpdb->get_results("SELECT id, username, email, status, created_at, last_login FROM $users_table");
                
                echo '<table>';
                echo '<thead><tr><th>ID</th><th>Username</th><th>Email</th><th>Status</th><th>Last Login</th></tr></thead>';
                echo '<tbody>';
                foreach ($users as $user) {
                    echo '<tr>';
                    echo '<td>' . $user->id . '</td>';
                    echo '<td>' . esc_html($user->username) . '</td>';
                    echo '<td>' . esc_html($user->email) . '</td>';
                    echo '<td>' . esc_html($user->status) . '</td>';
                    echo '<td>' . ($user->last_login ? esc_html($user->last_login) : 'Never') . '</td>';
                    echo '</tr>';
                }
                echo '</tbody></table>';
            } else {
                echo '<div class="status warning">';
                echo '<strong>⚠️ No Editor Users Found</strong><br>';
                echo 'Create editor users from: <a href="' . admin_url('admin.php?page=cpd-editor-users') . '">Church Programme > Editor Users</a>';
                echo '</div>';
            }
        } else {
            echo '<div class="status error">';
            echo '<strong>❌ Editor Users Table Missing</strong><br>';
            echo 'Click the repair button above to create the table.';
            echo '</div>';
        }
        ?>
        
        <h2>🔧 Quick Actions</h2>
        
        <div style="margin: 20px 0;">
            <a href="<?php echo admin_url('admin.php?page=church-programme-dashboard'); ?>" class="btn btn-success">
                ⚙️ Go to Settings
            </a>
            <a href="<?php echo admin_url('admin.php?page=cpd-editor-users'); ?>" class="btn btn-success">
                👥 Manage Editor Users
            </a>
            <a href="<?php echo home_url('/cpd-dashboard/'); ?>" class="btn" target="_blank">
                📅 View Dashboard
            </a>
            <a href="<?php echo home_url('/cpd-editor/'); ?>" class="btn" target="_blank">
                ✏️ View Editor
            </a>
        </div>
        
        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; color: #666; font-size: 0.9em;">
            <strong>Plugin Version:</strong> 1.0.0<br>
            <strong>WordPress Version:</strong> <?php echo get_bloginfo('version'); ?><br>
            <strong>PHP Version:</strong> <?php echo PHP_VERSION; ?><br>
            <strong>MySQL Version:</strong> <?php echo $wpdb->db_version(); ?>
        </div>
    </div>
</body>
</html>

