<?php
/**
 * Direct Notice Board Settings Updater - Fixed Version
 * 
 * Use this script to directly update Notice Board settings in the database
 * with a custom rich text editor that works outside WordPress admin.
 * 
 * Instructions:
 * 1. Upload this file to your WordPress root directory (same level as wp-config.php)
 * 2. Access it via browser: https://yoursite.com/update-notice-board-settings-fixed.php
 * 3. Configure your notice board settings and click "Update Settings"
 * 4. Delete this file after use for security
 */

// Security check
if (!defined('ABSPATH')) {
    // Load WordPress
    require_once('wp-load.php');
}

// Check if user is logged in and has admin permissions
if (!current_user_can('manage_options')) {
    die('You do not have permission to access this page.');
}

$message = '';
$current_settings = array(
    'enabled' => get_option('cpd_notice_board_enabled', '0'),
    'title' => get_option('cpd_notice_board_title', 'Notice Board'),
    'content' => get_option('cpd_notice_board_content', '')
);

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_settings'])) {
    $new_settings = array(
        'enabled' => isset($_POST['enabled']) ? '1' : '0',
        'title' => sanitize_text_field($_POST['title']),
        'content' => wp_kses_post($_POST['content'])
    );
    
    // Update all settings
    update_option('cpd_notice_board_enabled', $new_settings['enabled']);
    update_option('cpd_notice_board_title', $new_settings['title']);
    update_option('cpd_notice_board_content', $new_settings['content']);
    
    $current_settings = $new_settings;
    $message = "✅ Success! Notice Board settings have been updated.";
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Update Notice Board Settings</title>
    <!-- Include jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Include TinyMCE -->
    <script src="https://cdn.tiny.cloud/1/no-api-key/tinymce/6/tinymce.min.js" referrerpolicy="origin"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f0f0f1;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #1d2327;
            margin-bottom: 20px;
        }
        .info-box {
            background: #f6f7f7;
            border-left: 4px solid #72aee6;
            padding: 20px;
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 25px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            font-size: 14px;
        }
        input[type="text"], select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #8c8f94;
            border-radius: 4px;
            font-size: 16px;
        }
        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .checkbox-group input[type="checkbox"] {
            width: auto;
        }
        button {
            background: #2271b1;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        button:hover {
            background: #135e96;
        }
        .message {
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .success {
            background: #d1e7dd;
            border: 1px solid #badbcc;
            color: #0f5132;
        }
        .current-settings {
            background: #fff3cd;
            border-left: 4px solid #ffc107;
            padding: 15px;
            margin-bottom: 20px;
        }
        .editor-container {
            border: 1px solid #c3c4c7;
            border-radius: 4px;
            overflow: hidden;
        }
        .setting-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 15px;
        }
        @media (max-width: 768px) {
            .setting-row {
                grid-template-columns: 1fr;
            }
        }
        .preview-box {
            background: #f9f9f9;
            border: 1px solid #e2e8f0;
            border-radius: 4px;
            padding: 15px;
            margin-top: 10px;
        }
        .preview-box h4 {
            margin-top: 0;
            color: #4a5568;
        }
        .editor-toolbar {
            background: #f6f7f7;
            padding: 10px;
            border-bottom: 1px solid #c3c4c7;
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
        }
        .editor-toolbar button {
            background: #fff;
            color: #2c3338;
            border: 1px solid #c3c4c7;
            padding: 6px 12px;
            font-size: 14px;
            margin-right: 0;
        }
        .editor-toolbar button:hover {
            background: #f6f7f7;
        }
        .color-picker {
            position: relative;
            display: inline-block;
        }
        .color-picker input[type="color"] {
            position: absolute;
            opacity: 0;
            width: 100%;
            height: 100%;
            cursor: pointer;
        }
        .color-picker label {
            display: inline-block;
            padding: 6px 12px;
            background: #fff;
            border: 1px solid #c3c4c7;
            border-radius: 4px;
            cursor: pointer;
            margin: 0;
            font-size: 14px;
        }
        .color-picker label:hover {
            background: #f6f7f7;
        }
        #notice_board_content {
            width: 100%;
            min-height: 300px;
            padding: 15px;
            border: none;
            font-family: monospace;
            font-size: 14px;
            line-height: 1.5;
            resize: vertical;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Update Notice Board Settings</h1>
        
        <div class="info-box">
            <h3>Current Settings:</h3>
            <div class="setting-row">
                <div><strong>Enabled:</strong> <?php echo $current_settings['enabled'] === '1' ? 'Yes' : 'No'; ?></div>
                <div><strong>Title:</strong> <?php echo esc_html($current_settings['title']); ?></div>
            </div>
            <?php if ($current_settings['enabled'] === '1' && !empty($current_settings['content'])): ?>
                <div style="margin-top: 15px;">
                    <strong>Current Content Preview:</strong>
                    <div class="preview-box">
                        <?php echo wp_kses_post(wpautop($current_settings['content'])); ?>
                    </div>
                </div>
            <?php endif; ?>
        </div>

        <?php if ($message): ?>
            <div class="message success">
                <?php echo esc_html($message); ?>
            </div>
        <?php endif; ?>

        <form method="post">
            <div class="setting-row">
                <div class="form-group">
                    <div class="checkbox-group">
                        <input type="checkbox" id="enabled" name="enabled" value="1" <?php checked($current_settings['enabled'], '1'); ?>>
                        <label for="enabled" style="display: inline; margin-bottom: 0;">Enable Notice Board</label>
                    </div>
                    <p style="margin-top: 5px; color: #666; font-size: 14px;">
                        Show the notice board on the dashboard
                    </p>
                </div>
                
                <div class="form-group">
                    <label for="title">Notice Board Title:</label>
                    <input type="text" id="title" name="title" 
                           value="<?php echo esc_attr($current_settings['title']); ?>" 
                           placeholder="Enter notice board title">
                    <p style="margin-top: 5px; color: #666; font-size: 14px;">
                        The title displayed above the notice board
                    </p>
                </div>
            </div>
            
            <div class="form-group">
                <label for="notice_board_content">Notice Board Content:</label>
                <div class="editor-container">
                    <div class="editor-toolbar" id="editor-toolbar">
                        <button type="button" onclick="formatText('bold')" title="Bold"><strong>B</strong></button>
                        <button type="button" onclick="formatText('italic')" title="Italic"><em>I</em></button>
                        <button type="button" onclick="formatText('underline')" title="Underline"><u>U</u></button>
                        <div class="color-picker">
                            <input type="color" id="text-color" onchange="changeTextColor(this.value)" title="Text Color">
                            <label for="text-color">🎨</label>
                        </div>
                        <button type="button" onclick="insertList('ul')" title="Bullet List">• List</button>
                        <button type="button" onclick="insertList('ol')" title="Numbered List">1. List</button>
                        <button type="button" onclick="insertLink()" title="Insert Link">🔗 Link</button>
                        <button type="button" onclick="insertImage()" title="Insert Image">🖼️ Image</button>
                        <button type="button" onclick="toggleView()" title="Toggle HTML/Visual" id="toggle-view">HTML</button>
                    </div>
                    <textarea id="notice_board_content" name="content" placeholder="Enter your notice board content here..."><?php echo esc_textarea($current_settings['content']); ?></textarea>
                </div>
                <p style="margin-top: 5px; color: #666; font-size: 14px;">
                    Use the toolbar to format your content. You can add bold, italic, lists, links, and images.
                </p>
            </div>
            
            <div style="margin-top: 30px;">
                <h3>Notice Board Features:</h3>
                <div class="current-settings">
                    <ul>
                        <li><strong>Rich Text Formatting:</strong> Bold, italic, underline, lists</li>
                        <li><strong>Links:</strong> Insert hyperlinks to external websites</li>
                        <li><strong>Images:</strong> Add inline images using image URLs</li>
                        <li><strong>HTML Support:</strong> Switch between visual and HTML view</li>
                        <li><strong>Live Preview:</strong> See how your notice will look on the dashboard</li>
                    </ul>
                </div>
            </div>
            
            <div style="margin-top: 30px;">
                <button type="submit" name="update_settings">Update Settings</button>
                <a href="<?php echo CPD_Subdomain::get_dashboard_url(); ?>" target="_blank" style="text-decoration: none;">
                    <button type="button">View Dashboard</button>
                </a>
            </div>
        </form>

        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd;">
            <p><strong>Security Note:</strong> Please delete this file after updating the settings.</p>
            <p><strong>Preview:</strong> After updating, visit your dashboard to see the notice board with your new content.</p>
        </div>
    </div>

    <script>
    // Text formatting functions
    function formatText(type) {
        const textarea = document.getElementById('notice_board_content');
        const start = textarea.selectionStart;
        const end = textarea.selectionEnd;
        const selectedText = textarea.value.substring(start, end);
        
        let formattedText = '';
        
        switch(type) {
            case 'bold':
                formattedText = `<strong>${selectedText}</strong>`;
                break;
            case 'italic':
                formattedText = `<em>${selectedText}</em>`;
                break;
            case 'underline':
                formattedText = `<u>${selectedText}</u>`;
                break;
        }
        
        textarea.value = textarea.value.substring(0, start) + formattedText + textarea.value.substring(end);
        textarea.focus();
        textarea.setSelectionRange(start + formattedText.length, start + formattedText.length);
    }
    
    function insertList(type) {
        const textarea = document.getElementById('notice_board_content');
        const start = textarea.selectionStart;
        const end = textarea.selectionEnd;
        
        let listText = '';
        if (type === 'ul') {
            listText = '\n• List item 1\n• List item 2\n• List item 3\n';
        } else {
            listText = '\n1. List item 1\n2. List item 2\n3. List item 3\n';
        }
        
        textarea.value = textarea.value.substring(0, start) + listText + textarea.value.substring(end);
        textarea.focus();
        textarea.setSelectionRange(start + listText.length, start + listText.length);
    }
    
    function insertLink() {
        const url = prompt('Enter URL:');
        if (url) {
            const text = prompt('Enter link text (optional):', url);
            const linkText = text || url;
            const link = `<a href="${url}" target="_blank">${linkText}</a>`;
            
            const textarea = document.getElementById('notice_board_content');
            const start = textarea.selectionStart;
            const end = textarea.selectionEnd;
            
            textarea.value = textarea.value.substring(0, start) + link + textarea.value.substring(end);
            textarea.focus();
            textarea.setSelectionRange(start + link.length, start + link.length);
        }
    }
    
    function insertImage() {
        const url = prompt('Enter image URL:');
        if (url) {
            const alt = prompt('Enter alt text (optional):', '');
            const altText = alt ? ` alt="${alt}"` : '';
            const img = `<img src="${url}"${altText} style="max-width: 100%; height: auto;">`;
            
            const textarea = document.getElementById('notice_board_content');
            const start = textarea.selectionStart;
            const end = textarea.selectionEnd;
            
            textarea.value = textarea.value.substring(0, start) + img + textarea.value.substring(end);
            textarea.focus();
            textarea.setSelectionRange(start + img.length, start + img.length);
        }
    }
    
    function changeTextColor(color) {
        const textarea = document.getElementById('notice_board_content');
        const start = textarea.selectionStart;
        const end = textarea.selectionEnd;
        const selectedText = textarea.value.substring(start, end);
        
        if (selectedText) {
            const coloredText = `<span style="color: ${color};">${selectedText}</span>`;
            textarea.value = textarea.value.substring(0, start) + coloredText + textarea.value.substring(end);
            textarea.focus();
            textarea.setSelectionRange(start + coloredText.length, start + coloredText.length);
        }
    }
    
    function toggleView() {
        const textarea = document.getElementById('notice_board_content');
        const toggleBtn = document.getElementById('toggle-view');
        
        if (toggleBtn.textContent === 'HTML') {
            // Convert to visual preview
            let content = textarea.value;
            content = content.replace(/<strong>(.*?)<\/strong>/g, '**$1**');
            content = content.replace(/<em>(.*?)<\/em>/g, '*$1*');
            content = content.replace(/<u>(.*?)<\/u>/g, '_$1_');
            content = content.replace(/<span style="color: (.*?);">(.*?)<\/span>/g, '[$2]($1)');
            content = content.replace(/<a href="(.*?)".*?>(.*?)<\/a>/g, '[$2]($1)');
            content = content.replace(/<img src="(.*?)".*?>/g, '![Image]($1)');
            content = content.replace(/<br\s*\/?>/g, '\n');
            content = content.replace(/<p>(.*?)<\/p>/g, '$1\n\n');
            content = content.replace(/<ul>(.*?)<\/ul>/gs, '$1');
            content = content.replace(/<ol>(.*?)<\/ol>/gs, '$1');
            content = content.replace(/<li>(.*?)<\/li>/g, '• $1\n');
            
            textarea.value = content;
            toggleBtn.textContent = 'Visual';
        } else {
            // Convert back to HTML
            let content = textarea.value;
            content = content.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
            content = content.replace(/\*(.*?)\*/g, '<em>$1</em>');
            content = content.replace(/_(.*?)_/g, '<u>$1</u>');
            content = content.replace(/\[(.*?)\]\((.*?)\)/g, function(match, text, url) {
                // Check if it's a color or a link
                if (url.startsWith('#')) {
                    return `<span style="color: ${url};">${text}</span>`;
                } else {
                    return `<a href="${url}" target="_blank">${text}</a>`;
                }
            });
            content = content.replace(/!\[(.*?)\]\((.*?)\)/g, '<img src="$2" alt="$1" style="max-width: 100%; height: auto;">');
            content = content.replace(/\n/g, '<br>');
            content = content.replace(/(•\s.*?)(?=\n|$)/g, '<li>$1</li>');
            
            textarea.value = content;
            toggleBtn.textContent = 'HTML';
        }
    }
    
    // Auto-resize textarea
    document.addEventListener('DOMContentLoaded', function() {
        const textarea = document.getElementById('notice_board_content');
        textarea.style.minHeight = '300px';
        
        // Show/hide content preview based on enabled state
        document.getElementById('enabled').addEventListener('change', function() {
            const isEnabled = this.checked;
            if (!isEnabled) {
                const previewBox = document.querySelector('.preview-box');
                if (previewBox) previewBox.style.display = 'none';
            }
        });
    });
    </script>
</body>
</html>
