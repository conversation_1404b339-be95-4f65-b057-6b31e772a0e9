<?php
/**
 * Subdomain Handler for Church Programme Dashboard
 * 
 * This file should be copied to wp-content/mu-plugins/ for proper subdomain handling
 * 
 * Instructions:
 * 1. Copy this file to: wp-content/mu-plugins/cpd-subdomain-handler.php
 * 2. Configure your DNS to point subdomains to your server
 * 3. Configure your web server (Apache/Nginx) to handle subdomains
 * 
 * For Apache (.htaccess):
 * RewriteCond %{HTTP_HOST} ^churchprogramme\.jermesa\.com$ [NC]
 * RewriteRule ^(.*)$ /wp-content/plugins/church-programme-dashboard/public/templates/dashboard.php [L]
 * 
 * RewriteCond %{HTTP_HOST} ^churcheditor\.jermesa\.com$ [NC]
 * RewriteRule ^(.*)$ /wp-content/plugins/church-programme-dashboard/public/templates/editor.php [L]
 * 
 * For Nginx:
 * server {
 *     server_name churchprogramme.jermesa.com;
 *     root /path/to/wordpress;
 *     
 *     location / {
 *         try_files $uri $uri/ /wp-content/plugins/church-programme-dashboard/public/templates/dashboard.php;
 *     }
 * }
 * 
 * server {
 *     server_name churcheditor.jermesa.com;
 *     root /path/to/wordpress;
 *     
 *     location / {
 *         try_files $uri $uri/ /wp-content/plugins/church-programme-dashboard/public/templates/editor.php;
 *     }
 * }
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Handle subdomain routing
 */
function cpd_handle_subdomain_routing() {
    // Get current host
    $current_host = isset($_SERVER['HTTP_HOST']) ? $_SERVER['HTTP_HOST'] : '';
    
    // Remove port if present
    $current_host = preg_replace('/:\d+$/', '', $current_host);
    
    // Get site URL
    $site_url = get_site_url();
    $site_host = parse_url($site_url, PHP_URL_HOST);
    
    // Check if we're on a subdomain
    if ($current_host === $site_host) {
        return; // Not a subdomain, continue normal WordPress flow
    }
    
    // Check for dashboard subdomain
    if ($current_host === 'churchprogramme.jermesa.com' || 
        preg_match('/^churchprogramme\./i', $current_host)) {
        cpd_render_dashboard_subdomain();
        exit;
    }
    
    // Check for editor subdomain
    if ($current_host === 'churcheditor.jermesa.com' || 
        preg_match('/^churcheditor\./i', $current_host)) {
        cpd_render_editor_subdomain();
        exit;
    }
}

/**
 * Render dashboard on subdomain
 */
function cpd_render_dashboard_subdomain() {
    // Add CORS headers
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
    header('Access-Control-Allow-Headers: Content-Type, Authorization, X-WP-Nonce');
    header('Access-Control-Allow-Credentials: true');
    
    // Prevent theme from loading
    add_filter('template_include', '__return_false');
    add_filter('show_admin_bar', '__return_false');
    
    // Remove all theme actions
    remove_all_actions('wp_head');
    remove_all_actions('wp_footer');
    remove_all_actions('wp_enqueue_scripts');
    
    // Re-add essential WordPress actions
    add_action('wp_head', 'wp_enqueue_scripts', 1);
    add_action('wp_head', 'wp_print_styles', 8);
    add_action('wp_head', 'wp_print_head_scripts', 9);
    add_action('wp_footer', 'wp_print_footer_scripts', 20);
    
    // Ensure plugin constants are defined
    if (!defined('CPD_PLUGIN_DIR')) {
        define('CPD_PLUGIN_DIR', WP_PLUGIN_DIR . '/church-programme-dashboard/');
    }
    if (!defined('CPD_PLUGIN_URL')) {
        define('CPD_PLUGIN_URL', plugins_url('/', WP_PLUGIN_DIR . '/church-programme-dashboard/church-programme-dashboard.php'));
    }
    if (!defined('CPD_VERSION')) {
        define('CPD_VERSION', '1.0.0');
    }
    
    // Check if plugin file exists
    $dashboard_file = WP_PLUGIN_DIR . '/church-programme-dashboard/public/templates/dashboard.php';
    
    if (file_exists($dashboard_file)) {
        // Include the dashboard template
        include $dashboard_file;
    } else {
        wp_die('Church Programme Dashboard plugin not found. Please ensure the plugin is installed and activated.');
    }
}

/**
 * Render editor on subdomain
 */
function cpd_render_editor_subdomain() {
    // Add CORS headers
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
    header('Access-Control-Allow-Headers: Content-Type, Authorization, X-WP-Nonce');
    header('Access-Control-Allow-Credentials: true');
    
    // Prevent theme from loading
    add_filter('template_include', '__return_false');
    add_filter('show_admin_bar', '__return_false');
    
    // Remove all theme actions
    remove_all_actions('wp_head');
    remove_all_actions('wp_footer');
    remove_all_actions('wp_enqueue_scripts');
    
    // Re-add essential WordPress actions
    add_action('wp_head', 'wp_enqueue_scripts', 1);
    add_action('wp_head', 'wp_print_styles', 8);
    add_action('wp_head', 'wp_print_head_scripts', 9);
    add_action('wp_footer', 'wp_print_footer_scripts', 20);
    
    // Ensure plugin constants are defined
    if (!defined('CPD_PLUGIN_DIR')) {
        define('CPD_PLUGIN_DIR', WP_PLUGIN_DIR . '/church-programme-dashboard/');
    }
    if (!defined('CPD_PLUGIN_URL')) {
        define('CPD_PLUGIN_URL', plugins_url('/', WP_PLUGIN_DIR . '/church-programme-dashboard/church-programme-dashboard.php'));
    }
    if (!defined('CPD_VERSION')) {
        define('CPD_VERSION', '1.0.0');
    }
    
    // Check if plugin file exists
    $editor_file = WP_PLUGIN_DIR . '/church-programme-dashboard/public/templates/editor.php';
    
    if (file_exists($editor_file)) {
        // Include the editor template
        include $editor_file;
    } else {
        wp_die('Church Programme Dashboard plugin not found. Please ensure the plugin is installed and activated.');
    }
}

// Hook into WordPress early
add_action('muplugins_loaded', 'cpd_handle_subdomain_routing', 1);
