# Church Programme Dashboard

A beautiful, mobile-first WordPress plugin for displaying church programmes with AI-powered data extraction from images.

## Features

### 📱 Mobile-First Dashboard
- Beautiful, modern UI optimized for mobile devices
- Minimalistic light theme design
- Responsive carousel slider showing upcoming programmes
- Interactive calendar view with programme markers
- Modal popups for detailed programme information

### 🤖 AI-Powered Data Extraction
- Support for multiple AI providers:
  - OpenRouter
  - Google Gemini
  - DeepSeek
- Automatic extraction of programme data from images
- Custom prompts for each programme type
- Secure API key storage in browser localStorage

### 🔐 Secure Editor Access
- Separate editor page with login authentication
- Admin-managed user access
- Session-based authentication
- Manual data entry and editing capabilities

### 📅 Four Programme Types
1. **JINGIASENG RANGBAH** - Two programmes (1:00 PM & 6:30 PM)
2. **JINGIASENG IING** - Two zones (6:30 PM)
3. **JINGIASENG SAMLA** - Five columns (6:30 PM)
4. **JINGIASENG KHYNNAH** - Eight columns (3:00 PM)

### ⚙️ Comprehensive Admin Panel
- Customizable colors and styling
- Editable programme labels and times
- Notice board management
- User access management
- Custom subdomain/path configuration
- Cookie consent settings

### 🎨 Design Features
- SVG icons (no external icon libraries)
- Google Fonts with Open SIL License (Inter & Poppins)
- Fully customizable colors and backgrounds
- Smooth animations and transitions

## Installation

1. Download the plugin folder `church-programme-dashboard`
2. Upload to your WordPress `wp-content/plugins/` directory
3. Activate the plugin through the WordPress admin panel
4. Go to **Church Programme** > **Settings** to configure

**Important:** This plugin uses **subdomain-based URLs** (e.g., `churchprogramme.yoursite.com`), not path-based URLs. You must configure DNS records for your subdomains. See **SUBDOMAIN_SETUP.md** for detailed instructions.

## Initial Setup

### 1. Configure General Settings
- Set your dashboard title
- Configure custom path name (e.g., `programme`)
- Set header background color or image
- Your dashboard will be accessible at: `yoursite.com/programme/`

### 2. Create Editor Users
- Go to **Church Programme** > **Editor Users**
- Add usernames and passwords for people who can edit programmes
- Only WordPress admins can create editor users

### 3. Configure AI (Optional)
- Access the editor page at: `yoursite.com/programme/editor/`
- Login with editor credentials
- Go to **AI Extraction** tab
- Select AI provider and enter API key
- Fetch available models
- Select a vision-capable model

### 4. Add Programme Data

#### Option A: AI Extraction
1. Upload programme images
2. Click "Extract Data" for each programme type
3. Review extracted data
4. Data is automatically saved to database

#### Option B: Manual Entry
1. Go to **Manual Entry** tab
2. Select programme type
3. Enter date, time, and participant details
4. Click "Save Programme"

## Programme Types & Data Structure

### JINGIASENG RANGBAH
Contains two programmes:
- **JINGIASENG 1:00 Baje** (1:00 PM)
  - NONGIATHUH KHANA POR 1:00PM
- **MIET BALANG** (6:30 PM)
  - PULE SDANG & DUWAI
  - NONGKREN
  - KHUBOR

### JINGIASENG IING
Two zones (6:30 PM):
- **ZONE-1**
  - ING
  - NONGPULE & DUWAI
  - NONGKREN
- **ZONE-2**
  - ING
  - NONGPULE & DUWAI
  - NONGKREN

### JINGIASENG SAMLA
Five columns (6:30 PM):
- PULESDANG & DUWAI
- JINGAINGUH
- SPECIAL NO.
- NONGKREN
- Special items (when applicable)

### JINGIASENG KHYNNAH
Eight columns (3:00 PM):
- JINGRWAI IAROH
- NONGPULE SDANG (OLD TESTAMENT & NEW TESTAMENT)
- NONG DUWAI
- LUM JINGAINGUH
- JINGRWAI KYRPANG
- DUWAI JINGAINGUH
- NONGKREN/ACTIVITIES

## AI Provider Setup

### OpenRouter
1. Sign up at https://openrouter.ai
2. Get API key from dashboard
3. Recommended models: GPT-4 Vision, Claude 3 Opus/Sonnet

### Google Gemini
1. Get API key from https://makersuite.google.com/app/apikey
2. Recommended models: Gemini Pro Vision, Gemini 1.5 Pro

### DeepSeek
1. Sign up at https://platform.deepseek.com
2. Get API key from dashboard
3. Recommended models: DeepSeek VL

## Customization

### Colors
Go to **Settings** > **Colors & Styling**:
- Primary Color
- Secondary Color
- Accent Color
- Text Color

### Programme Labels
Go to **Settings** > **Programme Labels**:
- Customize display names for each programme type
- Edit default times

### Notice Board
Go to **Settings** > **Notice Board**:
- Enable/disable notice board
- Set title and content
- Supports rich text formatting

### Cookie Consent
Go to **Settings** > **Cookie Consent**:
- Customize consent message
- Edit button text
- Functional cookie consent with localStorage

## Dashboard Features

### Carousel Slider
- Shows 3 upcoming programmes
- Auto-advances every 5 seconds
- Manual navigation with arrows
- Indicator dots for slide position
- Large background text for visual appeal

### Calendar View
- Interactive monthly calendar
- Dates with programmes are highlighted
- Click any date to view programme details
- Navigate between months
- Modal popup with full programme information

### Notice Board
- Optional section between carousel and calendar
- Displays important announcements
- Can be enabled/disabled from admin panel

## Security Features

- API keys stored in browser localStorage only
- Never sent to WordPress server
- Session-based editor authentication
- WordPress nonce verification for AJAX requests
- Prepared SQL statements for database queries
- Input sanitization and validation

## Browser Compatibility

- Chrome/Edge (latest)
- Firefox (latest)
- Safari (latest)
- Mobile browsers (iOS Safari, Chrome Mobile)

## Requirements

- WordPress 5.8 or higher
- PHP 7.4 or higher
- Modern web browser with JavaScript enabled
- AI API key (optional, for AI extraction feature)

## File Structure

```
church-programme-dashboard/
├── admin/
│   ├── css/
│   │   └── admin-style.css
│   ├── js/
│   │   └── admin-script.js
│   ├── views/
│   │   ├── settings-page.php
│   │   └── users-page.php
│   ├── class-cpd-admin.php
│   └── class-cpd-admin-settings.php
├── includes/
│   ├── class-cpd-ajax.php
│   ├── class-cpd-ai.php
│   ├── class-cpd-auth.php
│   ├── class-cpd-database.php
│   ├── class-cpd-rest-api.php
│   └── class-cpd-subdomain.php
├── public/
│   ├── css/
│   │   ├── dashboard-style.css
│   │   └── editor-style.css
│   ├── js/
│   │   ├── dashboard-script.js
│   │   └── editor-script.js
│   ├── templates/
│   │   ├── dashboard.php
│   │   └── editor.php
│   ├── class-cpd-dashboard.php
│   ├── class-cpd-editor.php
│   └── class-cpd-public.php
├── church-programme-dashboard.php
└── README.md
```

## Database Tables

The plugin creates 4 custom tables:

1. **cpd_programmes** - Stores programme data
2. **cpd_editor_users** - Stores editor user credentials
3. **cpd_ai_settings** - Stores AI configuration (deprecated, using localStorage)
4. **cpd_extraction_history** - Logs AI extraction attempts

## Support & Attribution

**Developed by:** Jermesa Studio  
**Website:** https://www.jermesa.com  
**Privacy Policy:** https://jermesa.com/privacy-policy/

**Fonts Used:**
- Inter (Open Font License)
- Poppins (Open Font License)

## License

GPL v2 or later

## Changelog

### Version 1.0.0
- Initial release
- Mobile-first dashboard design
- AI-powered data extraction
- Four programme types support
- Comprehensive admin panel
- Secure editor access
- Cookie consent functionality

## Troubleshooting

### Dashboard not showing
1. Check if rewrite rules are flushed (deactivate and reactivate plugin)
2. Verify custom path name in settings
3. Check WordPress permalink settings

### AI extraction not working
1. Verify API key is correct
2. Check if selected model supports vision
3. Ensure image is clear and readable
4. Check browser console for errors

### Editor login issues
1. Verify user was created in admin panel
2. Check username and password
3. Clear browser cache and cookies

### Calendar not loading
1. Check browser console for JavaScript errors
2. Verify REST API is accessible
3. Check if programmes exist in database

## Future Enhancements

- Export/import programme data
- Email notifications for upcoming programmes
- Multi-language support
- PDF generation
- Mobile app integration
- Advanced reporting

---

For support, please visit https://www.jermesa.com

