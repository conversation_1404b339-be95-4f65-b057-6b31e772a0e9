# Upload Instructions - Critical Fixes

## ⚠️ IMPORTANT: Files Must Be Re-Uploaded

The fixes have been applied to the local files, but **you must upload them to your server** for the changes to take effect.

## 🔧 What Was Fixed

### Fix 1: REST API 403 Errors
**File:** `includes/class-cpd-rest-api.php`
- Added WordPress admin permission check
- Added CORS headers for subdomain access

### Fix 2: Programme Management JavaScript Error
**File:** `admin/js/admin-programmes.js`
- Added form existence check before reset

### Fix 3: Subdomain CORS Issues
**File:** `includes/class-cpd-subdomain.php`
- Added CORS headers
- Prevented theme from loading on subdomains
- Added fallback path URLs

### Fix 4: Settings Page Updates
**File:** `admin/views/settings-page.php`
- Added test buttons for temporary URLs
- Added warning box with fallback URLs

### Fix 5: Admin Redirects
**File:** `admin/class-cpd-admin.php`
- Updated to use fallback URLs

## 📤 How to Upload Files

### Method 1: FTP/SFTP (Recommended)

1. **Connect to your server** using FTP client (FileZilla, WinSCP, etc.)
   - Host: Your server address
   - Username: Your FTP username
   - Password: Your FTP password

2. **Navigate to plugin directory:**
   ```
   /public_html/wp-content/plugins/church-programme-dashboard/
   ```
   (or `/www/wp-content/plugins/church-programme-dashboard/`)

3. **Upload these 5 files** (overwrite existing):
   - `includes/class-cpd-rest-api.php`
   - `includes/class-cpd-subdomain.php`
   - `admin/js/admin-programmes.js`
   - `admin/views/settings-page.php`
   - `admin/class-cpd-admin.php`

4. **Also upload new file:**
   - `version-check.php` (to root of plugin folder)

### Method 2: cPanel File Manager

1. **Log in to cPanel**

2. **Open File Manager**

3. **Navigate to:**
   ```
   public_html/wp-content/plugins/church-programme-dashboard/
   ```

4. **For each file:**
   - Click "Upload" button
   - Select the file from your computer
   - Confirm overwrite when prompted

5. **Files to upload:**
   - `includes/class-cpd-rest-api.php`
   - `includes/class-cpd-subdomain.php`
   - `admin/js/admin-programmes.js`
   - `admin/views/settings-page.php`
   - `admin/class-cpd-admin.php`
   - `version-check.php`

### Method 3: WordPress Plugin Upload (Full Re-upload)

1. **Zip the entire plugin folder:**
   - Right-click `church-programme-dashboard` folder
   - Select "Compress" or "Send to > Compressed folder"
   - Name it `church-programme-dashboard.zip`

2. **In WordPress Admin:**
   - Go to **Plugins** > **Add New**
   - Click **Upload Plugin**
   - Choose the ZIP file
   - Click **Install Now**
   - Click **Replace current with uploaded** when prompted
   - Click **Activate Plugin**

## ✅ Verify Upload

### Step 1: Check Version
Visit this URL in your browser:
```
https://jermesa.com/wp-content/plugins/church-programme-dashboard/version-check.php
```

This will show:
- ✅ Which files are present
- ✅ Which fixes are applied
- ✅ File modification dates
- ✅ Next steps

### Step 2: Clear Cache

**Browser Cache:**
1. Press `Ctrl+Shift+Delete` (Windows) or `Cmd+Shift+Delete` (Mac)
2. Select "Cached images and files"
3. Click "Clear data"

**Or hard refresh:**
- Windows: `Ctrl+F5`
- Mac: `Cmd+Shift+R`

**WordPress Cache (if using cache plugin):**
1. Go to your cache plugin settings
2. Click "Clear Cache" or "Purge Cache"

### Step 3: Flush Permalinks
1. Go to **Settings** > **Permalinks**
2. Click **"Save Changes"** (don't change anything)
3. This flushes WordPress rewrite rules

## 🧪 Test After Upload

### Test 1: Version Check
```
https://jermesa.com/wp-content/plugins/church-programme-dashboard/version-check.php
```
Should show all green checkmarks ✅

### Test 2: Programme Management
1. Go to **Church Programme** > **Settings**
2. Click **"Manage Programmes"** tab
3. Click **"+ Add New Programme"**
4. Modal should open without errors
5. Console should show no 403 errors

### Test 3: Dashboard Access
```
https://jermesa.com/cpd-dashboard/
```
Should load dashboard without CORS errors

### Test 4: Editor Access
```
https://jermesa.com/cpd-editor/
```
Should load editor login page

## 🐛 If Still Not Working

### Check 1: Files Actually Uploaded?
- Visit version-check.php
- Verify modification dates are recent
- All fixes should show ✅

### Check 2: Browser Cache Cleared?
- Try incognito/private browsing mode
- Or different browser

### Check 3: Server Cache?
- If using caching plugin, clear it
- If using server-level cache (Varnish, Redis), clear it
- Contact hosting provider if needed

### Check 4: File Permissions
Files should have these permissions:
- PHP files: `644` or `640`
- JS files: `644`
- Directories: `755`

To fix permissions via FTP:
1. Right-click file
2. Select "File Permissions"
3. Set to `644`

## 📋 Checklist

Before testing, ensure:

- [ ] All 5 files uploaded to server
- [ ] version-check.php uploaded
- [ ] Browser cache cleared
- [ ] WordPress cache cleared (if applicable)
- [ ] Permalinks flushed
- [ ] version-check.php shows all green ✅
- [ ] Tested in incognito mode

## 🆘 Still Having Issues?

### Console Errors Still Showing?

**If you see:**
```
admin-programmes.js?ver=1.0.0:140 Uncaught TypeError: Cannot read properties of undefined (reading 'reset')
```

**This means:** The old file is still cached. The line number (140) should be different in the new file.

**Solution:**
1. Check version-check.php
2. Verify file modification date is recent
3. Clear browser cache completely
4. Try incognito mode

### 403 Errors Still Showing?

**If you see:**
```
/wp-json/cpd/v1/editor/programmes:1 Failed to load resource: the server responded with a status of 403 ()
```

**This means:** REST API file not updated or cache issue.

**Solution:**
1. Verify `includes/class-cpd-rest-api.php` is uploaded
2. Check version-check.php shows REST API fix ✅
3. Log out and log back in to WordPress admin
4. Clear all caches

### CORS Errors Still Showing?

**If you see:**
```
Access to script at 'https://jermesa.com/...' from origin 'https://churchprogramme.jermesa.com' has been blocked by CORS policy
```

**This means:** Subdomain file not updated.

**Solution:**
1. Verify `includes/class-cpd-subdomain.php` is uploaded
2. Check version-check.php shows subdomain fix ✅
3. Use temporary URLs instead: `/cpd-dashboard/` and `/cpd-editor/`

## 📞 Support

If issues persist after following all steps:

1. **Take screenshot of version-check.php page**
2. **Take screenshot of browser console errors**
3. **Note which step failed**
4. **Contact support with:**
   - Screenshots
   - URL you're trying to access
   - Browser and version
   - Whether you're logged in as admin

## 🎯 Expected Results After Upload

### Version Check Page:
```
✅ All Required Files Found
✅ REST API Fix: Applied
✅ Admin JavaScript Fix: Applied
✅ Subdomain Fallback Fix: Applied
✅ All Fixes Applied Successfully!
```

### Programme Management:
- No console errors
- Modal opens smoothly
- Can add/edit/delete programmes
- No 403 errors

### Dashboard:
- Loads at `/cpd-dashboard/`
- No CORS errors
- Shows calendar and carousel
- No theme files loaded

### Editor:
- Loads at `/cpd-editor/`
- Shows login form
- No CORS errors
- No theme files loaded

---

**Remember:** Files must be uploaded to server for changes to take effect!

**Quick Check:** Visit version-check.php first to verify upload status.

**Plugin Version:** 1.0.0  
**Fixes Date:** 2025-09-30  
**Upload Required:** YES ⚠️

