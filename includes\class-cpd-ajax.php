<?php
/**
 * AJAX Handler Class
 * 
 * Handles AJAX requests for the plugin
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

class CPD_AJAX {
    
    /**
     * Initialize
     */
    public static function init() {
        // Admin AJAX actions
        add_action('wp_ajax_cpd_upload_image', array(__CLASS__, 'upload_image'));
        add_action('wp_ajax_cpd_save_settings', array(__CLASS__, 'save_settings'));
        add_action('wp_ajax_cpd_create_editor_user', array(__CLASS__, 'create_editor_user'));
        add_action('wp_ajax_cpd_delete_editor_user', array(__CLASS__, 'delete_editor_user'));
        add_action('wp_ajax_cpd_get_editor_users', array(__CLASS__, 'get_editor_users'));
    }
    
    /**
     * Upload image
     */
    public static function upload_image() {
        check_ajax_referer('cpd_ajax_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('Permission denied.', 'church-programme-dashboard')));
        }
        
        if (empty($_FILES['file'])) {
            wp_send_json_error(array('message' => __('No file uploaded.', 'church-programme-dashboard')));
        }
        
        require_once(ABSPATH . 'wp-admin/includes/file.php');
        require_once(ABSPATH . 'wp-admin/includes/image.php');
        require_once(ABSPATH . 'wp-admin/includes/media.php');
        
        $file = $_FILES['file'];
        $upload_overrides = array('test_form' => false);
        
        $movefile = wp_handle_upload($file, $upload_overrides);
        
        if ($movefile && !isset($movefile['error'])) {
            wp_send_json_success(array(
                'url' => $movefile['url'],
                'file' => $movefile['file'],
            ));
        } else {
            wp_send_json_error(array('message' => $movefile['error']));
        }
    }
    
    /**
     * Save settings
     */
    public static function save_settings() {
        // Log the request for debugging
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('CPD: save_settings() called');
            error_log('CPD: POST data: ' . print_r($_POST, true));
        }

        // Verify nonce
        try {
            check_ajax_referer('cpd_ajax_nonce', 'nonce');
        } catch (Exception $e) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('CPD: Nonce verification failed: ' . $e->getMessage());
            }
            wp_send_json_error(array('message' => __('Security check failed. Please refresh the page and try again.', 'church-programme-dashboard')));
            return;
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('CPD: Permission denied for user: ' . get_current_user_id());
            }
            wp_send_json_error(array('message' => __('Permission denied.', 'church-programme-dashboard')));
            return;
        }

        // Get settings from POST
        $settings = isset($_POST['settings']) ? $_POST['settings'] : array();

        if (empty($settings)) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('CPD: No settings provided');
            }
            wp_send_json_error(array('message' => __('No settings to save.', 'church-programme-dashboard')));
            return;
        }

        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('CPD: Processing ' . count($settings) . ' settings');
        }

        $saved_count = 0;
        $errors = array();

        foreach ($settings as $key => $value) {
            // Skip empty keys
            if (empty($key)) {
                continue;
            }

            // Sanitize based on key
            if (strpos($key, 'color') !== false) {
                $value = sanitize_hex_color($value);
            } elseif (strpos($key, 'url') !== false || strpos($key, 'image') !== false) {
                $value = esc_url_raw($value);
            } elseif (strpos($key, 'content') !== false) {
                $value = wp_kses_post($value);
            } else {
                $value = sanitize_text_field($value);
            }

            // Update option
            $option_name = 'cpd_' . $key;
            $result = update_option($option_name, $value);

            if ($result || get_option($option_name) === $value) {
                $saved_count++;
                if (defined('WP_DEBUG') && WP_DEBUG) {
                    error_log("CPD: Saved $option_name = " . substr($value, 0, 50));
                }
            } else {
                $errors[] = $key;
                if (defined('WP_DEBUG') && WP_DEBUG) {
                    error_log("CPD: Failed to save $option_name");
                }
            }
        }

        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log("CPD: Saved $saved_count settings, " . count($errors) . " errors");
        }

        if (!empty($errors) && $saved_count === 0) {
            wp_send_json_error(array(
                'message' => __('Failed to save settings. Please try again.', 'church-programme-dashboard'),
                'errors' => $errors
            ));
        } else {
            $message = __('Settings saved successfully.', 'church-programme-dashboard');
            if (!empty($errors)) {
                $message .= ' ' . sprintf(__('(%d settings could not be saved)', 'church-programme-dashboard'), count($errors));
            }
            wp_send_json_success(array(
                'message' => $message,
                'saved_count' => $saved_count,
                'total_count' => count($settings)
            ));
        }
    }
    
    /**
     * Create editor user
     */
    public static function create_editor_user() {
        check_ajax_referer('cpd_ajax_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('Permission denied.', 'church-programme-dashboard')));
        }
        
        $username = isset($_POST['username']) ? sanitize_user($_POST['username']) : '';
        $password = isset($_POST['password']) ? $_POST['password'] : '';
        $email = isset($_POST['email']) ? sanitize_email($_POST['email']) : '';
        
        if (empty($username) || empty($password)) {
            wp_send_json_error(array('message' => __('Username and password are required.', 'church-programme-dashboard')));
        }
        
        // Check if username already exists
        $existing = CPD_Database::get_editor_user_by_username($username);
        if ($existing) {
            wp_send_json_error(array('message' => __('Username already exists.', 'church-programme-dashboard')));
        }
        
        $user_id = CPD_Database::create_editor_user($username, $password, $email, get_current_user_id());
        
        if ($user_id) {
            wp_send_json_success(array(
                'message' => __('Editor user created successfully.', 'church-programme-dashboard'),
                'user_id' => $user_id,
            ));
        } else {
            wp_send_json_error(array('message' => __('Failed to create editor user.', 'church-programme-dashboard')));
        }
    }
    
    /**
     * Delete editor user
     */
    public static function delete_editor_user() {
        check_ajax_referer('cpd_ajax_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('Permission denied.', 'church-programme-dashboard')));
        }
        
        $user_id = isset($_POST['user_id']) ? intval($_POST['user_id']) : 0;
        
        if (!$user_id) {
            wp_send_json_error(array('message' => __('Invalid user ID.', 'church-programme-dashboard')));
        }
        
        $result = CPD_Database::delete_editor_user($user_id);
        
        if ($result) {
            wp_send_json_success(array('message' => __('Editor user deleted successfully.', 'church-programme-dashboard')));
        } else {
            wp_send_json_error(array('message' => __('Failed to delete editor user.', 'church-programme-dashboard')));
        }
    }
    
    /**
     * Get editor users
     */
    public static function get_editor_users() {
        check_ajax_referer('cpd_ajax_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('Permission denied.', 'church-programme-dashboard')));
        }
        
        $users = CPD_Database::get_all_editor_users();
        
        wp_send_json_success(array('users' => $users));
    }
}

