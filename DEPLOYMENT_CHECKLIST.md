# Deployment Checklist - Church Programme Dashboard

Use this checklist before deploying to production.

## Pre-Deployment

### 1. Code Review
- [x] All PHP files reviewed
- [x] All JavaScript files reviewed
- [x] No console.log() statements in production code
- [x] No TODO comments remaining
- [x] All functions implemented
- [x] Error handling in place

### 2. File Structure Verification
- [x] All required files present
- [x] Correct directory structure
- [x] No development files included
- [x] README.md complete
- [x] INSTALLATION.md complete
- [x] Documentation up to date

### 3. Security Audit
- [x] API keys stored in localStorage only
- [x] All database queries use prepared statements
- [x] Input sanitization implemented
- [x] Output escaping implemented
- [x] WordPress nonce verification
- [x] File upload restrictions
- [x] XSS prevention
- [x] CSRF protection
- [x] SQL injection prevention

### 4. Testing
- [ ] Run complete test suite (see TESTING_GUIDE.md)
- [ ] Test on fresh WordPress installation
- [ ] Test all CRUD operations
- [ ] Test AI extraction with all providers
- [ ] Test on multiple browsers
- [ ] Test on mobile devices
- [ ] Test with large datasets (50+ programmes)
- [ ] Performance testing
- [ ] Security testing

## Deployment Steps

### Step 1: Prepare Files
- [ ] Create clean copy of plugin folder
- [ ] Remove any .git folders
- [ ] Remove any development files
- [ ] Remove any test files
- [ ] Verify all files have proper headers

### Step 2: Create ZIP Package
```bash
cd /path/to/plugins
zip -r church-programme-dashboard.zip church-programme-dashboard/ -x "*.git*" "*.DS_Store" "*node_modules*"
```

- [ ] ZIP file created
- [ ] ZIP file size reasonable (< 5MB)
- [ ] Test ZIP extraction

### Step 3: Backup Production Site
Before installing on production:
- [ ] Backup WordPress database
- [ ] Backup WordPress files
- [ ] Note current WordPress version
- [ ] Note current PHP version
- [ ] Document current plugins

### Step 4: Install on Staging (Recommended)
- [ ] Upload to staging site
- [ ] Activate plugin
- [ ] Check for errors
- [ ] Test all features
- [ ] Verify no conflicts with other plugins
- [ ] Check performance impact

### Step 5: Install on Production
- [ ] Upload ZIP via WordPress admin
- [ ] Or upload via FTP to wp-content/plugins/
- [ ] Activate plugin
- [ ] Check for PHP errors
- [ ] Verify database tables created

### Step 6: Initial Configuration
- [ ] Go to Church Programme > Settings
- [ ] Set dashboard title
- [ ] Set custom path
- [ ] Configure colors
- [ ] Set programme labels
- [ ] Configure notice board
- [ ] Configure cookie consent
- [ ] Save all settings

### Step 7: Create Users
- [ ] Go to Church Programme > Editor Users
- [ ] Create first editor user
- [ ] Test login
- [ ] Create additional users as needed

### Step 8: Add Initial Data
- [ ] Configure AI settings (if using)
- [ ] Add first programme (test)
- [ ] Verify programme displays on dashboard
- [ ] Add more programmes
- [ ] Test calendar functionality

### Step 9: Verify Public Access
- [ ] Visit dashboard URL
- [ ] Check header displays correctly
- [ ] Check carousel works
- [ ] Check calendar works
- [ ] Check modal popups work
- [ ] Check cookie consent works
- [ ] Check all links work
- [ ] Test on mobile device

### Step 10: Verify Editor Access
- [ ] Visit editor URL
- [ ] Test login
- [ ] Test AI extraction
- [ ] Test manual entry
- [ ] Test programme management
- [ ] Test edit functionality
- [ ] Test delete functionality
- [ ] Test logout

## Post-Deployment

### 1. Monitoring
First 24 hours:
- [ ] Monitor error logs
- [ ] Check for PHP errors
- [ ] Check for JavaScript errors
- [ ] Monitor server performance
- [ ] Check database queries

First week:
- [ ] Gather user feedback
- [ ] Monitor usage patterns
- [ ] Check for any issues
- [ ] Verify AI extraction working
- [ ] Check mobile usage

### 2. Documentation
- [ ] Share dashboard URL with congregation
- [ ] Provide editor login credentials to authorized users
- [ ] Document any custom configurations
- [ ] Create user guide if needed
- [ ] Document AI provider setup

### 3. Training
- [ ] Train editor users on:
  - Login process
  - AI extraction
  - Manual entry
  - Programme management
  - Best practices

### 4. Maintenance Plan
- [ ] Schedule regular backups
- [ ] Plan for programme data cleanup
- [ ] Monitor AI API usage/costs
- [ ] Plan for WordPress updates
- [ ] Plan for plugin updates

## Rollback Plan

If issues occur:

### Immediate Rollback
1. Deactivate plugin via WordPress admin
2. Or rename plugin folder via FTP
3. Restore from backup if needed

### Data Preservation
If rolling back but want to keep data:
1. Export database tables:
   - wp_cpd_programmes
   - wp_cpd_editor_users
   - wp_cpd_ai_settings
   - wp_cpd_extraction_history
2. Store SQL dump safely
3. Can re-import after fixing issues

## Performance Optimization

### After Deployment
- [ ] Enable caching plugin (if not already)
- [ ] Exclude editor page from cache
- [ ] Enable GZIP compression
- [ ] Optimize images if using header backgrounds
- [ ] Consider CDN for static assets
- [ ] Monitor page load times

### Database Optimization
- [ ] Verify indexes are working
- [ ] Monitor query performance
- [ ] Clean up old extraction history periodically
- [ ] Consider archiving old programmes

## Security Hardening

### Additional Security Measures
- [ ] Enable HTTPS (SSL certificate)
- [ ] Use strong passwords for editor users
- [ ] Limit login attempts (use security plugin)
- [ ] Regular security scans
- [ ] Keep WordPress updated
- [ ] Keep PHP updated
- [ ] Monitor for suspicious activity

### API Key Management
- [ ] Document which users have API keys
- [ ] Monitor API usage
- [ ] Rotate keys periodically
- [ ] Revoke keys for departed users

## Compliance Verification

### Legal Compliance
- [ ] Privacy policy link working
- [ ] Cookie consent functional
- [ ] Attribution to Jermesa Studio present
- [ ] Font attribution present
- [ ] All licenses compliant

### Accessibility
- [ ] Test with screen reader
- [ ] Check keyboard navigation
- [ ] Verify color contrast
- [ ] Check alt text on images
- [ ] Test with accessibility tools

## Support Setup

### User Support
- [ ] Create support email/contact
- [ ] Document common issues
- [ ] Create FAQ document
- [ ] Set up feedback mechanism

### Technical Support
- [ ] Document server requirements
- [ ] Document troubleshooting steps
- [ ] Keep contact for Jermesa Studio
- [ ] Maintain changelog

## Success Metrics

### Track These Metrics
- [ ] Dashboard page views
- [ ] Editor logins
- [ ] Programmes added per week
- [ ] AI extractions performed
- [ ] User feedback/satisfaction
- [ ] Mobile vs desktop usage
- [ ] Page load times
- [ ] Error rates

## Final Verification

Before marking deployment complete:
- [ ] All features working
- [ ] No critical errors
- [ ] Performance acceptable
- [ ] Security measures in place
- [ ] Documentation complete
- [ ] Users trained
- [ ] Monitoring active
- [ ] Backup system working
- [ ] Rollback plan ready
- [ ] Support system ready

## Sign-Off

**Deployed by:** _______________  
**Date:** _______________  
**WordPress Version:** _______________  
**PHP Version:** _______________  
**Server:** _______________  

**Dashboard URL:** _______________  
**Editor URL:** _______________  

**Issues Found:** _______________  
**Issues Resolved:** _______________  

**Status:** ☐ Success  ☐ Success with minor issues  ☐ Failed  

**Notes:**
_______________________________________________
_______________________________________________
_______________________________________________

---

## Quick Reference

**Plugin Version:** 1.0.0  
**WordPress Required:** 5.8+  
**PHP Required:** 7.4+  
**License:** GPL v2 or later  

**Support:** https://www.jermesa.com  
**Privacy Policy:** https://jermesa.com/privacy-policy/  

**Database Tables:**
- wp_cpd_programmes
- wp_cpd_editor_users
- wp_cpd_ai_settings
- wp_cpd_extraction_history

**Key Files:**
- Main: church-programme-dashboard.php
- Dashboard: public/templates/dashboard.php
- Editor: public/templates/editor.php
- Admin: admin/views/settings-page.php

**REST API Endpoints:**
- Public: /wp-json/cpd/v1/programmes/*
- Editor: /wp-json/cpd/v1/editor/*
- AI: /wp-json/cpd/v1/ai/*

---

**Deployment Complete!** 🎉

Your Church Programme Dashboard is now live and ready to serve your congregation.

For support or questions, visit https://www.jermesa.com

