<?php
/**
 * Admin Settings Page
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

$settings = CPD_Admin_Settings::get_all_settings();
?>

<div class="wrap cpd-admin-wrap">
    <h1><?php echo esc_html__('Church Programme Dashboard Settings', 'church-programme-dashboard'); ?></h1>
    
    <div class="cpd-admin-notice" id="cpd-save-notice" style="display: none;">
        <p></p>
    </div>
    
    <div class="cpd-admin-tabs">
        <nav class="nav-tab-wrapper">
            <a href="#general" class="nav-tab nav-tab-active"><?php esc_html_e('General', 'church-programme-dashboard'); ?></a>
            <a href="#subdomains" class="nav-tab"><?php esc_html_e('Subdomains', 'church-programme-dashboard'); ?></a>
            <a href="#colors" class="nav-tab"><?php esc_html_e('Colors & Styling', 'church-programme-dashboard'); ?></a>
            <a href="#labels" class="nav-tab"><?php esc_html_e('Programme Labels', 'church-programme-dashboard'); ?></a>
            <a href="#programmes" class="nav-tab"><?php esc_html_e('Manage Programmes', 'church-programme-dashboard'); ?></a>
            <a href="#notice" class="nav-tab"><?php esc_html_e('Notice Board', 'church-programme-dashboard'); ?></a>
            <a href="#carousel" class="nav-tab"><?php esc_html_e('Carousel Animation', 'church-programme-dashboard'); ?></a>
            <a href="#cookie" class="nav-tab"><?php esc_html_e('Cookie Consent', 'church-programme-dashboard'); ?></a>
            <a href="#links" class="nav-tab"><?php esc_html_e('Quick Links', 'church-programme-dashboard'); ?></a>
        </nav>
        
        <form id="cpd-settings-form" method="post">
            <?php wp_nonce_field('cpd_save_settings', 'cpd_settings_nonce'); ?>
            
            <!-- General Settings -->
            <div id="general" class="cpd-tab-content cpd-tab-active">
                <h2><?php esc_html_e('General Settings', 'church-programme-dashboard'); ?></h2>
                
                <table class="form-table">
                    <tr>
                        <th scope="row">
                            <label for="dashboard_title"><?php esc_html_e('Dashboard Title', 'church-programme-dashboard'); ?></label>
                        </th>
                        <td>
                            <input type="text" id="dashboard_title" name="dashboard_title" 
                                   value="<?php echo esc_attr($settings['general']['dashboard_title']); ?>" 
                                   class="regular-text">
                            <p class="description"><?php esc_html_e('The main title displayed on the dashboard.', 'church-programme-dashboard'); ?></p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label><?php esc_html_e('Dashboard & Editor URLs', 'church-programme-dashboard'); ?></label>
                        </th>
                        <td>
                            <p class="description">
                                <?php esc_html_e('This plugin uses subdomain-based URLs. Configure your subdomains in the "Subdomains" tab.', 'church-programme-dashboard'); ?>
                            </p>
                            <p class="description">
                                <strong><?php esc_html_e('Dashboard URL (Subdomain):', 'church-programme-dashboard'); ?></strong>
                                <code><?php echo esc_html(CPD_Subdomain::get_dashboard_url()); ?></code>
                                <a href="<?php echo esc_url(CPD_Subdomain::get_dashboard_url()); ?>" target="_blank" class="button button-small" style="margin-left: 10px;">
                                    <?php esc_html_e('Test', 'church-programme-dashboard'); ?>
                                </a>
                            </p>
                            <p class="description">
                                <strong><?php esc_html_e('Editor URL (Subdomain):', 'church-programme-dashboard'); ?></strong>
                                <code><?php echo esc_html(CPD_Subdomain::get_editor_url()); ?></code>
                                <a href="<?php echo esc_url(CPD_Subdomain::get_editor_url()); ?>" target="_blank" class="button button-small" style="margin-left: 10px;">
                                    <?php esc_html_e('Test', 'church-programme-dashboard'); ?>
                                </a>
                            </p>
                            <div style="background: #fff3cd; border-left: 4px solid #ffc107; padding: 10px; margin-top: 10px;">
                                <p><strong><?php esc_html_e('Testing Before DNS Configuration:', 'church-programme-dashboard'); ?></strong></p>
                                <p class="description">
                                    <?php esc_html_e('If you haven\'t configured DNS records yet, use these temporary URLs:', 'church-programme-dashboard'); ?>
                                </p>
                                <p class="description">
                                    <strong><?php esc_html_e('Dashboard (Temporary):', 'church-programme-dashboard'); ?></strong>
                                    <code><?php echo esc_html(CPD_Subdomain::get_dashboard_url(true)); ?></code>
                                    <a href="<?php echo esc_url(CPD_Subdomain::get_dashboard_url(true)); ?>" target="_blank" class="button button-small" style="margin-left: 10px;">
                                        <?php esc_html_e('Test', 'church-programme-dashboard'); ?>
                                    </a>
                                </p>
                                <p class="description">
                                    <strong><?php esc_html_e('Editor (Temporary):', 'church-programme-dashboard'); ?></strong>
                                    <code><?php echo esc_html(CPD_Subdomain::get_editor_url(true)); ?></code>
                                    <a href="<?php echo esc_url(CPD_Subdomain::get_editor_url(true)); ?>" target="_blank" class="button button-small" style="margin-left: 10px;">
                                        <?php esc_html_e('Test', 'church-programme-dashboard'); ?>
                                    </a>
                                </p>
                            </div>
                        </td>
                    </tr>

                    <tr>
                        <th scope="row">
                            <label for="header_bg_color"><?php esc_html_e('Header Background Color', 'church-programme-dashboard'); ?></label>
                        </th>
                        <td>
                            <input type="text" id="header_bg_color" name="header_bg_color" 
                                   value="<?php echo esc_attr($settings['general']['header_bg_color']); ?>" 
                                   class="cpd-color-picker">
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="header_bg_image"><?php esc_html_e('Header Background Image', 'church-programme-dashboard'); ?></label>
                        </th>
                        <td>
                            <input type="text" id="header_bg_image" name="header_bg_image" 
                                   value="<?php echo esc_attr($settings['general']['header_bg_image']); ?>" 
                                   class="regular-text">
                            <button type="button" class="button cpd-upload-image" data-target="header_bg_image">
                                <?php esc_html_e('Upload Image', 'church-programme-dashboard'); ?>
                            </button>
                            <?php if ($settings['general']['header_bg_image']): ?>
                                <button type="button" class="button cpd-remove-image" data-target="header_bg_image">
                                    <?php esc_html_e('Remove', 'church-programme-dashboard'); ?>
                                </button>
                            <?php endif; ?>
                            <p class="description"><?php esc_html_e('Optional background image for the header.', 'church-programme-dashboard'); ?></p>
                        </td>
                    </tr>
                </table>
            </div>

            <!-- Subdomain Settings -->
            <div id="subdomains" class="cpd-tab-content">
                <h2><?php esc_html_e('Subdomain Configuration', 'church-programme-dashboard'); ?></h2>

                <div class="cpd-notice cpd-notice-info">
                    <p><strong><?php esc_html_e('Important:', 'church-programme-dashboard'); ?></strong></p>
                    <p><?php esc_html_e('This plugin uses subdomain-based URLs for the dashboard and editor pages.', 'church-programme-dashboard'); ?></p>
                    <p><?php esc_html_e('You must configure DNS records for these subdomains to point to your WordPress installation.', 'church-programme-dashboard'); ?></p>
                    <p><?php
                        printf(
                            esc_html__('Your base domain is: %s', 'church-programme-dashboard'),
                            '<strong>' . esc_html(CPD_Subdomain::get_base_domain()) . '</strong>'
                        );
                    ?></p>
                </div>

                <table class="form-table">
                    <tr>
                        <th scope="row">
                            <label for="dashboard_subdomain"><?php esc_html_e('Dashboard Subdomain', 'church-programme-dashboard'); ?></label>
                        </th>
                        <td>
                            <input type="text" id="dashboard_subdomain" name="dashboard_subdomain"
                                   value="<?php echo esc_attr(get_option('cpd_dashboard_subdomain', 'churchprogramme')); ?>"
                                   class="regular-text">
                            <p class="description">
                                <?php esc_html_e('Enter only the subdomain name (e.g., "churchprogramme").', 'church-programme-dashboard'); ?><br>
                                <strong><?php esc_html_e('Full URL:', 'church-programme-dashboard'); ?></strong>
                                <code><?php echo esc_html(CPD_Subdomain::get_dashboard_url()); ?></code>
                            </p>
                        </td>
                    </tr>

                    <tr>
                        <th scope="row">
                            <label for="editor_subdomain"><?php esc_html_e('Editor Subdomain', 'church-programme-dashboard'); ?></label>
                        </th>
                        <td>
                            <input type="text" id="editor_subdomain" name="editor_subdomain"
                                   value="<?php echo esc_attr(get_option('cpd_editor_subdomain', 'churcheditor')); ?>"
                                   class="regular-text">
                            <p class="description">
                                <?php esc_html_e('Enter only the subdomain name (e.g., "churcheditor").', 'church-programme-dashboard'); ?><br>
                                <strong><?php esc_html_e('Full URL:', 'church-programme-dashboard'); ?></strong>
                                <code><?php echo esc_html(CPD_Subdomain::get_editor_url()); ?></code>
                            </p>
                        </td>
                    </tr>
                </table>

                <div class="cpd-notice cpd-notice-warning">
                    <p><strong><?php esc_html_e('DNS Configuration Required:', 'church-programme-dashboard'); ?></strong></p>
                    <p><?php esc_html_e('After setting your subdomains, you need to add DNS records:', 'church-programme-dashboard'); ?></p>
                    <ol>
                        <li><?php esc_html_e('Log in to your domain registrar or DNS provider', 'church-programme-dashboard'); ?></li>
                        <li><?php esc_html_e('Add an A record or CNAME record for each subdomain', 'church-programme-dashboard'); ?></li>
                        <li><?php esc_html_e('Point the records to your WordPress server IP or domain', 'church-programme-dashboard'); ?></li>
                        <li><?php esc_html_e('Wait for DNS propagation (can take up to 48 hours)', 'church-programme-dashboard'); ?></li>
                    </ol>
                    <p><strong><?php esc_html_e('Example DNS Records:', 'church-programme-dashboard'); ?></strong></p>
                    <pre style="background: #f5f5f5; padding: 10px; border-radius: 4px;">
Type: A or CNAME
Name: <?php echo esc_html(get_option('cpd_dashboard_subdomain', 'churchprogramme')); ?>

Host: <?php echo esc_html(get_option('cpd_dashboard_subdomain', 'churchprogramme')); ?>.<?php echo esc_html(CPD_Subdomain::get_base_domain()); ?>

Points to: <?php echo esc_html($_SERVER['SERVER_ADDR'] ?? 'your-server-ip'); ?> (or <?php echo esc_html(CPD_Subdomain::get_base_domain()); ?>)
                    </pre>
                </div>
            </div>

            <!-- Color Settings -->
            <div id="colors" class="cpd-tab-content">
                <h2><?php esc_html_e('Colors & Styling', 'church-programme-dashboard'); ?></h2>
                
                <table class="form-table">
                    <tr>
                        <th scope="row">
                            <label for="primary_color"><?php esc_html_e('Primary Color', 'church-programme-dashboard'); ?></label>
                        </th>
                        <td>
                            <input type="text" id="primary_color" name="primary_color" 
                                   value="<?php echo esc_attr($settings['colors']['primary_color']); ?>" 
                                   class="cpd-color-picker">
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="secondary_color"><?php esc_html_e('Secondary Color', 'church-programme-dashboard'); ?></label>
                        </th>
                        <td>
                            <input type="text" id="secondary_color" name="secondary_color" 
                                   value="<?php echo esc_attr($settings['colors']['secondary_color']); ?>" 
                                   class="cpd-color-picker">
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="accent_color"><?php esc_html_e('Accent Color', 'church-programme-dashboard'); ?></label>
                        </th>
                        <td>
                            <input type="text" id="accent_color" name="accent_color" 
                                   value="<?php echo esc_attr($settings['colors']['accent_color']); ?>" 
                                   class="cpd-color-picker">
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="text_color"><?php esc_html_e('Text Color', 'church-programme-dashboard'); ?></label>
                        </th>
                        <td>
                            <input type="text" id="text_color" name="text_color" 
                                   value="<?php echo esc_attr($settings['colors']['text_color']); ?>" 
                                   class="cpd-color-picker">
                        </td>
                    </tr>
                </table>
            </div>
            
            <!-- Programme Labels -->
            <div id="labels" class="cpd-tab-content">
                <h2><?php esc_html_e('Programme Labels', 'church-programme-dashboard'); ?></h2>
                <p><?php esc_html_e('Customize the labels and times displayed for each programme type.', 'church-programme-dashboard'); ?></p>
                
                <table class="form-table">
                    <tr>
                        <th scope="row" colspan="2">
                            <h3><?php esc_html_e('MIET BALANG', 'church-programme-dashboard'); ?></h3>
                        </th>
                    </tr>
                    <tr>
                        <th scope="row">
                            <label for="label_miet_balang"><?php esc_html_e('Label', 'church-programme-dashboard'); ?></label>
                        </th>
                        <td>
                            <input type="text" id="label_miet_balang" name="label_miet_balang" 
                                   value="<?php echo esc_attr($settings['labels']['miet_balang']); ?>" 
                                   class="regular-text">
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">
                            <label for="label_miet_balang_time"><?php esc_html_e('Time', 'church-programme-dashboard'); ?></label>
                        </th>
                        <td>
                            <input type="text" id="label_miet_balang_time" name="label_miet_balang_time" 
                                   value="<?php echo esc_attr($settings['labels']['miet_balang_time']); ?>" 
                                   class="regular-text">
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row" colspan="2">
                            <h3><?php esc_html_e('JINGIASENG SAMLA', 'church-programme-dashboard'); ?></h3>
                        </th>
                    </tr>
                    <tr>
                        <th scope="row">
                            <label for="label_jingiaseng_samla"><?php esc_html_e('Label', 'church-programme-dashboard'); ?></label>
                        </th>
                        <td>
                            <input type="text" id="label_jingiaseng_samla" name="label_jingiaseng_samla" 
                                   value="<?php echo esc_attr($settings['labels']['jingiaseng_samla']); ?>" 
                                   class="regular-text">
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">
                            <label for="label_jingiaseng_samla_time"><?php esc_html_e('Time', 'church-programme-dashboard'); ?></label>
                        </th>
                        <td>
                            <input type="text" id="label_jingiaseng_samla_time" name="label_jingiaseng_samla_time" 
                                   value="<?php echo esc_attr($settings['labels']['jingiaseng_samla_time']); ?>" 
                                   class="regular-text">
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row" colspan="2">
                            <h3><?php esc_html_e('JINGIASENG 1:00 Baje', 'church-programme-dashboard'); ?></h3>
                        </th>
                    </tr>
                    <tr>
                        <th scope="row">
                            <label for="label_jingiaseng_1pm"><?php esc_html_e('Label', 'church-programme-dashboard'); ?></label>
                        </th>
                        <td>
                            <input type="text" id="label_jingiaseng_1pm" name="label_jingiaseng_1pm" 
                                   value="<?php echo esc_attr($settings['labels']['jingiaseng_1pm']); ?>" 
                                   class="regular-text">
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">
                            <label for="label_jingiaseng_1pm_time"><?php esc_html_e('Time', 'church-programme-dashboard'); ?></label>
                        </th>
                        <td>
                            <input type="text" id="label_jingiaseng_1pm_time" name="label_jingiaseng_1pm_time" 
                                   value="<?php echo esc_attr($settings['labels']['jingiaseng_1pm_time']); ?>" 
                                   class="regular-text">
                        </td>
                    </tr>

                    <tr>
                        <th scope="row" colspan="2">
                            <h3><?php esc_html_e('JINGIASENG KHYNNAH', 'church-programme-dashboard'); ?></h3>
                        </th>
                    </tr>
                    <tr>
                        <th scope="row">
                            <label for="label_jingiaseng_khynnah"><?php esc_html_e('Label', 'church-programme-dashboard'); ?></label>
                        </th>
                        <td>
                            <input type="text" id="label_jingiaseng_khynnah" name="label_jingiaseng_khynnah"
                                   value="<?php echo esc_attr($settings['labels']['jingiaseng_khynnah']); ?>"
                                   class="regular-text">
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">
                            <label for="label_jingiaseng_khynnah_time"><?php esc_html_e('Time', 'church-programme-dashboard'); ?></label>
                        </th>
                        <td>
                            <input type="text" id="label_jingiaseng_khynnah_time" name="label_jingiaseng_khynnah_time"
                                   value="<?php echo esc_attr($settings['labels']['jingiaseng_khynnah_time']); ?>"
                                   class="regular-text">
                        </td>
                    </tr>

                    <tr>
                        <th scope="row" colspan="2">
                            <h3><?php esc_html_e('JINGIASENG IING', 'church-programme-dashboard'); ?></h3>
                        </th>
                    </tr>
                    <tr>
                        <th scope="row">
                            <label for="label_jingiaseng_iing"><?php esc_html_e('Label', 'church-programme-dashboard'); ?></label>
                        </th>
                        <td>
                            <input type="text" id="label_jingiaseng_iing" name="label_jingiaseng_iing"
                                   value="<?php echo esc_attr($settings['labels']['jingiaseng_iing']); ?>"
                                   class="regular-text">
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">
                            <label for="label_jingiaseng_iing_time"><?php esc_html_e('Time', 'church-programme-dashboard'); ?></label>
                        </th>
                        <td>
                            <input type="text" id="label_jingiaseng_iing_time" name="label_jingiaseng_iing_time"
                                   value="<?php echo esc_attr($settings['labels']['jingiaseng_iing_time']); ?>"
                                   class="regular-text">
                        </td>
                    </tr>
                </table>
            </div>

            <!-- Manage Programmes -->
            <div id="programmes" class="cpd-tab-content">
                <h2><?php esc_html_e('Manage Programmes', 'church-programme-dashboard'); ?></h2>
                <p><?php esc_html_e('Add, edit, or delete programme data directly from the admin panel.', 'church-programme-dashboard'); ?></p>

                <div class="cpd-programmes-admin-section">
                    <!-- Filter Section -->
                    <div class="cpd-admin-filter-bar" style="background: #f9f9f9; padding: 15px; margin-bottom: 20px; border-radius: 4px;">
                        <h3><?php esc_html_e('Filter Programmes', 'church-programme-dashboard'); ?></h3>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 15px;">
                            <div>
                                <label for="admin-filter-type"><?php esc_html_e('Programme Type', 'church-programme-dashboard'); ?></label>
                                <select id="admin-filter-type" class="regular-text">
                                    <option value=""><?php esc_html_e('All Types', 'church-programme-dashboard'); ?></option>
                                    <option value="jingiaseng_1pm"><?php esc_html_e('JINGIASENG 1:00 Baje', 'church-programme-dashboard'); ?></option>
                                    <option value="miet_balang"><?php esc_html_e('MIET BALANG', 'church-programme-dashboard'); ?></option>
                                    <option value="jingiaseng_samla"><?php esc_html_e('JINGIASENG SAMLA', 'church-programme-dashboard'); ?></option>
                                    <option value="jingiaseng_khynnah"><?php esc_html_e('JINGIASENG KHYNNAH', 'church-programme-dashboard'); ?></option>
                                    <option value="jingiaseng_iing"><?php esc_html_e('JINGIASENG IING', 'church-programme-dashboard'); ?></option>
                                </select>
                            </div>
                            <div>
                                <label for="admin-filter-start-date"><?php esc_html_e('Start Date', 'church-programme-dashboard'); ?></label>
                                <input type="date" id="admin-filter-start-date" class="regular-text">
                            </div>
                            <div>
                                <label for="admin-filter-end-date"><?php esc_html_e('End Date', 'church-programme-dashboard'); ?></label>
                                <input type="date" id="admin-filter-end-date" class="regular-text">
                            </div>
                        </div>
                        <button type="button" id="admin-apply-filter" class="button button-primary">
                            <?php esc_html_e('Apply Filter', 'church-programme-dashboard'); ?>
                        </button>
                        <button type="button" id="admin-clear-filter" class="button">
                            <?php esc_html_e('Clear Filter', 'church-programme-dashboard'); ?>
                        </button>
                        <button type="button" id="admin-add-programme" class="button button-secondary" style="float: right;">
                            <?php esc_html_e('+ Add New Programme', 'church-programme-dashboard'); ?>
                        </button>
                    </div>

                    <!-- Programmes List -->
                    <div id="admin-programmes-list" style="background: #fff; padding: 20px; border: 1px solid #ddd; border-radius: 4px;">
                        <p style="text-align: center; color: #666;">
                            <?php esc_html_e('Loading programmes...', 'church-programme-dashboard'); ?>
                        </p>
                    </div>

                    <!-- Add/Edit Programme Modal -->
                    <div id="admin-programme-modal" style="display: none; position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0,0,0,0.7); z-index: 100000; align-items: center; justify-content: center;">
                        <div style="background: #fff; padding: 30px; border-radius: 8px; max-width: 800px; width: 90%; max-height: 90vh; overflow-y: auto;">
                            <h2 id="admin-modal-title"><?php esc_html_e('Add Programme', 'church-programme-dashboard'); ?></h2>

                            <form id="admin-programme-form">
                                <input type="hidden" id="admin-programme-id" value="">

                                <table class="form-table">
                                    <tr>
                                        <th scope="row">
                                            <label for="admin-programme-type"><?php esc_html_e('Programme Type', 'church-programme-dashboard'); ?> *</label>
                                        </th>
                                        <td>
                                            <select id="admin-programme-type" class="regular-text" required>
                                                <option value=""><?php esc_html_e('Select Type', 'church-programme-dashboard'); ?></option>
                                                <option value="jingiaseng_1pm"><?php esc_html_e('JINGIASENG 1:00 Baje', 'church-programme-dashboard'); ?></option>
                                                <option value="miet_balang"><?php esc_html_e('MIET BALANG', 'church-programme-dashboard'); ?></option>
                                                <option value="jingiaseng_samla"><?php esc_html_e('JINGIASENG SAMLA', 'church-programme-dashboard'); ?></option>
                                                <option value="jingiaseng_khynnah"><?php esc_html_e('JINGIASENG KHYNNAH', 'church-programme-dashboard'); ?></option>
                                                <option value="jingiaseng_iing"><?php esc_html_e('JINGIASENG IING', 'church-programme-dashboard'); ?></option>
                                            </select>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th scope="row">
                                            <label for="admin-programme-date"><?php esc_html_e('Date', 'church-programme-dashboard'); ?> *</label>
                                        </th>
                                        <td>
                                            <input type="date" id="admin-programme-date" class="regular-text" required>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th scope="row">
                                            <label for="admin-programme-time"><?php esc_html_e('Time', 'church-programme-dashboard'); ?> *</label>
                                        </th>
                                        <td>
                                            <input type="time" id="admin-programme-time" class="regular-text" required>
                                        </td>
                                    </tr>
                                </table>

                                <div id="admin-programme-fields"></div>

                                <p class="submit">
                                    <button type="submit" class="button button-primary button-large">
                                        <?php esc_html_e('Save Programme', 'church-programme-dashboard'); ?>
                                    </button>
                                    <button type="button" id="admin-modal-close" class="button button-large">
                                        <?php esc_html_e('Cancel', 'church-programme-dashboard'); ?>
                                    </button>
                                </p>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Notice Board -->
            <div id="notice" class="cpd-tab-content">
                <h2><?php esc_html_e('Notice Board Settings', 'church-programme-dashboard'); ?></h2>

                <table class="form-table">
                    <tr>
                        <th scope="row">
                            <label for="notice_board_enabled"><?php esc_html_e('Enable Notice Board', 'church-programme-dashboard'); ?></label>
                        </th>
                        <td>
                            <label>
                                <input type="checkbox" id="notice_board_enabled" name="notice_board_enabled"
                                       value="1" <?php checked($settings['notice']['enabled'], '1'); ?>>
                                <?php esc_html_e('Show notice board on dashboard', 'church-programme-dashboard'); ?>
                            </label>
                        </td>
                    </tr>

                    <tr>
                        <th scope="row">
                            <label for="notice_board_title"><?php esc_html_e('Notice Board Title', 'church-programme-dashboard'); ?></label>
                        </th>
                        <td>
                            <input type="text" id="notice_board_title" name="notice_board_title"
                                   value="<?php echo esc_attr($settings['notice']['title']); ?>"
                                   class="regular-text">
                        </td>
                    </tr>

                    <tr>
                        <th scope="row">
                            <label for="notice_board_content"><?php esc_html_e('Notice Board Content', 'church-programme-dashboard'); ?></label>
                        </th>
                        <td>
                            <?php
                            wp_editor($settings['notice']['content'], 'notice_board_content', array(
                                'textarea_name' => 'notice_board_content',
                                'textarea_rows' => 10,
                                'media_buttons' => false,
                                'teeny' => true,
                            ));
                            ?>
                        </td>
                    </tr>
                </table>
            </div>

            <!-- Carousel Animation Settings -->
            <div id="carousel" class="cpd-tab-content">
                <h2><?php esc_html_e('Carousel Animation Settings', 'church-programme-dashboard'); ?></h2>
                <p><?php esc_html_e('Customize the animation effects, speed, and timing for the programme carousel.', 'church-programme-dashboard'); ?></p>
                
                <table class="form-table">
                    <tr>
                        <th scope="row">
                            <label for="carousel_animation_type"><?php esc_html_e('Animation Type', 'church-programme-dashboard'); ?></label>
                        </th>
                        <td>
                            <select id="carousel_animation_type" name="carousel_animation_type" class="regular-text">
                                <option value="slide" <?php selected($settings['carousel']['animation_type'], 'slide'); ?>>
                                    <?php esc_html_e('Slide (Default)', 'church-programme-dashboard'); ?>
                                </option>
                                <option value="fade" <?php selected($settings['carousel']['animation_type'], 'fade'); ?>>
                                    <?php esc_html_e('Fade', 'church-programme-dashboard'); ?>
                                </option>
                                <option value="zoom" <?php selected($settings['carousel']['animation_type'], 'zoom'); ?>>
                                    <?php esc_html_e('Zoom', 'church-programme-dashboard'); ?>
                                </option>
                                <option value="flip" <?php selected($settings['carousel']['animation_type'], 'flip'); ?>>
                                    <?php esc_html_e('3D Flip', 'church-programme-dashboard'); ?>
                                </option>
                                <option value="continuous-scroll" <?php selected($settings['carousel']['animation_type'], 'continuous-scroll'); ?>>
                                    <?php esc_html_e('Continuous Scroll', 'church-programme-dashboard'); ?>
                                </option>
                            </select>
                            <p class="description"><?php esc_html_e('Choose the animation effect for carousel transitions.', 'church-programme-dashboard'); ?></p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="carousel_direction"><?php esc_html_e('Slide Direction', 'church-programme-dashboard'); ?></label>
                        </th>
                        <td>
                            <select id="carousel_direction" name="carousel_direction" class="regular-text">
                                <option value="right-to-left" <?php selected($settings['carousel']['direction'], 'right-to-left'); ?>>
                                    <?php esc_html_e('Right to Left', 'church-programme-dashboard'); ?>
                                </option>
                                <option value="left-to-right" <?php selected($settings['carousel']['direction'], 'left-to-right'); ?>>
                                    <?php esc_html_e('Left to Right', 'church-programme-dashboard'); ?>
                                </option>
                                <option value="top-to-bottom" <?php selected($settings['carousel']['direction'], 'top-to-bottom'); ?>>
                                    <?php esc_html_e('Top to Bottom', 'church-programme-dashboard'); ?>
                                </option>
                                <option value="bottom-to-top" <?php selected($settings['carousel']['direction'], 'bottom-to-top'); ?>>
                                    <?php esc_html_e('Bottom to Top', 'church-programme-dashboard'); ?>
                                </option>
                            </select>
                            <p class="description"><?php esc_html_e('Choose the direction for slide animations.', 'church-programme-dashboard'); ?></p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="carousel_animation_speed"><?php esc_html_e('Animation Speed (ms)', 'church-programme-dashboard'); ?></label>
                        </th>
                        <td>
                            <input type="number" id="carousel_animation_speed" name="carousel_animation_speed" 
                                   value="<?php echo esc_attr($settings['carousel']['animation_speed']); ?>" 
                                   class="regular-text" min="100" max="2000" step="100">
                            <p class="description"><?php esc_html_e('Animation duration in milliseconds (100-2000ms). Lower values = faster animation.', 'church-programme-dashboard'); ?></p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="carousel_auto_interval"><?php esc_html_e('Auto-advance Interval (ms)', 'church-programme-dashboard'); ?></label>
                        </th>
                        <td>
                            <input type="number" id="carousel_auto_interval" name="carousel_auto_interval" 
                                   value="<?php echo esc_attr($settings['carousel']['auto_interval']); ?>" 
                                   class="regular-text" min="1000" max="30000" step="1000">
                            <p class="description"><?php esc_html_e('Time between automatic slide transitions in milliseconds (1000-30000ms).', 'church-programme-dashboard'); ?></p>
                        </td>
                    </tr>
                </table>
                
                <div class="cpd-notice cpd-notice-info">
                    <p><strong><?php esc_html_e('Animation Preview:', 'church-programme-dashboard'); ?></strong></p>
                    <p><?php esc_html_e('Changes will take effect immediately on the dashboard. You can test different animations by visiting the dashboard after saving.', 'church-programme-dashboard'); ?></p>
                </div>
            </div>

            <!-- Cookie Consent -->
            <div id="cookie" class="cpd-tab-content">
                <h2><?php esc_html_e('Cookie Consent Settings', 'church-programme-dashboard'); ?></h2>

                <table class="form-table">
                    <tr>
                        <th scope="row">
                            <label for="cookie_consent_text"><?php esc_html_e('Consent Message', 'church-programme-dashboard'); ?></label>
                        </th>
                        <td>
                            <textarea id="cookie_consent_text" name="cookie_consent_text"
                                      rows="3" class="large-text"><?php echo esc_textarea($settings['cookie']['consent_text']); ?></textarea>
                        </td>
                    </tr>

                    <tr>
                        <th scope="row">
                            <label for="cookie_consent_button"><?php esc_html_e('Button Text', 'church-programme-dashboard'); ?></label>
                        </th>
                        <td>
                            <input type="text" id="cookie_consent_button" name="cookie_consent_button"
                                   value="<?php echo esc_attr($settings['cookie']['consent_button']); ?>"
                                   class="regular-text">
                        </td>
                    </tr>
                </table>
            </div>

            <!-- Quick Links -->
            <div id="links" class="cpd-tab-content">
                <h2><?php esc_html_e('Quick Links', 'church-programme-dashboard'); ?></h2>

                <table class="form-table">
                    <tr>
                        <th scope="row">
                            <?php esc_html_e('Dashboard URL', 'church-programme-dashboard'); ?>
                        </th>
                        <td>
                            <a href="<?php echo esc_url(CPD_Subdomain::get_dashboard_url()); ?>" target="_blank" class="button">
                                <?php esc_html_e('View Dashboard', 'church-programme-dashboard'); ?>
                            </a>
                            <p class="description">
                                <code><?php echo esc_html(CPD_Subdomain::get_dashboard_url()); ?></code>
                            </p>
                        </td>
                    </tr>

                    <tr>
                        <th scope="row">
                            <?php esc_html_e('Editor URL', 'church-programme-dashboard'); ?>
                        </th>
                        <td>
                            <a href="<?php echo esc_url(CPD_Subdomain::get_editor_url()); ?>" target="_blank" class="button">
                                <?php esc_html_e('View Editor', 'church-programme-dashboard'); ?>
                            </a>
                            <p class="description">
                                <code><?php echo esc_html(CPD_Subdomain::get_editor_url()); ?></code>
                            </p>
                        </td>
                    </tr>
                </table>
            </div>

            <p class="submit">
                <button type="submit" class="button button-primary button-large">
                    <?php esc_html_e('Save All Settings', 'church-programme-dashboard'); ?>
                </button>
            </p>
        </form>
    </div>
</div>
