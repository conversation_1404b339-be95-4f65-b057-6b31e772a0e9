<?php
/**
 * REST API Handler Class
 * 
 * Handles all REST API endpoints for the plugin
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

class CPD_REST_API {
    
    /**
     * Initialize
     */
    public static function init() {
        add_action('rest_api_init', array(__CLASS__, 'register_routes'));
        add_action('rest_api_init', array(__CLASS__, 'add_cors_headers'));
    }

    /**
     * Add CORS headers for subdomain access
     */
    public static function add_cors_headers() {
        remove_filter('rest_pre_serve_request', 'rest_send_cors_headers');
        add_filter('rest_pre_serve_request', function($value) {
            header('Access-Control-Allow-Origin: *');
            header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
            header('Access-Control-Allow-Headers: Content-Type, Authorization, X-WP-Nonce');
            header('Access-Control-Allow-Credentials: true');
            return $value;
        });
    }
    
    /**
     * Register REST API routes
     */
    public static function register_routes() {
        // Public routes
        register_rest_route('cpd/v1', '/programmes/(?P<year>\d+)/(?P<month>\d+)', array(
            'methods' => 'GET',
            'callback' => array(__CLASS__, 'get_programmes_by_month'),
            'permission_callback' => '__return_true',
        ));
        
        register_rest_route('cpd/v1', '/programmes/date/(?P<date>[\d-]+)', array(
            'methods' => 'GET',
            'callback' => array(__CLASS__, 'get_programmes_by_date'),
            'permission_callback' => '__return_true',
        ));
        
        register_rest_route('cpd/v1', '/programmes/upcoming', array(
            'methods' => 'GET',
            'callback' => array(__CLASS__, 'get_upcoming_programmes'),
            'permission_callback' => '__return_true',
        ));
        
        register_rest_route('cpd/v1', '/settings/public', array(
            'methods' => 'GET',
            'callback' => array(__CLASS__, 'get_public_settings'),
            'permission_callback' => '__return_true',
        ));
        
        // Editor routes (require authentication)
        register_rest_route('cpd/v1', '/editor/login', array(
            'methods' => 'POST',
            'callback' => array(__CLASS__, 'editor_login'),
            'permission_callback' => '__return_true',
        ));
        
        register_rest_route('cpd/v1', '/editor/logout', array(
            'methods' => 'POST',
            'callback' => array(__CLASS__, 'editor_logout'),
            'permission_callback' => array(__CLASS__, 'check_editor_permission'),
        ));
        
        register_rest_route('cpd/v1', '/editor/check-auth', array(
            'methods' => 'GET',
            'callback' => array(__CLASS__, 'check_editor_auth'),
            'permission_callback' => '__return_true',
        ));
        
        register_rest_route('cpd/v1', '/editor/programmes', array(
            'methods' => 'GET',
            'callback' => array(__CLASS__, 'get_all_programmes'),
            'permission_callback' => array(__CLASS__, 'check_editor_permission'),
        ));
        
        register_rest_route('cpd/v1', '/editor/programmes', array(
            'methods' => 'POST',
            'callback' => array(__CLASS__, 'create_programme'),
            'permission_callback' => array(__CLASS__, 'check_editor_permission'),
        ));

        register_rest_route('cpd/v1', '/editor/programmes/(?P<id>\d+)', array(
            'methods' => 'GET',
            'callback' => array(__CLASS__, 'get_single_programme'),
            'permission_callback' => array(__CLASS__, 'check_editor_permission'),
        ));

        register_rest_route('cpd/v1', '/editor/programmes/(?P<id>\d+)', array(
            'methods' => 'PUT',
            'callback' => array(__CLASS__, 'update_programme'),
            'permission_callback' => array(__CLASS__, 'check_editor_permission'),
        ));

        register_rest_route('cpd/v1', '/editor/programmes/(?P<id>\d+)', array(
            'methods' => 'DELETE',
            'callback' => array(__CLASS__, 'delete_programme'),
            'permission_callback' => array(__CLASS__, 'check_editor_permission'),
        ));
        
        register_rest_route('cpd/v1', '/editor/programmes/bulk-delete', array(
            'methods' => 'POST',
            'callback' => array(__CLASS__, 'bulk_delete_programmes'),
            'permission_callback' => array(__CLASS__, 'check_editor_permission'),
        ));
        
        // AI extraction routes
        register_rest_route('cpd/v1', '/ai/models/(?P<provider>[a-zA-Z0-9_-]+)', array(
            'methods' => 'GET',
            'callback' => array(__CLASS__, 'get_ai_models'),
            'permission_callback' => array(__CLASS__, 'check_editor_permission'),
        ));
        
        register_rest_route('cpd/v1', '/ai/extract', array(
            'methods' => 'POST',
            'callback' => array(__CLASS__, 'extract_from_image'),
            'permission_callback' => array(__CLASS__, 'check_editor_permission'),
        ));
    }
    
    /**
     * Check editor permission
     */
    public static function check_editor_permission() {
        // Allow WordPress admins
        if (current_user_can('manage_options')) {
            return true;
        }

        // Allow editor users
        return CPD_Auth::is_editor_logged_in();
    }
    
    /**
     * Get programmes by month
     */
    public static function get_programmes_by_month($request) {
        $year = $request->get_param('year');
        $month = $request->get_param('month');
        
        $start_date = sprintf('%04d-%02d-01', $year, $month);
        $end_date = date('Y-m-t', strtotime($start_date));
        
        $programmes = CPD_Database::get_programmes_by_date_range($start_date, $end_date);
        
        // Format the data
        $formatted = array();
        foreach ($programmes as $programme) {
            $formatted[] = array(
                'id' => $programme->id,
                'type' => $programme->programme_type,
                'date' => $programme->programme_date,
                'time' => $programme->programme_time,
                'data' => json_decode($programme->programme_data, true),
            );
        }
        
        return rest_ensure_response($formatted);
    }
    
    /**
     * Get programmes by specific date
     */
    public static function get_programmes_by_date($request) {
        $date = $request->get_param('date');
        
        $programmes = CPD_Database::get_programmes_by_date($date);
        
        // Format the data
        $formatted = array();
        foreach ($programmes as $programme) {
            $formatted[] = array(
                'id' => $programme->id,
                'type' => $programme->programme_type,
                'date' => $programme->programme_date,
                'time' => $programme->programme_time,
                'data' => json_decode($programme->programme_data, true),
            );
        }
        
        return rest_ensure_response($formatted);
    }
    
    /**
     * Get upcoming programmes for current week
     */
    public static function get_upcoming_programmes($request) {
        $today = current_time('Y-m-d');
        $end_of_week = date('Y-m-d', strtotime($today . ' +7 days'));
        
        $programmes = CPD_Database::get_programmes_by_date_range($today, $end_of_week);
        
        // Format and organize by date
        $formatted = array();
        foreach ($programmes as $programme) {
            $date = $programme->programme_date;
            if (!isset($formatted[$date])) {
                $formatted[$date] = array();
            }
            
            $formatted[$date][] = array(
                'id' => $programme->id,
                'type' => $programme->programme_type,
                'date' => $programme->programme_date,
                'time' => $programme->programme_time,
                'data' => json_decode($programme->programme_data, true),
            );
        }
        
        return rest_ensure_response($formatted);
    }
    
    /**
     * Get public settings
     */
    public static function get_public_settings($request) {
        $settings = array(
            'dashboard_title' => get_option('cpd_dashboard_title'),
            'header_bg_color' => get_option('cpd_header_bg_color'),
            'header_bg_image' => get_option('cpd_header_bg_image'),
            'primary_color' => get_option('cpd_primary_color'),
            'secondary_color' => get_option('cpd_secondary_color'),
            'accent_color' => get_option('cpd_accent_color'),
            'text_color' => get_option('cpd_text_color'),
            'notice_board_enabled' => get_option('cpd_notice_board_enabled'),
            'notice_board_title' => get_option('cpd_notice_board_title'),
            'notice_board_content' => get_option('cpd_notice_board_content'),
            'labels' => array(
                'miet_balang' => get_option('cpd_label_miet_balang'),
                'miet_balang_time' => get_option('cpd_label_miet_balang_time'),
                'jingiaseng_samla' => get_option('cpd_label_jingiaseng_samla'),
                'jingiaseng_samla_time' => get_option('cpd_label_jingiaseng_samla_time'),
                'jingiaseng_1pm' => get_option('cpd_label_jingiaseng_1pm'),
                'jingiaseng_1pm_time' => get_option('cpd_label_jingiaseng_1pm_time'),
                'jingiaseng_khynnah' => get_option('cpd_label_jingiaseng_khynnah'),
                'jingiaseng_khynnah_time' => get_option('cpd_label_jingiaseng_khynnah_time'),
                'jingiaseng_iing' => get_option('cpd_label_jingiaseng_iing'),
                'jingiaseng_iing_time' => get_option('cpd_label_jingiaseng_iing_time'),
            ),
            'cookie_consent_text' => get_option('cpd_cookie_consent_text'),
            'cookie_consent_button' => get_option('cpd_cookie_consent_button'),
        );
        
        return rest_ensure_response($settings);
    }
    
    /**
     * Editor login
     */
    public static function editor_login($request) {
        $username = $request->get_param('username');
        $password = $request->get_param('password');
        
        if (empty($username) || empty($password)) {
            return new WP_Error('missing_credentials', __('Username and password are required.', 'church-programme-dashboard'), array('status' => 400));
        }
        
        $result = CPD_Auth::login($username, $password);
        
        if (is_wp_error($result)) {
            return $result;
        }
        
        return rest_ensure_response(array(
            'success' => true,
            'message' => __('Login successful.', 'church-programme-dashboard'),
            'user' => CPD_Auth::get_current_editor_user(),
        ));
    }
    
    /**
     * Editor logout
     */
    public static function editor_logout($request) {
        CPD_Auth::logout();
        
        return rest_ensure_response(array(
            'success' => true,
            'message' => __('Logout successful.', 'church-programme-dashboard'),
        ));
    }
    
    /**
     * Check editor authentication
     */
    public static function check_editor_auth($request) {
        if (CPD_Auth::is_editor_logged_in()) {
            return rest_ensure_response(array(
                'authenticated' => true,
                'user' => CPD_Auth::get_current_editor_user(),
            ));
        }
        
        return rest_ensure_response(array(
            'authenticated' => false,
        ));
    }
    
    /**
     * Get all programmes (for editor)
     */
    public static function get_all_programmes($request) {
        $start_date = $request->get_param('start_date');
        $end_date = $request->get_param('end_date');
        $type = $request->get_param('type');

        // If no dates provided, get ALL programmes (no date filter)
        if (!$start_date && !$end_date) {
            $programmes = CPD_Database::get_all_programmes($type);
        } else {
            // If only one date provided, use a wide range
            if (!$start_date) {
                $start_date = date('Y-01-01'); // Start of current year
            }
            if (!$end_date) {
                $end_date = date('Y-12-31'); // End of current year
            }

            $programmes = CPD_Database::get_programmes_by_date_range($start_date, $end_date, $type);
        }

        // Format the data
        $formatted = array();
        foreach ($programmes as $programme) {
            $formatted[] = array(
                'id' => $programme->id,
                'programme_type' => $programme->programme_type,
                'programme_date' => $programme->programme_date,
                'programme_time' => $programme->programme_time,
                'programme_data' => $programme->programme_data,
                'created_at' => $programme->created_at,
                'updated_at' => $programme->updated_at,
            );
        }

        return rest_ensure_response(array(
            'success' => true,
            'programmes' => $formatted,
            'count' => count($formatted),
        ));
    }
    
    /**
     * Get single programme by ID
     */
    public static function get_single_programme($request) {
        $id = $request->get_param('id');

        if (!$id) {
            return new WP_Error('missing_id', __('Programme ID is required.', 'church-programme-dashboard'), array('status' => 400));
        }

        $programme = CPD_Database::get_programme($id);

        if (!$programme) {
            return new WP_Error('not_found', __('Programme not found.', 'church-programme-dashboard'), array('status' => 404));
        }

        // Format the data
        $formatted = array(
            'id' => $programme->id,
            'programme_type' => $programme->programme_type,
            'programme_date' => $programme->programme_date,
            'programme_time' => $programme->programme_time,
            'programme_data' => $programme->programme_data,
            'created_at' => $programme->created_at,
            'updated_at' => $programme->updated_at,
        );

        return rest_ensure_response(array(
            'success' => true,
            'programme' => $formatted,
        ));
    }

    /**
     * Create programme
     */
    public static function create_programme($request) {
        $data = array(
            'programme_type' => $request->get_param('type'),
            'programme_date' => $request->get_param('date'),
            'programme_time' => $request->get_param('time'),
            'programme_data' => $request->get_param('data'),
        );

        $id = CPD_Database::insert_programme($data);

        if ($id) {
            return rest_ensure_response(array(
                'success' => true,
                'id' => $id,
                'message' => __('Programme created successfully.', 'church-programme-dashboard'),
            ));
        }

        return new WP_Error('create_failed', __('Failed to create programme.', 'church-programme-dashboard'), array('status' => 500));
    }
    
    /**
     * Update programme
     */
    public static function update_programme($request) {
        $id = $request->get_param('id');
        
        $data = array();
        if ($request->get_param('type')) {
            $data['programme_type'] = $request->get_param('type');
        }
        if ($request->get_param('date')) {
            $data['programme_date'] = $request->get_param('date');
        }
        if ($request->get_param('time')) {
            $data['programme_time'] = $request->get_param('time');
        }
        if ($request->get_param('data')) {
            $data['programme_data'] = $request->get_param('data');
        }
        
        $result = CPD_Database::update_programme($id, $data);
        
        if ($result !== false) {
            return rest_ensure_response(array(
                'success' => true,
                'message' => __('Programme updated successfully.', 'church-programme-dashboard'),
            ));
        }
        
        return new WP_Error('update_failed', __('Failed to update programme.', 'church-programme-dashboard'), array('status' => 500));
    }
    
    /**
     * Delete programme
     */
    public static function delete_programme($request) {
        $id = $request->get_param('id');
        
        $result = CPD_Database::delete_programme($id);
        
        if ($result) {
            return rest_ensure_response(array(
                'success' => true,
                'message' => __('Programme deleted successfully.', 'church-programme-dashboard'),
            ));
        }
        
        return new WP_Error('delete_failed', __('Failed to delete programme.', 'church-programme-dashboard'), array('status' => 500));
    }
    
    /**
     * Bulk delete programmes
     */
    public static function bulk_delete_programmes($request) {
        $ids = $request->get_param('ids');
        $type = $request->get_param('type');
        
        if ($type) {
            $result = CPD_Database::delete_programmes_by_type($type);
        } elseif ($ids && is_array($ids)) {
            $result = 0;
            foreach ($ids as $id) {
                if (CPD_Database::delete_programme($id)) {
                    $result++;
                }
            }
        } else {
            return new WP_Error('invalid_params', __('Invalid parameters.', 'church-programme-dashboard'), array('status' => 400));
        }
        
        return rest_ensure_response(array(
            'success' => true,
            'deleted' => $result,
            'message' => sprintf(__('%d programme(s) deleted successfully.', 'church-programme-dashboard'), $result),
        ));
    }
    
    /**
     * Get AI models
     */
    public static function get_ai_models($request) {
        $provider = $request->get_param('provider');
        $api_key = $request->get_header('X-API-Key');

        if (empty($api_key)) {
            return new WP_Error('missing_api_key', __('API key is required.', 'church-programme-dashboard'), array('status' => 400));
        }

        require_once CPD_PLUGIN_DIR . 'includes/class-cpd-ai.php';

        $models = CPD_AI::fetch_models($provider, $api_key);

        if (is_wp_error($models)) {
            return $models;
        }

        return rest_ensure_response(array('models' => $models));
    }

    /**
     * Extract from image
     */
    public static function extract_from_image($request) {
        $provider = $request->get_param('provider');
        $model = $request->get_param('model');
        $image_url = $request->get_param('image_url');
        $programme_type = $request->get_param('programme_type');
        $api_key = $request->get_header('X-API-Key');

        if (empty($api_key)) {
            return new WP_Error('missing_api_key', __('API key is required.', 'church-programme-dashboard'), array('status' => 400));
        }

        if (empty($provider) || empty($model) || empty($image_url) || empty($programme_type)) {
            return new WP_Error('missing_params', __('All parameters are required.', 'church-programme-dashboard'), array('status' => 400));
        }

        require_once CPD_PLUGIN_DIR . 'includes/class-cpd-ai.php';

        // Extract data
        $result = CPD_AI::extract_from_image($provider, $model, $api_key, $image_url, $programme_type);

        if (is_wp_error($result)) {
            // Save error to history
            CPD_Database::save_extraction_history(array(
                'programme_type' => $programme_type,
                'image_url' => $image_url,
                'ai_provider' => $provider,
                'ai_model' => $model,
                'extraction_status' => 'failed',
                'error_message' => $result->get_error_message(),
                'user_id' => CPD_Auth::is_editor_logged_in() ? $_SESSION['cpd_editor_user_id'] : null,
            ));

            return $result;
        }

        // Save to history
        CPD_Database::save_extraction_history(array(
            'programme_type' => $programme_type,
            'image_url' => $image_url,
            'ai_provider' => $provider,
            'ai_model' => $model,
            'extraction_status' => 'success',
            'extracted_data' => $result,
            'user_id' => CPD_Auth::is_editor_logged_in() ? $_SESSION['cpd_editor_user_id'] : null,
        ));

        // Save programmes to database
        if (isset($result['programmes']) && is_array($result['programmes'])) {
            $saved_count = 0;
            foreach ($result['programmes'] as $programme) {
                $id = CPD_Database::insert_programme(array(
                    'programme_type' => $programme['type'],
                    'programme_date' => $programme['date'],
                    'programme_time' => $programme['time'],
                    'programme_data' => $programme['data'],
                ));

                if ($id) {
                    $saved_count++;
                }
            }

            return rest_ensure_response(array(
                'success' => true,
                'message' => sprintf(__('%d programme(s) extracted and saved successfully.', 'church-programme-dashboard'), $saved_count),
                'data' => $result,
            ));
        }

        return rest_ensure_response(array(
            'success' => true,
            'message' => __('Data extracted successfully.', 'church-programme-dashboard'),
            'data' => $result,
        ));
    }
}

