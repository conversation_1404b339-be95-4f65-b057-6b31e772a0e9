# Secure Admin Panel Installation Guide

## Overview

This guide explains how to install and use the secure admin panel system for the Church Programme Dashboard. The system provides a secure, standalone admin interface that works even when the WordPress admin panel has issues.

## 🚀 Quick Installation Steps

### Step 1: Install the Main Plugin

1. **Upload the plugin folder** to your WordPress installation:
   ```
   wp-content/plugins/church-programme-dashboard/
   ```

2. **Activate the plugin** in WordPress admin:
   - Go to **Plugins** > **Installed Plugins**
   - Find "Church Programme Dashboard"
   - Click **Activate**

### Step 2: Upload Secure Admin Files

Upload these two files to your **WordPress root directory** (same level as `wp-config.php`):

1. **`secure-admin-panel-simple.php`** - Secure login gateway
2. **`complete-admin-panel.php`** - Complete settings management

**Upload Location:**
```
/ (WordPress root directory)
├── wp-config.php
├── secure-admin-panel-simple.php  ← Upload here
├── complete-admin-panel.php       ← Upload here
├── wp-content/
└── wp-admin/
```

### Step 3: Access the Secure System

1. **Go to your secure admin panel:**
   ```
   https://yoursite.com/secure-admin-panel-simple.php
   ```

2. **First-time login credentials:**
   - **Username:** `admin`
   - **Password:** `admin123`

3. **Change your password immediately:**
   - Go to **Users** tab
   - Use "Change Your Password" form
   - Enter current password: `admin123`
   - Enter new password twice
   - Click "Change Password"

## 🔐 Security Features

### Authentication System
- **Password Hashing**: All passwords securely hashed using PHP `password_hash()`
- **Session Management**: Secure session handling with logout functionality
- **Role-based Access**: Different user roles (Administrator, Editor, User)
- **Automatic Redirect**: Direct access to complete panel redirects to login

### User Management
- **Add Users**: Create new users with different roles
- **Delete Users**: Remove users (except main admin)
- **Change Passwords**: Secure password change with current password verification
- **User Roles**:
  - **Administrator**: Full access to all settings
  - **Editor**: Limited access (future feature)
  - **User**: Basic access (future feature)

### File Security
- **No Default Credentials Displayed**: Clean interface without exposing credentials
- **Session Protection**: Prevents unauthorized access
- **Role Verification**: Only administrators can access complete admin panel

## 📋 Complete Step-by-Step Installation

### 1. Plugin Installation

```bash
# Upload the entire plugin folder to:
wp-content/plugins/church-programme-dashboard/
```

**Files to upload:**
- All files from the `church-programme-dashboard` folder
- Maintain the folder structure

### 2. Secure Admin Files Upload

**Upload these files to WordPress root:**

| File | Purpose | Location |
|------|---------|----------|
| `secure-admin-panel-simple.php` | Secure login gateway | WordPress root |
| `complete-admin-panel.php` | Complete settings | WordPress root |

**Example upload paths:**
- `/home/<USER>/public_html/secure-admin-panel-simple.php`
- `/home/<USER>/public_html/complete-admin-panel.php`

### 3. First-Time Setup

1. **Access Secure Admin:**
   ```
   https://yoursite.com/secure-admin-panel-simple.php
   ```

2. **Initial Login:**
   - Username: `admin`
   - Password: `admin123`

3. **Immediate Security Actions:**
   - Go to **Users** tab
   - Click "Change Your Password"
   - Enter current password: `admin123`
   - Enter new strong password twice
   - Click "Change Password"

### 4. Configure Your Dashboard

1. **From Secure Admin Panel:**
   - Go to **Dashboard** tab
   - Click "Open Complete Panel"

2. **Configure Settings (Tabs):**

   - **General**: Dashboard title, header colors
   - **Subdomain**: Dashboard and editor subdomains
   - **Colors & Styling**: Theme colors
   - **Programme Labels**: Customize programme names and times
   - **Notice Board**: Enable/disable, title, rich content
   - **Carousel**: Animation type, speed, interval
   - **Cookie Consent**: Message and button text

## 🔧 Configuration Details

### Carousel Settings (Your Original Question)

**To increase carousel slide interval:**

1. Go to **Carousel** tab in complete admin panel
2. Find "Auto-advance Interval (ms)"
3. **Current setting**: 5000ms (5 seconds)
4. **Increase to**: 8000ms (8 seconds) or higher
5. Click "Save Carousel Settings"

**Available intervals:** 1000ms to 30000ms (1-30 seconds)

### Notice Board Features

- **Rich Text Editor**: Bold, italic, underline, colors
- **Lists**: Bullet and numbered lists
- **Links**: Insert hyperlinks
- **Images**: Upload and insert images
- **HTML/Visual Toggle**: Switch between HTML and visual editing

### Subdomain Configuration

**Default setup:**
- Dashboard: `https://dashboard.yoursite.com`
- Editor: `https://editor.yoursite.com`

**To change:**
1. Go to **Subdomain** tab
2. Update subdomain names
3. Save settings
4. Configure DNS records for subdomains

## 🛡️ Security Best Practices

### Password Security
- ✅ Change default password immediately
- ✅ Use strong, unique passwords
- ✅ Enable password change for all users
- ✅ Regular password updates recommended

### File Security
- ✅ Keep admin files in secure location
- ✅ Regular backups of WordPress database
- ✅ Monitor access logs
- ✅ Consider deleting files after initial setup

### User Management
- ✅ Only give admin access to trusted users
- ✅ Use appropriate roles for different users
- ✅ Regularly review and clean up user accounts
- ✅ Delete unused accounts

## 🚨 Troubleshooting

### Common Issues

**1. "You do not have permission to access this page"**
- **Cause**: Trying to access complete admin panel without login
- **Solution**: Always start from `secure-admin-panel-simple.php`

**2. Login not working**
- **Cause**: Incorrect credentials or session issues
- **Solution**: 
  - Verify username: `admin`
  - Verify password (if changed)
  - Clear browser cookies
  - Try different browser

**3. Settings not saving**
- **Cause**: File permissions or database issues
- **Solution**:
  - Check file permissions (644 for PHP files)
  - Verify WordPress database connection
  - Check PHP error logs

**4. Carousel interval not changing**
- **Cause**: JavaScript cache or incorrect setting
- **Solution**:
  - Clear browser cache
  - Verify setting saved in database
  - Check browser console for errors

### File Permissions

**Recommended permissions:**
- PHP files: `644` (read/write for owner, read for others)
- WordPress root: `755` for directories
- Never use `777` for security

### Database Requirements

The system uses WordPress options API:
- Requires `wp_options` table access
- Stores settings as serialized data
- Uses WordPress database security

## 📞 Support Information

### Getting Help

1. **Check Browser Console** (F12) for errors
2. **Check WordPress Error Logs**
3. **Verify File Locations**
4. **Test with Default Credentials**

### Emergency Access

If you lose access:
1. **Database reset**: Delete `cpd_custom_users` option from `wp_options` table
2. **Re-upload files**: Overwrite with fresh copies
3. **Use default credentials**: `admin` / `admin123`

### Backup Recommendations

**Before making changes:**
- Backup WordPress database
- Export current settings
- Note down custom configurations

**Regular backups:**
- WordPress database
- Plugin files
- Custom admin panel files

## 🎯 Quick Reference

### URLs to Remember
- **Secure Login**: `https://yoursite.com/secure-admin-panel-simple.php`
- **Complete Settings**: `https://yoursite.com/complete-admin-panel.php`
- **Public Dashboard**: `https://dashboard.yoursite.com`
- **Editor**: `https://editor.yoursite.com`

### Default Credentials (First Time Only)
- **Username**: `admin`
- **Password**: `admin123`

### Important Security Steps
1. ✅ Change default password immediately
2. ✅ Upload files to correct locations
3. ✅ Use strong passwords for all users
4. ✅ Regular security reviews

## ✅ Completion Checklist

- [ ] Plugin uploaded and activated
- [ ] Secure admin files uploaded to WordPress root
- [ ] Successfully logged in with default credentials
- [ ] Changed admin password
- [ ] Configured carousel interval (8000ms+ recommended)
- [ ] Tested notice board functionality
- [ ] Verified dashboard displays correctly
- [ ] Created additional users (if needed)
- [ ] Performed security review

---

**🎉 Congratulations!** Your secure admin panel system is now installed and ready to use.

For ongoing maintenance, remember to:
- Regularly update passwords
- Monitor user access
- Keep backups current
- Review security settings periodically

**Need help?** Check browser console for errors and verify file locations first.
