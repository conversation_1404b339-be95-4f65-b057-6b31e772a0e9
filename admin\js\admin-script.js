/**
 * Admin JavaScript - Rebuilt from Scratch
 */

(function($) {
    'use strict';
    
    console.log('=== CPD Admin Script Loading ===');
    console.log('jQuery:', typeof jQuery !== 'undefined' ? 'Loaded' : 'NOT LOADED');
    console.log('cpdAdmin:', typeof cpdAdmin !== 'undefined' ? cpdAdmin : 'NOT DEFINED');
    
    $(document).ready(function() {
        console.log('=== Document Ready ===');
        
        // Initialize color pickers
        if (typeof $.fn.wpColorPicker !== 'undefined') {
            $('.cpd-color-picker').wpColorPicker();
            console.log('Color pickers initialized');
        }
        
        // Tab navigation
        $('.nav-tab').on('click', function(e) {
            e.preventDefault();
            var target = $(this).attr('href');
            $('.nav-tab').removeClass('nav-tab-active');
            $(this).addClass('nav-tab-active');
            $('.cpd-tab-content').removeClass('cpd-tab-active');
            $(target).addClass('cpd-tab-active');
            console.log('Tab switched to:', target);
        });
        
        // Image upload
        $('.cpd-upload-image').on('click', function(e) {
            e.preventDefault();
            var button = $(this);
            var targetInput = $('#' + button.data('target'));
            
            if (typeof wp === 'undefined' || typeof wp.media === 'undefined') {
                alert('WordPress media library not available');
                return;
            }
            
            var mediaUploader = wp.media({
                title: 'Select Image',
                button: { text: 'Use this image' },
                multiple: false
            });
            
            mediaUploader.on('select', function() {
                var attachment = mediaUploader.state().get('selection').first().toJSON();
                targetInput.val(attachment.url);
            });
            
            mediaUploader.open();
        });
        
        // Remove image
        $('.cpd-remove-image').on('click', function(e) {
            e.preventDefault();
            var button = $(this);
            var targetInput = $('#' + button.data('target'));
            targetInput.val('');
            button.hide();
        });
        
        // SAVE SETTINGS - Main functionality
        console.log('=== Attaching Save Handler ===');
        
        $('#cpd-settings-form').on('submit', function(e) {
            console.log('=== FORM SUBMIT TRIGGERED ===');
            e.preventDefault();
            
            // Check if cpdAdmin exists
            if (typeof cpdAdmin === 'undefined') {
                console.error('ERROR: cpdAdmin is not defined!');
                alert('Error: Configuration not loaded. Please refresh the page.');
                return false;
            }
            
            console.log('cpdAdmin object:', cpdAdmin);
            
            var form = $(this);
            var submitButton = form.find('button[type="submit"]');
            var originalText = submitButton.text();
            
            console.log('Disabling button and changing text...');
            submitButton.prop('disabled', true).text('Saving...');
            
            // Collect form data
            var formData = {};
            form.find('input, textarea, select').each(function() {
                var input = $(this);
                var name = input.attr('name');
                
                if (name && name !== 'cpd_settings_nonce' && name !== '_wp_http_referer') {
                    if (input.attr('type') === 'checkbox') {
                        formData[name] = input.is(':checked') ? '1' : '0';
                    } else {
                        formData[name] = input.val();
                    }
                }
            });
            
            // Get TinyMCE content
            if (typeof tinyMCE !== 'undefined') {
                var editor = tinyMCE.get('notice_board_content');
                if (editor) {
                    formData['notice_board_content'] = editor.getContent();
                }
            }
            
            console.log('Form data collected:', formData);
            console.log('Sending AJAX request to:', cpdAdmin.ajaxUrl);
            
            // Try the original save method first, then fallback to direct method
            function trySaveSettings(action, data, attempt) {
                console.log('Attempt ' + attempt + ' with action:', action);
                
                $.ajax({
                    url: cpdAdmin.ajaxUrl,
                    type: 'POST',
                    data: data,
                    success: function(response) {
                        console.log('=== AJAX SUCCESS ===');
                        console.log('Response:', response);
                        
                        if (response.success) {
                            showNotice('success', response.data.message);
                            
                            // Reload if subdomain changed
                            if (formData.dashboard_subdomain || formData.editor_subdomain) {
                                setTimeout(function() {
                                    location.reload();
                                }, 1500);
                            }
                        } else {
                            showNotice('error', response.data.message || 'Failed to save settings.');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('=== AJAX ERROR ===');
                        console.error('Status:', status);
                        console.error('Error:', error);
                        console.error('Response:', xhr.responseText);
                        
                        // If first attempt failed, try direct method
                        if (attempt === 1) {
                            console.log('First attempt failed, trying direct method...');
                            var directData = $.extend({}, formData);
                            directData.action = 'cpd_direct_save_settings';
                            directData.nonce = cpdAdmin.nonce;
                            trySaveSettings('cpd_direct_save_settings', directData, 2);
                        } else {
                            showNotice('error', 'An error occurred while saving settings. Please try again.');
                        }
                    },
                    complete: function() {
                        if (attempt === 2) {
                            console.log('=== AJAX COMPLETE (Final) ===');
                            submitButton.prop('disabled', false).text(originalText);
                        }
                    }
                });
            }
            
            // Start with original method
            trySaveSettings('cpd_save_settings', {
                action: 'cpd_save_settings',
                nonce: cpdAdmin.nonce,
                settings: formData
            }, 1);
            
            return false;
        });
        
        console.log('Save handler attached');
        
        // Helper function to show notices
        function showNotice(type, message) {
            var notice = $('#cpd-save-notice');
            notice.removeClass('notice-success notice-error')
                  .addClass('notice notice-' + type)
                  .find('p').text(message);
            notice.slideDown();
            
            setTimeout(function() {
                notice.slideUp();
            }, 5000);
        }
        
        // Generate password
        $('#cpd-generate-password').on('click', function() {
            var password = generatePassword(16);
            $('#new_password').val(password);
            
            var passwordInput = $('#new_password');
            var originalType = passwordInput.attr('type');
            passwordInput.attr('type', 'text');
            
            setTimeout(function() {
                passwordInput.attr('type', originalType);
            }, 3000);
        });
        
        function generatePassword(length) {
            var charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
            var password = '';
            for (var i = 0; i < length; i++) {
                password += charset.charAt(Math.floor(Math.random() * charset.length));
            }
            return password;
        }
        
        console.log('=== Admin Script Loaded Successfully ===');
    });
    
})(jQuery);
