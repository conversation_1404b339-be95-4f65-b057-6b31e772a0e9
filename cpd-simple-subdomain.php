<?php
/**
 * Plugin Name: CPD Simple Subdomain Handler
 * Description: Clean subdomain handling for Church Programme Dashboard
 * Version: 1.0
 */


if (!defined('ABSPATH')) {
    exit;
}

class CPD_Simple_Subdomain_Handler {
    
    private $settings;
    
    public function __construct() {
        $this->settings = get_option('cpd_settings', array());
    }
    
    /**
     * Initialize subdomain handling
     */
    public function init() {
        // Hook in early to catch subdomain requests before WordPress routing
        add_action('wp_loaded', array($this, 'handle_routing'), 1);
        
        // Also hook into template_redirect as backup
        add_action('template_redirect', array($this, 'handle_routing'), 1);
    }
    
    /**
     * Handle subdomain routing
     */
    public function handle_routing() {
        if ($this->is_dashboard_request()) {
            $this->serve_dashboard();
            exit;
        }
        
        if ($this->is_editor_request()) {
            $this->serve_editor();
            exit;
        }
    }
    
    /**
     * Check if current request is for dashboard
     */
    public function is_dashboard_request() {
        $current_host = $_SERVER['HTTP_HOST'] ?? '';
        
        // Debug logging
        error_log("CPD Subdomain Handler - Current Host: " . $current_host);
        
        // Check for exact match first
        if ($current_host === 'churchprogramme.jermesa.com') {
            error_log("CPD Subdomain Handler - Dashboard subdomain detected: " . $current_host);
            return true;
        }
        
        // Check for pattern match
        if (preg_match('/^churchprogramme\./i', $current_host)) {
            error_log("CPD Subdomain Handler - Dashboard pattern matched: " . $current_host);
            return true;
        }
        
        return false;
    }
    
    /**
     * Check if current request is for editor
     */
    public function is_editor_request() {
        $current_host = $_SERVER['HTTP_HOST'] ?? '';
        
        // Debug logging
        error_log("CPD Subdomain Handler - Current Host: " . $current_host);
        
        // Check for exact match first
        if ($current_host === 'churcheditor.jermesa.com') {
            error_log("CPD Subdomain Handler - Editor subdomain detected: " . $current_host);
            return true;
        }
        
        // Check for pattern match
        if (preg_match('/^churcheditor\./i', $current_host)) {
            error_log("CPD Subdomain Handler - Editor pattern matched: " . $current_host);
            return true;
        }
        
        return false;
    }
    
    /**
     * Serve the dashboard
     */
    private function serve_dashboard() {
        // Prevent WordPress from loading theme
        if (!defined('WP_USE_THEMES')) {
            define('WP_USE_THEMES', false);
        }

        // Set proper headers
        header('Content-Type: text/html; charset=utf-8');
        
        // Include the dashboard template
        $dashboard_file = CPD_PLUGIN_DIR . 'public/templates/dashboard.php';
        if (file_exists($dashboard_file)) {
            include $dashboard_file;
        } else {
            wp_die('Dashboard template not found.');
        }
    }
    
    /**
     * Serve the editor
     */
    private function serve_editor() {
        // Prevent WordPress from loading theme
        if (!defined('WP_USE_THEMES')) {
            define('WP_USE_THEMES', false);
        }

        // Set proper headers
        header('Content-Type: text/html; charset=utf-8');
        
        // Include the editor template
        $editor_file = CPD_PLUGIN_DIR . 'public/templates/editor.php';
        if (file_exists($editor_file)) {
            include $editor_file;
        } else {
            wp_die('Editor template not found.');
        }
    }
    
    /**
     * Get dashboard URL
     */
    public function get_dashboard_url() {
        $site_url = get_site_url();
        $parsed_url = parse_url($site_url);
        $main_domain = $parsed_url['host'];
        
        // Remove www if present
        if (strpos($main_domain, 'www.') === 0) {
            $main_domain = substr($main_domain, 4);
        }
        
        $dashboard_subdomain = $this->settings['dashboard_subdomain'] ?? 'churchprogramme';
        $protocol = $parsed_url['scheme'];
        
        return $protocol . '://' . $dashboard_subdomain . '.' . $main_domain;
    }
    
    /**
     * Get editor URL
     */
    public function get_editor_url() {
        $site_url = get_site_url();
        $parsed_url = parse_url($site_url);
        $main_domain = $parsed_url['host'];
        
        // Remove www if present
        if (strpos($main_domain, 'www.') === 0) {
            $main_domain = substr($main_domain, 4);
        }
        
        $editor_subdomain = $this->settings['editor_subdomain'] ?? 'churcheditor';
        $protocol = $parsed_url['scheme'];
        
        return $protocol . '://' . $editor_subdomain . '.' . $main_domain;
    }
    
    /**
     * Test subdomain connectivity
     */
    public function test_subdomain($type = 'dashboard') {
        $url = $type === 'dashboard' ? $this->get_dashboard_url() : $this->get_editor_url();
        
        $response = wp_remote_get($url . '?test=1', array(
            'timeout' => 10,
            'sslverify' => false,
            'headers' => array(
                'User-Agent' => 'Church Programme Dashboard Test'
            )
        ));
        
        if (is_wp_error($response)) {
            return array(
                'success' => false,
                'message' => 'Failed to connect to subdomain: ' . $response->get_error_message(),
                'url' => $url
            );
        }
        
        $response_code = wp_remote_retrieve_response_code($response);
        
        if ($response_code === 200) {
            return array(
                'success' => true,
                'message' => 'Subdomain is working correctly!',
                'url' => $url,
                'response_code' => $response_code
            );
        } else {
            return array(
                'success' => false,
                'message' => 'Subdomain returned HTTP ' . $response_code,
                'url' => $url,
                'response_code' => $response_code
            );
        }
    }
    
    /**
     * Get subdomain setup instructions
     */
    public function get_setup_instructions() {
        $main_domain = parse_url(get_site_url(), PHP_URL_HOST);
        $dashboard_subdomain = $this->settings['dashboard_subdomain'] ?? 'churchprogramme';
        $editor_subdomain = $this->settings['editor_subdomain'] ?? 'churcheditor';
        
        if (strpos($main_domain, 'www.') === 0) {
            $main_domain = substr($main_domain, 4);
        }
        
        return array(
            'dashboard_subdomain' => $dashboard_subdomain . '.' . $main_domain,
            'editor_subdomain' => $editor_subdomain . '.' . $main_domain,
            'instructions' => array(
                'DNS Setup' => array(
                    'Add CNAME records in your DNS settings:',
                    '- Name: ' . $dashboard_subdomain . ' → Value: ' . $main_domain,
                    '- Name: ' . $editor_subdomain . ' → Value: ' . $main_domain,
                    'TTL: 300 (or your preferred value)'
                ),
                'Server Configuration' => array(
                    'Ensure your web server is configured to handle subdomains',
                    'The subdomains should point to the same document root as your main WordPress site',
                    'No additional server configuration should be needed with this handler'
                )
            )
        );
    }
}

// Initialize the subdomain handler
function cpd_init_simple_subdomain() {
    $subdomain_handler = new CPD_Simple_Subdomain_Handler();
    $subdomain_handler->init();
}
add_action('init', 'cpd_init_simple_subdomain');
?>
