# Admin Panel Save Issue - Complete Debugging Guide

## Issue Summary
The "Save All Settings" button in the Admin Panel is not responding and changes are not being saved to the database.

## Files Created for Debugging

### 1. test-admin-save.php
**Purpose:** Comprehensive diagnostic page that tests all aspects of the save functionality
**Location:** `church-programme-dashboard/test-admin-save.php`
**Access:** `http://yourdomain.com/wp-content/plugins/church-programme-dashboard/test-admin-save.php`

**What it tests:**
- AJAX handler registration
- Class initialization
- Current settings in database
- JavaScript dependencies
- Live AJAX save test
- Network requests

### 2. admin-script-debug.js
**Purpose:** Debug version of admin script with extensive console logging
**Location:** `church-programme-dashboard/admin/js/admin-script-debug.js`

**Features:**
- Logs every step of the save process
- Shows form data being collected
- Displays AJAX request/response details
- Identifies where the process fails

### 3. test-button-click.html
**Purpose:** Basic test to verify button clicks and form submissions work
**Location:** `church-programme-dashboard/test-button-click.html`
**Access:** `http://yourdomain.com/wp-content/plugins/church-programme-dashboard/test-button-click.html`

## Step-by-Step Debugging Process

### STEP 1: Run Initial Diagnostics

1. **Access the diagnostic page:**
   ```
   http://yourdomain.com/wp-content/plugins/church-programme-dashboard/test-admin-save.php
   ```

2. **Check the results:**
   - All AJAX actions should show "✓ Registered"
   - All classes should show "✓ Loaded"
   - Settings should display current values

3. **Click "Run Save Test":**
   - Should show "✓ SUCCESS" message
   - Check the console log for detailed information

### STEP 2: Check Browser Console

1. **Open Admin Settings page:**
   - Go to WordPress Admin > Church Programme > Settings

2. **Open Developer Tools:**
   - Press F12 (or Cmd+Option+I on Mac)
   - Go to "Console" tab

3. **Make a change and click "Save All Settings":**
   - Look for console messages
   - Should see: "=== FORM SUBMIT TRIGGERED ==="
   - Should see: "Saving settings: {data}"
   - Should see: "Save response: {success: true}"

4. **If you see errors:**
   - Red error messages indicate JavaScript problems
   - "cpdAdmin is not defined" = localization issue
   - "$ is not a function" = jQuery not loaded
   - No messages at all = event handler not attached

### STEP 3: Check Network Tab

1. **In Developer Tools, go to "Network" tab**

2. **Click "Save All Settings" button**

3. **Look for request to "admin-ajax.php":**
   - Should appear in the list
   - Status should be "200" (green)
   - Click on it to see details

4. **Check Request:**
   - Headers tab: Should show POST method
   - Payload tab: Should show action, nonce, and settings data

5. **Check Response:**
   - Preview tab: Should show {success: true, data: {...}}
   - If shows HTML instead of JSON = PHP error
   - If shows {success: false} = server-side error

### STEP 4: Enable Debug Mode

Add to `wp-config.php`:
```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
define('WP_DEBUG_DISPLAY', false);
define('SCRIPT_DEBUG', true);
```

Then check `wp-content/debug.log` for errors.

### STEP 5: Use Debug Script

Temporarily use the debug version of the admin script:

**Edit:** `church-programme-dashboard/admin/class-cpd-admin.php`

**Find line ~105:**
```php
wp_enqueue_script(
    'cpd-admin-script',
    CPD_PLUGIN_URL . 'admin/js/admin-script.js',
    array('jquery', 'wp-color-picker'),
    $js_version,
    true
);
```

**Change to:**
```php
wp_enqueue_script(
    'cpd-admin-script',
    CPD_PLUGIN_URL . 'admin/js/admin-script-debug.js',  // Changed
    array('jquery', 'wp-color-picker'),
    time(),  // Force reload
    true
);
```

**Clear cache and reload the admin page.**

## Common Issues & Solutions

### Issue 1: JavaScript Not Loading

**Symptoms:**
- No console messages when clicking save
- Form submits and page reloads
- Button doesn't change to "Saving..."

**Causes:**
- JavaScript file not found (404 error)
- JavaScript syntax error
- Script not enqueued properly

**Solutions:**
1. Check Network tab for 404 errors
2. Verify file exists: `admin/js/admin-script.js`
3. Clear browser cache (Ctrl+Shift+Delete)
4. Clear WordPress cache (if using cache plugin)
5. Try different browser

### Issue 2: cpdAdmin Not Defined

**Symptoms:**
- Console error: "cpdAdmin is not defined"
- AJAX request fails immediately

**Causes:**
- wp_localize_script not called
- Script loaded before localization
- JavaScript error before cpdAdmin is used

**Solutions:**
1. Verify `wp_localize_script` is called in `admin/class-cpd-admin.php`
2. Check script dependencies are correct
3. Ensure script is loaded in footer (last parameter = true)

### Issue 3: AJAX Handler Not Registered

**Symptoms:**
- AJAX returns 0 or -1
- Console shows "action not found"

**Causes:**
- CPD_AJAX::init() not called
- Hook not registered properly
- Plugin not fully activated

**Solutions:**
1. Deactivate and reactivate plugin
2. Check `church-programme-dashboard.php` calls `CPD_AJAX::init()`
3. Verify hook is registered: `add_action('wp_ajax_cpd_save_settings', ...)`

### Issue 4: Nonce Verification Failed

**Symptoms:**
- Response: "Security check failed"
- Console shows nonce error

**Causes:**
- Nonce expired (page open too long)
- Nonce not generated correctly
- User session issue

**Solutions:**
1. Refresh the admin page
2. Log out and log back in
3. Clear browser cookies
4. Check if nonce is in POST data (Network tab)

### Issue 5: Permission Denied

**Symptoms:**
- Response: "Permission denied"
- Settings don't save

**Causes:**
- User doesn't have 'manage_options' capability
- User role changed
- Security plugin blocking

**Solutions:**
1. Verify user is Administrator
2. Check user capabilities
3. Temporarily disable security plugins
4. Check if other admin functions work

### Issue 6: PHP Errors

**Symptoms:**
- AJAX returns HTML instead of JSON
- 500 Internal Server Error
- White screen

**Causes:**
- PHP syntax error
- Fatal error in code
- Missing function or class

**Solutions:**
1. Enable WP_DEBUG and check debug.log
2. Check server error logs
3. Look for PHP errors in Network tab response
4. Test PHP syntax: `php -l includes/class-cpd-ajax.php`

### Issue 7: Settings Not Persisting

**Symptoms:**
- Success message shows
- Settings don't save to database
- Settings reset after page reload

**Causes:**
- Database write permissions
- update_option() failing
- Wrong option names

**Solutions:**
1. Check database permissions
2. Test manually: `update_option('test_option', 'test_value')`
3. Verify option names have 'cpd_' prefix
4. Check database directly:
   ```sql
   SELECT * FROM wp_options WHERE option_name LIKE 'cpd_%';
   ```

## Quick Fixes

### Fix 1: Force Script Reload

Clear all caches and force browser to reload JavaScript:

```php
// In admin/class-cpd-admin.php, line ~105
wp_enqueue_script(
    'cpd-admin-script',
    CPD_PLUGIN_URL . 'admin/js/admin-script.js',
    array('jquery', 'wp-color-picker'),
    time(), // This forces reload every time
    true
);
```

### Fix 2: Verify Event Handler

Add this to browser console on admin page:

```javascript
// Check if form exists
console.log('Form:', jQuery('#cpd-settings-form').length);

// Check if handler is attached
var events = jQuery._data(jQuery('#cpd-settings-form')[0], 'events');
console.log('Events:', events);

// Manually trigger save
jQuery('#cpd-settings-form').trigger('submit');
```

### Fix 3: Test AJAX Directly

Run this in browser console:

```javascript
jQuery.post(cpdAdmin.ajaxUrl, {
    action: 'cpd_save_settings',
    nonce: cpdAdmin.nonce,
    settings: {
        dashboard_title: 'Test Title',
        primary_color: '#ff0000'
    }
}, function(response) {
    console.log('Direct AJAX test:', response);
});
```

### Fix 4: Manual Database Update

If all else fails, update settings manually:

```php
// Add to functions.php temporarily
add_action('admin_init', function() {
    if (isset($_GET['cpd_manual_save']) && current_user_can('manage_options')) {
        update_option('cpd_dashboard_title', 'Your Title Here');
        update_option('cpd_primary_color', '#4a5568');
        update_option('cpd_notice_board_enabled', '1');
        // Add more as needed
        
        echo '<div class="notice notice-success"><p>Settings saved manually!</p></div>';
    }
});

// Visit: /wp-admin/?cpd_manual_save=1
```

## Testing Checklist

After applying fixes, test:

- [ ] Open Admin Settings page
- [ ] Open browser console (F12)
- [ ] Change Dashboard Title
- [ ] Click "Save All Settings"
- [ ] See "Saving..." on button
- [ ] See success message
- [ ] Refresh page
- [ ] Verify title changed
- [ ] Test each tab
- [ ] Test different field types (text, color, checkbox, textarea)

## Enhanced AJAX Handler

The AJAX handler has been updated with:
- Better error handling
- Detailed logging (when WP_DEBUG is enabled)
- Try-catch for nonce verification
- Count of saved settings
- Partial save support (some settings save even if others fail)

## Next Steps

1. **Run test-admin-save.php first**
2. **Check browser console for errors**
3. **Enable WP_DEBUG and check logs**
4. **Use debug script version**
5. **Test AJAX directly in console**
6. **Check database for saved values**

## Support Information

If issues persist:

1. **WordPress Version:** Requires 5.8+
2. **PHP Version:** Requires 7.4+
3. **Browser:** Try Chrome, Firefox, or Edge
4. **Plugins:** Temporarily disable other plugins
5. **Theme:** Try default WordPress theme
6. **Server:** Check error logs
7. **Permissions:** Verify file and database permissions

## Files Modified

1. `includes/class-cpd-ajax.php` - Enhanced error handling and logging
2. Created `test-admin-save.php` - Diagnostic page
3. Created `admin/js/admin-script-debug.js` - Debug version of script
4. Created `test-button-click.html` - Basic functionality test
5. Created this guide

## Conclusion

The save functionality should work if:
1. JavaScript loads correctly
2. cpdAdmin object is defined
3. AJAX handler is registered
4. Nonce verification passes
5. User has correct permissions
6. Database is writable

Use the diagnostic tools to identify which step is failing, then apply the appropriate fix.

