<?php
/**
 * Direct AJAX Handler Test
 * 
 * This file tests the AJAX save handler directly without JavaScript
 * 
 * Usage: Access this file directly in your browser:
 * http://yourdomain.com/wp-content/plugins/church-programme-dashboard/test-ajax-direct.php
 */

// Load WordPress
require_once('../../../wp-load.php');

// Check if user is admin
if (!current_user_can('manage_options')) {
    die('You must be an administrator to access this page.');
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>Direct AJAX Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
            max-width: 1000px;
            margin: 40px auto;
            padding: 20px;
            background: #f0f0f1;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin-bottom: 20px;
            border-radius: 5px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        h1 {
            color: #1d2327;
            border-bottom: 2px solid #2271b1;
            padding-bottom: 10px;
        }
        h2 {
            color: #2271b1;
            margin-top: 0;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        pre {
            background: #f6f7f7;
            padding: 15px;
            border-radius: 3px;
            overflow-x: auto;
            border: 1px solid #ddd;
        }
        button {
            background: #2271b1;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
        }
        button:hover {
            background: #135e96;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        table th, table td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        table th {
            background: #f6f7f7;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <h1>🔧 Direct AJAX Handler Test</h1>
    <p>This page tests the AJAX save handler by calling it directly from PHP.</p>

    <!-- Test 1: Check if handler exists -->
    <div class="test-section">
        <h2>Test 1: Check AJAX Handler Registration</h2>
        <?php
        $action = 'wp_ajax_cpd_save_settings';
        $has_action = has_action($action);
        
        if ($has_action) {
            echo '<div class="status success">✓ AJAX handler is registered</div>';
            echo '<p>Action hook: <code>' . esc_html($action) . '</code></p>';
            
            // Get the callback
            global $wp_filter;
            if (isset($wp_filter[$action])) {
                echo '<p>Callback details:</p>';
                echo '<pre>' . print_r($wp_filter[$action], true) . '</pre>';
            }
        } else {
            echo '<div class="status error">✗ AJAX handler is NOT registered</div>';
            echo '<p>This means CPD_AJAX::init() was not called or the hook was not added.</p>';
        }
        ?>
    </div>

    <!-- Test 2: Check if class and method exist -->
    <div class="test-section">
        <h2>Test 2: Check Class and Method</h2>
        <?php
        $class_exists = class_exists('CPD_AJAX');
        $method_exists = method_exists('CPD_AJAX', 'save_settings');
        
        echo '<table>';
        echo '<tr><th>Check</th><th>Status</th></tr>';
        echo '<tr><td>CPD_AJAX class exists</td><td class="status ' . ($class_exists ? 'success' : 'error') . '">' . ($class_exists ? '✓ Yes' : '✗ No') . '</td></tr>';
        echo '<tr><td>save_settings method exists</td><td class="status ' . ($method_exists ? 'success' : 'error') . '">' . ($method_exists ? '✓ Yes' : '✗ No') . '</td></tr>';
        echo '</table>';
        
        if ($class_exists && $method_exists) {
            echo '<div class="status success">✓ Class and method are available</div>';
        } else {
            echo '<div class="status error">✗ Class or method is missing</div>';
        }
        ?>
    </div>

    <!-- Test 3: Test with valid data -->
    <div class="test-section">
        <h2>Test 3: Call Handler with Valid Data</h2>
        <?php
        if (isset($_POST['run_test'])) {
            echo '<h3>Test Results:</h3>';
            
            // Simulate AJAX request
            $_POST['action'] = 'cpd_save_settings';
            $_POST['nonce'] = wp_create_nonce('cpd_ajax_nonce');
            $_POST['settings'] = array(
                'test_setting_' . time() => 'test_value',
                'dashboard_title' => 'Test Dashboard Title',
                'primary_color' => '#ff0000'
            );
            
            echo '<p><strong>Simulated POST data:</strong></p>';
            echo '<pre>' . print_r($_POST, true) . '</pre>';
            
            // Capture output
            ob_start();
            
            try {
                // Call the handler
                if (class_exists('CPD_AJAX') && method_exists('CPD_AJAX', 'save_settings')) {
                    CPD_AJAX::save_settings();
                } else {
                    echo json_encode(array('success' => false, 'data' => array('message' => 'Handler not available')));
                }
            } catch (Exception $e) {
                echo json_encode(array('success' => false, 'data' => array('message' => $e->getMessage())));
            }
            
            $output = ob_get_clean();
            
            echo '<p><strong>Handler output:</strong></p>';
            echo '<pre>' . esc_html($output) . '</pre>';
            
            // Try to decode as JSON
            $response = json_decode($output, true);
            if ($response) {
                if (isset($response['success']) && $response['success']) {
                    echo '<div class="status success">✓ Handler returned success!</div>';
                    echo '<p>Message: ' . esc_html($response['data']['message']) . '</p>';
                    
                    // Check if settings were actually saved
                    echo '<h3>Verify Settings in Database:</h3>';
                    echo '<table>';
                    echo '<tr><th>Setting</th><th>Value in DB</th></tr>';
                    foreach ($_POST['settings'] as $key => $value) {
                        $db_value = get_option('cpd_' . $key);
                        $match = ($db_value === $value);
                        echo '<tr>';
                        echo '<td>cpd_' . esc_html($key) . '</td>';
                        echo '<td class="status ' . ($match ? 'success' : 'error') . '">';
                        echo esc_html($db_value) . ' ' . ($match ? '✓' : '✗');
                        echo '</td>';
                        echo '</tr>';
                    }
                    echo '</table>';
                } else {
                    echo '<div class="status error">✗ Handler returned error</div>';
                    echo '<p>Error: ' . esc_html($response['data']['message']) . '</p>';
                }
            } else {
                echo '<div class="status error">✗ Handler did not return valid JSON</div>';
                echo '<p>This usually means there was a PHP error or the handler exited unexpectedly.</p>';
            }
            
            // Clean up
            unset($_POST['action']);
            unset($_POST['nonce']);
            unset($_POST['settings']);
            unset($_POST['run_test']);
        } else {
            ?>
            <form method="post">
                <input type="hidden" name="run_test" value="1">
                <button type="submit">Run Direct Handler Test</button>
            </form>
            <p class="description">This will call the save_settings handler directly with test data.</p>
            <?php
        }
        ?>
    </div>

    <!-- Test 4: Check current settings -->
    <div class="test-section">
        <h2>Test 4: Current Settings in Database</h2>
        <?php
        global $wpdb;
        $options = $wpdb->get_results(
            "SELECT option_name, option_value FROM {$wpdb->options} WHERE option_name LIKE 'cpd_%' ORDER BY option_name",
            ARRAY_A
        );
        
        if ($options) {
            echo '<p>Found ' . count($options) . ' CPD settings in database:</p>';
            echo '<table>';
            echo '<tr><th>Option Name</th><th>Value</th></tr>';
            foreach ($options as $option) {
                $value = $option['option_value'];
                if (strlen($value) > 100) {
                    $value = substr($value, 0, 100) . '...';
                }
                echo '<tr>';
                echo '<td>' . esc_html($option['option_name']) . '</td>';
                echo '<td>' . esc_html($value) . '</td>';
                echo '</tr>';
            }
            echo '</table>';
        } else {
            echo '<div class="status error">No CPD settings found in database</div>';
        }
        ?>
    </div>

    <!-- Test 5: Test update_option directly -->
    <div class="test-section">
        <h2>Test 5: Test update_option Directly</h2>
        <?php
        if (isset($_POST['test_update_option'])) {
            $test_key = 'cpd_test_option_' . time();
            $test_value = 'test_value_' . time();
            
            echo '<p>Testing: <code>update_option(\'' . esc_html($test_key) . '\', \'' . esc_html($test_value) . '\')</code></p>';
            
            $result = update_option($test_key, $test_value);
            $retrieved = get_option($test_key);
            
            if ($result || $retrieved === $test_value) {
                echo '<div class="status success">✓ update_option works!</div>';
                echo '<p>Value saved: ' . esc_html($retrieved) . '</p>';
                
                // Clean up
                delete_option($test_key);
                echo '<p><em>Test option deleted.</em></p>';
            } else {
                echo '<div class="status error">✗ update_option failed</div>';
                echo '<p>This indicates a database permission issue.</p>';
            }
            
            unset($_POST['test_update_option']);
        } else {
            ?>
            <form method="post">
                <input type="hidden" name="test_update_option" value="1">
                <button type="submit">Test update_option</button>
            </form>
            <p class="description">This tests if WordPress can write to the options table.</p>
            <?php
        }
        ?>
    </div>

    <!-- Test 6: Check WordPress environment -->
    <div class="test-section">
        <h2>Test 6: WordPress Environment</h2>
        <table>
            <tr>
                <th>Check</th>
                <th>Value</th>
            </tr>
            <tr>
                <td>WordPress Version</td>
                <td><?php echo esc_html(get_bloginfo('version')); ?></td>
            </tr>
            <tr>
                <td>PHP Version</td>
                <td><?php echo esc_html(phpversion()); ?></td>
            </tr>
            <tr>
                <td>Current User</td>
                <td><?php echo esc_html(wp_get_current_user()->user_login); ?> (ID: <?php echo get_current_user_id(); ?>)</td>
            </tr>
            <tr>
                <td>User Can Manage Options</td>
                <td class="status <?php echo current_user_can('manage_options') ? 'success' : 'error'; ?>">
                    <?php echo current_user_can('manage_options') ? '✓ Yes' : '✗ No'; ?>
                </td>
            </tr>
            <tr>
                <td>WP_DEBUG</td>
                <td><?php echo defined('WP_DEBUG') && WP_DEBUG ? 'Enabled' : 'Disabled'; ?></td>
            </tr>
            <tr>
                <td>AJAX URL</td>
                <td><?php echo esc_html(admin_url('admin-ajax.php')); ?></td>
            </tr>
        </table>
    </div>

    <div class="test-section">
        <h2>Summary</h2>
        <p>If all tests pass, the AJAX handler is working correctly and the issue is likely in the JavaScript.</p>
        <p>If tests fail, check the specific error messages above for guidance.</p>
        <p><strong>Next steps:</strong></p>
        <ul>
            <li>If handler tests pass but JavaScript doesn't work: Check browser console for errors</li>
            <li>If handler tests fail: Check the error messages and fix the PHP code</li>
            <li>If update_option fails: Check database permissions</li>
            <li>If nonce fails: Try logging out and back in</li>
        </ul>
    </div>
</body>
</html>

