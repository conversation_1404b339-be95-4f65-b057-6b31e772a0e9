<?php
/**
 * Plugin Name: Church Programme Dashboard
 * Plugin URI: https://jermesa.com
 * Description: A beautiful mobile-first dashboard for displaying church programmes with AI-powered data extraction from images. Features include a public dashboard with calendar view, secure editor page, and comprehensive admin panel.
 * Version: 13.0.0
 * Author: Jermesa Studio
 * Author URI: https://www.jermesa.com
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: church-programme-dashboard
 * Domain Path: /languages
 * Requires at least: 5.8
 * Requires PHP: 7.4
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('CPD_VERSION', '1.0.0');
define('CPD_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('CPD_PLUGIN_URL', plugin_dir_url(__FILE__));
define('CPD_PLUGIN_BASENAME', plugin_basename(__FILE__));
define('CPD_PLUGIN_FILE', __FILE__);

/**
 * Main Church Programme Dashboard Class
 */
class Church_Programme_Dashboard {
    
    /**
     * Single instance of the class
     */
    private static $instance = null;
    
    /**
     * Get single instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->init_hooks();
        $this->includes();
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        register_activation_hook(CPD_PLUGIN_FILE, array($this, 'activate'));
        register_deactivation_hook(CPD_PLUGIN_FILE, array($this, 'deactivate'));
        
        add_action('plugins_loaded', array($this, 'load_textdomain'));
        add_action('init', array($this, 'init'));
    }
    
    /**
     * Include required files
     */
    private function includes() {
        // Core classes
        require_once CPD_PLUGIN_DIR . 'includes/class-cpd-database.php';
        require_once CPD_PLUGIN_DIR . 'includes/class-cpd-ajax.php';
        require_once CPD_PLUGIN_DIR . 'includes/class-cpd-rest-api.php';
        require_once CPD_PLUGIN_DIR . 'includes/class-cpd-auth.php';
        require_once CPD_PLUGIN_DIR . 'includes/class-cpd-subdomain.php';

        // Admin classes - Always load to ensure they're available
        // The is_admin() check in init() will prevent them from running on frontend
        require_once CPD_PLUGIN_DIR . 'admin/class-cpd-admin.php';
        require_once CPD_PLUGIN_DIR . 'admin/class-cpd-admin-settings.php';

        // Public classes
        require_once CPD_PLUGIN_DIR . 'public/class-cpd-public.php';
        require_once CPD_PLUGIN_DIR . 'public/class-cpd-dashboard.php';
        require_once CPD_PLUGIN_DIR . 'public/class-cpd-editor.php';
    }
    
    /**
     * Plugin activation
     */
    public function activate() {
        // Create database tables
        CPD_Database::create_tables();
        
        // Set default options
        $this->set_default_options();
        
        // Flush rewrite rules
        flush_rewrite_rules();
    }
    
    /**
     * Plugin deactivation
     */
    public function deactivate() {
        // Flush rewrite rules
        flush_rewrite_rules();
    }
    
    /**
     * Set default options
     */
    private function set_default_options() {
        $defaults = array(
            'cpd_dashboard_title' => 'Church Programme',
            'cpd_subdomain_name' => 'programme',
            'cpd_header_bg_color' => '#ffffff',
            'cpd_header_bg_image' => '',
            'cpd_primary_color' => '#4a5568',
            'cpd_secondary_color' => '#718096',
            'cpd_accent_color' => '#3182ce',
            'cpd_text_color' => '#2d3748',
            'cpd_notice_board_enabled' => false,
            'cpd_notice_board_title' => 'Notice Board',
            'cpd_notice_board_content' => '',
            // Programme labels
            'cpd_label_miet_balang' => 'MIET BALANG',
            'cpd_label_miet_balang_time' => '6:30 PM',
            'cpd_label_jingiaseng_samla' => 'JINGIASENG SAMLA',
            'cpd_label_jingiaseng_samla_time' => '6:30 PM',
            'cpd_label_jingiaseng_1pm' => 'JINGIASENG 1:00 Baje',
            'cpd_label_jingiaseng_1pm_time' => '1:00 PM',
            'cpd_label_jingiaseng_khynnah' => 'JINGIASENG KHYNNAH',
            'cpd_label_jingiaseng_khynnah_time' => '3:00 PM',
            'cpd_label_jingiaseng_iing' => 'JINGIASENG IING',
            'cpd_label_jingiaseng_iing_time' => '6:30 PM',
            // Cookie consent
            'cpd_cookie_consent_text' => 'We use cookies to enhance your experience. By continuing to visit this site you agree to our use of cookies.',
            'cpd_cookie_consent_button' => 'Accept',
            // Carousel animation settings
            'cpd_carousel_animation_type' => 'slide',
            'cpd_carousel_animation_speed' => '500',
            'cpd_carousel_auto_interval' => '10000',
            'cpd_carousel_direction' => 'right-to-left',
        );
        
        foreach ($defaults as $key => $value) {
            if (get_option($key) === false) {
                add_option($key, $value);
            }
        }
    }
    
    /**
     * Load plugin textdomain
     */
    public function load_textdomain() {
        load_plugin_textdomain(
            'church-programme-dashboard',
            false,
            dirname(CPD_PLUGIN_BASENAME) . '/languages'
        );
    }
    
    /**
     * Initialize plugin
     */
    public function init() {
        // Initialize classes
        CPD_AJAX::init();
        CPD_REST_API::init();
        CPD_Auth::init();
        CPD_Subdomain::init();
        
        if (is_admin()) {
            CPD_Admin::init();
            CPD_Admin_Settings::init();
        }
        
        CPD_Public::init();
        CPD_Dashboard::init();
        CPD_Editor::init();
    }
}

/**
 * Initialize the plugin
 */
function cpd_init() {
    return Church_Programme_Dashboard::get_instance();
}

// Start the plugin
cpd_init();
