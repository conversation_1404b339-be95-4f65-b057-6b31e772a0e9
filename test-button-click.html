<!DOCTYPE html>
<html>
<head>
    <title>Button Click Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
        }
        .test-section {
            background: #f5f5f5;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
        }
        button {
            background: #0073aa;
            color: white;
            border: none;
            padding: 10px 20px;
            font-size: 14px;
            cursor: pointer;
            border-radius: 3px;
        }
        button:hover {
            background: #005177;
        }
        .log {
            background: white;
            padding: 10px;
            margin-top: 10px;
            border: 1px solid #ddd;
            max-height: 300px;
            overflow-y: auto;
        }
        .log-entry {
            padding: 5px;
            border-bottom: 1px solid #eee;
        }
        .success {
            color: green;
        }
        .error {
            color: red;
        }
    </style>
</head>
<body>
    <h1>Button Click & Form Submit Test</h1>
    <p>This page tests if button clicks and form submissions are working correctly.</p>

    <div class="test-section">
        <h2>Test 1: Simple Button Click</h2>
        <button id="test-btn-1">Click Me</button>
        <div id="result-1"></div>
    </div>

    <div class="test-section">
        <h2>Test 2: Form Submit with Button</h2>
        <form id="test-form-1">
            <input type="text" name="test-input" value="Test Value" style="padding: 5px; margin-right: 10px;">
            <button type="submit">Submit Form</button>
        </form>
        <div id="result-2"></div>
    </div>

    <div class="test-section">
        <h2>Test 3: Form Submit with preventDefault</h2>
        <form id="test-form-2">
            <input type="text" name="test-input-2" value="Test Value 2" style="padding: 5px; margin-right: 10px;">
            <button type="submit">Submit with preventDefault</button>
        </form>
        <div id="result-3"></div>
    </div>

    <div class="test-section">
        <h2>Test 4: Multiple Event Handlers</h2>
        <form id="test-form-3">
            <input type="text" name="test-input-3" value="Test Value 3" style="padding: 5px; margin-right: 10px;">
            <button type="submit">Submit Multiple Handlers</button>
        </form>
        <div id="result-4"></div>
    </div>

    <div class="test-section">
        <h2>Console Log</h2>
        <button id="clear-log">Clear Log</button>
        <div class="log" id="console-log"></div>
    </div>

    <script>
        // Logging function
        function log(message, type = 'info') {
            const logDiv = document.getElementById('console-log');
            const entry = document.createElement('div');
            entry.className = 'log-entry ' + type;
            entry.textContent = new Date().toLocaleTimeString() + ': ' + message;
            logDiv.insertBefore(entry, logDiv.firstChild);
        }

        // Clear log
        document.getElementById('clear-log').addEventListener('click', function() {
            document.getElementById('console-log').innerHTML = '';
            log('Log cleared');
        });

        // Test 1: Simple button click
        document.getElementById('test-btn-1').addEventListener('click', function() {
            log('Test 1: Button clicked', 'success');
            document.getElementById('result-1').innerHTML = '<span class="success">✓ Button click works!</span>';
        });

        // Test 2: Form submit without preventDefault
        document.getElementById('test-form-1').addEventListener('submit', function(e) {
            e.preventDefault(); // Prevent actual submission for demo
            log('Test 2: Form submitted', 'success');
            document.getElementById('result-2').innerHTML = '<span class="success">✓ Form submit works!</span>';
        });

        // Test 3: Form submit with explicit preventDefault
        document.getElementById('test-form-2').addEventListener('submit', function(e) {
            log('Test 3: Form submit event triggered', 'success');
            e.preventDefault();
            log('Test 3: preventDefault called', 'success');
            
            const formData = new FormData(e.target);
            const data = {};
            formData.forEach((value, key) => {
                data[key] = value;
            });
            
            log('Test 3: Form data collected: ' + JSON.stringify(data), 'success');
            document.getElementById('result-3').innerHTML = '<span class="success">✓ Form submit with preventDefault works!</span>';
        });

        // Test 4: Multiple event handlers
        const form3 = document.getElementById('test-form-3');
        
        // First handler
        form3.addEventListener('submit', function(e) {
            log('Test 4: First handler called', 'success');
        });
        
        // Second handler
        form3.addEventListener('submit', function(e) {
            log('Test 4: Second handler called', 'success');
            e.preventDefault();
            log('Test 4: preventDefault called in second handler', 'success');
            document.getElementById('result-4').innerHTML = '<span class="success">✓ Multiple handlers work!</span>';
        });

        // Log page load
        log('Page loaded and ready');
        log('All event handlers attached');

        // Test if jQuery is available
        if (typeof jQuery !== 'undefined') {
            log('jQuery is available (version ' + jQuery.fn.jquery + ')');
        } else {
            log('jQuery is NOT available', 'error');
        }
    </script>
</body>
</html>

