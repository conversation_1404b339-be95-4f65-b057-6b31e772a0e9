<?php
/**
 * Editor Users Management Page
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

$users = CPD_Database::get_all_editor_users();
?>

<div class="wrap cpd-admin-wrap">
    <h1><?php echo esc_html__('Editor Users', 'church-programme-dashboard'); ?></h1>
    
    <p><?php esc_html_e('Manage users who can access the editor page to add/edit/delete programme data.', 'church-programme-dashboard'); ?></p>
    
    <div class="cpd-admin-notice" id="cpd-user-notice" style="display: none;">
        <p></p>
    </div>
    
    <div class="cpd-add-user-section">
        <h2><?php esc_html_e('Add New Editor User', 'church-programme-dashboard'); ?></h2>
        
        <form id="cpd-add-user-form">
            <?php wp_nonce_field('cpd_ajax_nonce', 'cpd_user_nonce'); ?>
            
            <table class="form-table">
                <tr>
                    <th scope="row">
                        <label for="new_username"><?php esc_html_e('Username', 'church-programme-dashboard'); ?> <span class="required">*</span></label>
                    </th>
                    <td>
                        <input type="text" id="new_username" name="username" class="regular-text" required>
                    </td>
                </tr>
                
                <tr>
                    <th scope="row">
                        <label for="new_password"><?php esc_html_e('Password', 'church-programme-dashboard'); ?> <span class="required">*</span></label>
                    </th>
                    <td>
                        <input type="password" id="new_password" name="password" class="regular-text" required>
                        <button type="button" class="button" id="cpd-generate-password">
                            <?php esc_html_e('Generate Strong Password', 'church-programme-dashboard'); ?>
                        </button>
                    </td>
                </tr>
                
                <tr>
                    <th scope="row">
                        <label for="new_email"><?php esc_html_e('Email', 'church-programme-dashboard'); ?></label>
                    </th>
                    <td>
                        <input type="email" id="new_email" name="email" class="regular-text">
                        <p class="description"><?php esc_html_e('Optional. For password recovery purposes.', 'church-programme-dashboard'); ?></p>
                    </td>
                </tr>
            </table>
            
            <p class="submit">
                <button type="submit" class="button button-primary">
                    <?php esc_html_e('Add User', 'church-programme-dashboard'); ?>
                </button>
            </p>
        </form>
    </div>
    
    <hr>
    
    <div class="cpd-users-list-section">
        <h2><?php esc_html_e('Existing Editor Users', 'church-programme-dashboard'); ?></h2>
        
        <?php if (empty($users)): ?>
            <p><?php esc_html_e('No editor users found. Add your first user above.', 'church-programme-dashboard'); ?></p>
        <?php else: ?>
            <table class="wp-list-table widefat fixed striped">
                <thead>
                    <tr>
                        <th><?php esc_html_e('Username', 'church-programme-dashboard'); ?></th>
                        <th><?php esc_html_e('Email', 'church-programme-dashboard'); ?></th>
                        <th><?php esc_html_e('Status', 'church-programme-dashboard'); ?></th>
                        <th><?php esc_html_e('Created', 'church-programme-dashboard'); ?></th>
                        <th><?php esc_html_e('Last Login', 'church-programme-dashboard'); ?></th>
                        <th><?php esc_html_e('Actions', 'church-programme-dashboard'); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($users as $user): ?>
                        <tr data-user-id="<?php echo esc_attr($user->id); ?>">
                            <td><strong><?php echo esc_html($user->username); ?></strong></td>
                            <td><?php echo esc_html($user->email ? $user->email : '—'); ?></td>
                            <td>
                                <span class="cpd-status-badge cpd-status-<?php echo esc_attr($user->status); ?>">
                                    <?php echo esc_html(ucfirst($user->status)); ?>
                                </span>
                            </td>
                            <td><?php echo esc_html(date_i18n(get_option('date_format'), strtotime($user->created_at))); ?></td>
                            <td><?php echo $user->last_login ? esc_html(date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($user->last_login))) : '—'; ?></td>
                            <td>
                                <button type="button" class="button button-small cpd-delete-user" data-user-id="<?php echo esc_attr($user->id); ?>">
                                    <?php esc_html_e('Delete', 'church-programme-dashboard'); ?>
                                </button>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        <?php endif; ?>
    </div>
</div>

