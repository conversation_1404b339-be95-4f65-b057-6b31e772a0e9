/**
 * Admin JavaScript - Debug Version
 * This version includes extensive logging to help identify save issues
 */

(function($) {
    'use strict';
    
    console.log('=== CPD Admin Script Loading ===');
    console.log('jQuery version:', $.fn.jquery);
    console.log('cpdAdmin object:', typeof cpdAdmin !== 'undefined' ? cpdAdmin : 'NOT DEFINED');
    
    $(document).ready(function() {
        console.log('=== Document Ready ===');
        
        // Check if form exists
        var form = $('#cpd-settings-form');
        console.log('Form found:', form.length > 0);
        console.log('Form element:', form[0]);
        
        // Check if submit button exists
        var submitButton = form.find('button[type="submit"]');
        console.log('Submit button found:', submitButton.length > 0);
        console.log('Submit button element:', submitButton[0]);
        
        // Initialize color pickers
        if (typeof $.fn.wpColorPicker !== 'undefined') {
            $('.cpd-color-picker').wpColorPicker();
            console.log('Color pickers initialized');
        } else {
            console.error('wpColorPicker not available');
        }
        
        // Tab navigation
        $('.nav-tab').on('click', function(e) {
            e.preventDefault();
            console.log('Tab clicked:', $(this).attr('href'));
            
            var target = $(this).attr('href');
            
            // Update tabs
            $('.nav-tab').removeClass('nav-tab-active');
            $(this).addClass('nav-tab-active');
            
            // Update content
            $('.cpd-tab-content').removeClass('cpd-tab-active');
            $(target).addClass('cpd-tab-active');
        });
        
        // Image upload
        $('.cpd-upload-image').on('click', function(e) {
            e.preventDefault();
            console.log('Upload image clicked');
            
            var button = $(this);
            var targetInput = $('#' + button.data('target'));
            
            if (typeof wp === 'undefined' || typeof wp.media === 'undefined') {
                console.error('WordPress media library not available');
                alert('WordPress media library not available');
                return;
            }
            
            var mediaUploader = wp.media({
                title: 'Select Image',
                button: {
                    text: 'Use this image'
                },
                multiple: false
            });
            
            mediaUploader.on('select', function() {
                var attachment = mediaUploader.state().get('selection').first().toJSON();
                targetInput.val(attachment.url);
                console.log('Image selected:', attachment.url);
            });
            
            mediaUploader.open();
        });
        
        // Remove image
        $('.cpd-remove-image').on('click', function(e) {
            e.preventDefault();
            console.log('Remove image clicked');
            
            var button = $(this);
            var targetInput = $('#' + button.data('target'));
            
            targetInput.val('');
            button.hide();
        });
        
        // Save settings - MAIN HANDLER
        console.log('=== Attaching form submit handler ===');
        
        $('#cpd-settings-form').on('submit', function(e) {
            console.log('=== FORM SUBMIT TRIGGERED ===');
            e.preventDefault();
            
            // Check if cpdAdmin is defined
            if (typeof cpdAdmin === 'undefined') {
                console.error('CRITICAL: cpdAdmin object is not defined!');
                alert('Error: Admin configuration not loaded. Please refresh the page.');
                return false;
            }
            
            console.log('cpdAdmin.ajaxUrl:', cpdAdmin.ajaxUrl);
            console.log('cpdAdmin.nonce:', cpdAdmin.nonce);
            
            var form = $(this);
            var submitButton = form.find('button[type="submit"]');
            var originalText = submitButton.text();
            
            console.log('Original button text:', originalText);
            
            submitButton.prop('disabled', true).text('Saving...');
            
            var formData = {};
            var fieldCount = 0;
            
            form.find('input, textarea, select').each(function() {
                var input = $(this);
                var name = input.attr('name');
                
                if (name && name !== 'cpd_settings_nonce' && name !== '_wp_http_referer') {
                    fieldCount++;
                    if (input.attr('type') === 'checkbox') {
                        formData[name] = input.is(':checked') ? '1' : '0';
                    } else {
                        formData[name] = input.val();
                    }
                }
            });
            
            console.log('Fields collected:', fieldCount);
            
            // Get content from wp_editor
            if (typeof tinyMCE !== 'undefined') {
                var editor = tinyMCE.get('notice_board_content');
                if (editor) {
                    formData['notice_board_content'] = editor.getContent();
                    console.log('TinyMCE content retrieved');
                } else {
                    console.log('TinyMCE editor not found');
                }
            } else {
                console.log('TinyMCE not available');
            }
            
            console.log('Form data to save:', formData);
            console.log('Number of settings:', Object.keys(formData).length);

            var ajaxData = {
                action: 'cpd_save_settings',
                nonce: cpdAdmin.nonce,
                settings: formData
            };
            
            console.log('AJAX request data:', ajaxData);
            console.log('AJAX URL:', cpdAdmin.ajaxUrl);

            $.ajax({
                url: cpdAdmin.ajaxUrl,
                type: 'POST',
                data: ajaxData,
                beforeSend: function(xhr) {
                    console.log('=== AJAX Request Starting ===');
                },
                success: function(response) {
                    console.log('=== AJAX Success ===');
                    console.log('Response:', response);
                    console.log('Response type:', typeof response);
                    console.log('Response.success:', response.success);
                    
                    if (response.success) {
                        showNotice('success', response.data.message);
                        console.log('Settings saved successfully');

                        // Reload page if subdomain changed
                        if (formData.dashboard_subdomain || formData.editor_subdomain) {
                            console.log('Subdomain changed, reloading page...');
                            setTimeout(function() {
                                location.reload();
                            }, 1500);
                        }
                    } else {
                        var errorMsg = response.data && response.data.message ? response.data.message : 'Failed to save settings.';
                        showNotice('error', errorMsg);
                        console.error('Save failed:', response);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('=== AJAX Error ===');
                    console.error('Status:', status);
                    console.error('Error:', error);
                    console.error('XHR Status:', xhr.status);
                    console.error('XHR Response:', xhr.responseText);
                    console.error('XHR Object:', xhr);
                    
                    showNotice('error', 'An error occurred while saving settings. Check console for details.');
                },
                complete: function() {
                    console.log('=== AJAX Complete ===');
                    submitButton.prop('disabled', false).text(originalText);
                }
            });
            
            return false;
        });
        
        console.log('Form submit handler attached');
        
        // Test if handler is attached
        var events = $._data($('#cpd-settings-form')[0], 'events');
        console.log('Form events:', events);
        
        // Add user
        $('#cpd-add-user-form').on('submit', function(e) {
            e.preventDefault();
            console.log('Add user form submitted');
            
            var form = $(this);
            var submitButton = form.find('button[type="submit"]');
            var originalText = submitButton.text();
            
            submitButton.prop('disabled', true).text('Adding...');
            
            $.ajax({
                url: cpdAdmin.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'cpd_create_editor_user',
                    nonce: cpdAdmin.nonce,
                    username: $('#new_username').val(),
                    password: $('#new_password').val(),
                    email: $('#new_email').val()
                },
                success: function(response) {
                    console.log('Add user response:', response);
                    if (response.success) {
                        showUserNotice('success', response.data.message);
                        form[0].reset();
                        
                        // Reload page to show new user
                        setTimeout(function() {
                            location.reload();
                        }, 1500);
                    } else {
                        showUserNotice('error', response.data.message);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Add user error:', error);
                    showUserNotice('error', 'An error occurred while adding user.');
                },
                complete: function() {
                    submitButton.prop('disabled', false).text(originalText);
                }
            });
        });
        
        // Generate password
        $('#cpd-generate-password').on('click', function() {
            var password = generatePassword(16);
            $('#new_password').val(password);
            console.log('Password generated');
            
            // Show password temporarily
            var passwordInput = $('#new_password');
            var originalType = passwordInput.attr('type');
            passwordInput.attr('type', 'text');
            
            setTimeout(function() {
                passwordInput.attr('type', originalType);
            }, 3000);
        });
        
        // Delete user
        $(document).on('click', '.cpd-delete-user', function() {
            if (!confirm('Are you sure you want to delete this user?')) {
                return;
            }
            
            console.log('Delete user clicked');
            
            var button = $(this);
            var userId = button.data('user-id');
            var row = button.closest('tr');
            
            button.prop('disabled', true).text('Deleting...');
            
            $.ajax({
                url: cpdAdmin.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'cpd_delete_editor_user',
                    nonce: cpdAdmin.nonce,
                    user_id: userId
                },
                success: function(response) {
                    console.log('Delete user response:', response);
                    if (response.success) {
                        showUserNotice('success', response.data.message);
                        row.fadeOut(function() {
                            row.remove();
                        });
                    } else {
                        showUserNotice('error', response.data.message);
                        button.prop('disabled', false).text('Delete');
                    }
                },
                error: function() {
                    console.error('Delete user error');
                    showUserNotice('error', 'An error occurred while deleting user.');
                    button.prop('disabled', false).text('Delete');
                }
            });
        });
        
        // Helper functions
        function showNotice(type, message) {
            console.log('Showing notice:', type, message);
            var notice = $('#cpd-save-notice');
            notice.removeClass('notice-success notice-error')
                  .addClass('notice notice-' + type)
                  .find('p').text(message);
            notice.slideDown();
            
            setTimeout(function() {
                notice.slideUp();
            }, 5000);
        }
        
        function showUserNotice(type, message) {
            console.log('Showing user notice:', type, message);
            var notice = $('#cpd-user-notice');
            notice.removeClass('notice-success notice-error')
                  .addClass('notice notice-' + type)
                  .find('p').text(message);
            notice.slideDown();
            
            setTimeout(function() {
                notice.slideUp();
            }, 5000);
        }
        
        function generatePassword(length) {
            var charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
            var password = '';
            
            for (var i = 0; i < length; i++) {
                password += charset.charAt(Math.floor(Math.random() * charset.length));
            }
            
            return password;
        }
        
        console.log('=== Admin Script Initialization Complete ===');
    });
    
})(jQuery);

