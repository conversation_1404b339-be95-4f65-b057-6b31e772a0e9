/**
 * Editor JavaScript
 */

(function() {
    'use strict';
    
    // State
    let isAuthenticated = false;
    let currentUser = null;
    let aiProvider = localStorage.getItem('cpd_ai_provider') || '';
    let aiApiKey = localStorage.getItem('cpd_ai_api_key') || '';
    let aiModel = localStorage.getItem('cpd_ai_model') || '';
    
    // Initialize
    document.addEventListener('DOMContentLoaded', function() {
        checkAuthentication();
        initLoginForm();
        initEditorHandlers();
        loadAIConfig();
    });
    
    // Check Authentication
    function checkAuthentication() {
        fetch(window.cpdEditor.restUrl + 'editor/check-auth', {
            headers: {
                'X-WP-Nonce': window.cpdEditor.nonce
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.authenticated) {
                isAuthenticated = true;
                currentUser = data.user;
                showEditorScreen();
            } else {
                showLoginScreen();
            }
        })
        .catch(error => {
            console.error('Auth check failed:', error);
            showLoginScreen();
        });
    }
    
    // Show Login Screen
    function showLoginScreen() {
        document.getElementById('cpd-login-screen').style.display = 'flex';
        document.getElementById('cpd-editor-screen').style.display = 'none';
    }
    
    // Show Editor Screen
    function showEditorScreen() {
        document.getElementById('cpd-login-screen').style.display = 'none';
        document.getElementById('cpd-editor-screen').style.display = 'block';
        
        if (currentUser) {
            document.getElementById('cpd-user-info').textContent = 'Logged in as: ' + currentUser.username;
        }
    }
    
    // Initialize Login Form
    function initLoginForm() {
        const form = document.getElementById('cpd-login-form');
        if (!form) return;
        
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const username = document.getElementById('login-username').value;
            const password = document.getElementById('login-password').value;
            const submitBtn = form.querySelector('button[type="submit"]');
            const errorDiv = document.getElementById('login-error');
            
            submitBtn.classList.add('loading');
            submitBtn.disabled = true;
            errorDiv.style.display = 'none';
            
            fetch(window.cpdEditor.restUrl + 'editor/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-WP-Nonce': window.cpdEditor.nonce
                },
                body: JSON.stringify({ username, password })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    isAuthenticated = true;
                    currentUser = data.user;
                    showEditorScreen();
                } else {
                    errorDiv.textContent = data.message || 'Login failed. Please try again.';
                    errorDiv.style.display = 'block';
                }
            })
            .catch(error => {
                errorDiv.textContent = 'An error occurred. Please try again.';
                errorDiv.style.display = 'block';
            })
            .finally(() => {
                submitBtn.classList.remove('loading');
                submitBtn.disabled = false;
            });
        });
    }
    
    // Initialize Editor Handlers
    function initEditorHandlers() {
        // Logout
        const logoutBtn = document.getElementById('cpd-logout-btn');
        if (logoutBtn) {
            logoutBtn.addEventListener('click', function() {
                fetch(window.cpdEditor.restUrl + 'editor/logout', {
                    method: 'POST',
                    headers: {
                        'X-WP-Nonce': window.cpdEditor.nonce
                    }
                })
                .then(() => {
                    isAuthenticated = false;
                    currentUser = null;
                    showLoginScreen();
                })
                .catch(error => {
                    console.error('Logout failed:', error);
                });
            });
        }
        
        // Tab Navigation
        document.querySelectorAll('.cpd-tab-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const tabName = this.dataset.tab;
                
                // Update buttons
                document.querySelectorAll('.cpd-tab-btn').forEach(b => b.classList.remove('active'));
                this.classList.add('active');
                
                // Update content
                document.querySelectorAll('.cpd-tab-content').forEach(c => c.classList.remove('active'));
                document.getElementById('tab-' + tabName).classList.add('active');
                
                // Load data for specific tabs
                if (tabName === 'manage-programmes') {
                    loadProgrammes();
                }
            });
        });
        
        // AI Config
        initAIHandlers();
        
        // Image Upload
        initImageUploadHandlers();
        
        // Extract Buttons
        initExtractButtons();
        
        // Manual Entry
        initManualEntryForm();
        
        // Filter
        initFilterHandlers();
    }
    
    // Load AI Config
    function loadAIConfig() {
        const providerSelect = document.getElementById('ai-provider');
        const apiKeyInput = document.getElementById('ai-api-key');
        const modelSelect = document.getElementById('ai-model');
        
        if (providerSelect && aiProvider) {
            providerSelect.value = aiProvider;
        }
        
        if (apiKeyInput && aiApiKey) {
            apiKeyInput.value = aiApiKey;
        }
        
        if (modelSelect && aiModel) {
            modelSelect.value = aiModel;
        }
    }
    
    // Initialize AI Handlers
    function initAIHandlers() {
        const providerSelect = document.getElementById('ai-provider');
        const apiKeyInput = document.getElementById('ai-api-key');
        const fetchModelsBtn = document.getElementById('fetch-models-btn');
        const modelSelect = document.getElementById('ai-model');
        
        // Save provider
        if (providerSelect) {
            providerSelect.addEventListener('change', function() {
                aiProvider = this.value;
                localStorage.setItem('cpd_ai_provider', aiProvider);
            });
        }
        
        // Save API key
        if (apiKeyInput) {
            apiKeyInput.addEventListener('change', function() {
                aiApiKey = this.value;
                localStorage.setItem('cpd_ai_api_key', aiApiKey);
            });
        }
        
        // Fetch models
        if (fetchModelsBtn) {
            fetchModelsBtn.addEventListener('click', function() {
                if (!aiProvider || !aiApiKey) {
                    alert('Please select a provider and enter your API key.');
                    return;
                }
                
                const btn = this;
                btn.classList.add('loading');
                btn.disabled = true;
                
                fetch(window.cpdEditor.restUrl + 'ai/models/' + aiProvider, {
                    headers: {
                        'X-WP-Nonce': window.cpdEditor.nonce,
                        'X-API-Key': aiApiKey
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.models && data.models.length > 0) {
                        modelSelect.innerHTML = '<option value="">Select a model</option>';
                        data.models.forEach(model => {
                            const option = document.createElement('option');
                            option.value = model.id;
                            option.textContent = model.name;
                            modelSelect.appendChild(option);
                        });
                        
                        document.getElementById('model-select-group').style.display = 'block';
                        
                        if (aiModel) {
                            modelSelect.value = aiModel;
                        }
                    } else {
                        alert('No vision-capable models found for this provider.');
                    }
                })
                .catch(error => {
                    alert('Failed to fetch models. Please check your API key and try again.');
                    console.error(error);
                })
                .finally(() => {
                    btn.classList.remove('loading');
                    btn.disabled = false;
                });
            });
        }
        
        // Save model
        if (modelSelect) {
            modelSelect.addEventListener('change', function() {
                aiModel = this.value;
                localStorage.setItem('cpd_ai_model', aiModel);
            });
        }
    }
    
    // Initialize Image Upload Handlers
    function initImageUploadHandlers() {
        const imageInputs = document.querySelectorAll('.cpd-file-input');
        
        imageInputs.forEach(input => {
            input.addEventListener('change', function() {
                const file = this.files[0];
                if (!file) return;
                
                const previewId = 'preview-' + this.id.replace('image-', '');
                const preview = document.getElementById(previewId);
                
                if (preview) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        preview.innerHTML = '<img src="' + e.target.result + '" alt="Preview">';
                        preview.style.display = 'block';
                    };
                    reader.readAsDataURL(file);
                }
            });
        });
    }
    
    // Initialize Extract Buttons
    function initExtractButtons() {
        const extractBtns = document.querySelectorAll('.cpd-extract-btn, .cpd-btn[data-type]');
        
        extractBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const programmeType = this.dataset.type;
                const imageInputId = this.dataset.image;
                
                extractProgrammeData(programmeType, imageInputId, this);
            });
        });
    }
    
    // Extract Programme Data
    function extractProgrammeData(programmeType, imageInputId, button) {
        if (!aiProvider || !aiApiKey || !aiModel) {
            alert('Please configure AI settings first.');
            return;
        }
        
        const imageInput = document.getElementById(imageInputId);
        const file = imageInput.files[0];
        
        if (!file) {
            alert('Please select an image first.');
            return;
        }
        
        button.classList.add('loading');
        button.disabled = true;
        
        const resultId = 'result-' + imageInputId.replace('image-', '');
        const resultDiv = document.getElementById(resultId);
        
        // Convert image to base64
        const reader = new FileReader();
        reader.onload = function(e) {
            const imageData = e.target.result;
            
            // Call API
            fetch(window.cpdEditor.restUrl + 'ai/extract', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-WP-Nonce': window.cpdEditor.nonce,
                    'X-API-Key': aiApiKey
                },
                body: JSON.stringify({
                    provider: aiProvider,
                    model: aiModel,
                    image_url: imageData,
                    programme_type: programmeType
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    resultDiv.innerHTML = '<p><strong>Success!</strong> ' + data.message + '</p>';
                    resultDiv.classList.remove('error');
                    resultDiv.style.display = 'block';
                    
                    // Reload programmes list if on that tab
                    if (document.getElementById('tab-manage-programmes').classList.contains('active')) {
                        loadProgrammes();
                    }
                } else {
                    resultDiv.innerHTML = '<p><strong>Error:</strong> ' + (data.message || 'Extraction failed') + '</p>';
                    resultDiv.classList.add('error');
                    resultDiv.style.display = 'block';
                }
            })
            .catch(error => {
                resultDiv.innerHTML = '<p><strong>Error:</strong> An error occurred during extraction.</p>';
                resultDiv.classList.add('error');
                resultDiv.style.display = 'block';
                console.error(error);
            })
            .finally(() => {
                button.classList.remove('loading');
                button.disabled = false;
            });
        };
        
        reader.readAsDataURL(file);
    }
    
    // Initialize Manual Entry Form
    function initManualEntryForm() {
        const form = document.getElementById('manual-entry-form');
        const typeSelect = document.getElementById('manual-type');
        const fieldsContainer = document.getElementById('manual-fields-container');

        if (!form || !typeSelect || !fieldsContainer) return;

        // Generate fields when programme type changes
        typeSelect.addEventListener('change', function() {
            const type = this.value;
            if (type) {
                fieldsContainer.innerHTML = generateFieldsForType(type);
            } else {
                fieldsContainer.innerHTML = '';
            }
        });

        // Handle form submission
        form.addEventListener('submit', function(e) {
            e.preventDefault();

            const type = typeSelect.value;
            const date = document.getElementById('manual-date').value;
            const time = document.getElementById('manual-time').value;

            if (!type || !date || !time) {
                alert('Please fill in all required fields.');
                return;
            }

            // Collect programme data based on type
            const programmeData = collectProgrammeData(type);

            if (!programmeData) {
                alert('Please fill in all programme details.');
                return;
            }

            const submitBtn = form.querySelector('button[type="submit"]');
            submitBtn.classList.add('loading');
            submitBtn.disabled = true;

            // Check if this is an edit or create
            const editId = form.dataset.editId;
            const isEdit = editId && editId !== '';

            const url = isEdit
                ? window.cpdEditor.restUrl + 'editor/programmes/' + editId
                : window.cpdEditor.restUrl + 'editor/programmes';

            const method = isEdit ? 'PUT' : 'POST';

            // Submit to REST API
            fetch(url, {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                    'X-WP-Nonce': window.cpdEditor.nonce
                },
                body: JSON.stringify({
                    type: type,
                    date: date,
                    time: time,
                    data: programmeData
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(isEdit ? 'Programme updated successfully!' : 'Programme saved successfully!');
                    form.reset();
                    fieldsContainer.innerHTML = '';
                    delete form.dataset.editId;

                    // Reset button text
                    submitBtn.innerHTML = '<span class="cpd-btn-text">Save Programme</span><span class="cpd-btn-loading"><span class="cpd-spinner-small"></span> Saving...</span>';

                    // Reload programmes list if on that tab
                    if (document.getElementById('tab-manage-programmes').classList.contains('active')) {
                        loadProgrammes();
                    }
                } else {
                    alert('Error: ' + (data.message || 'Failed to save programme'));
                }
            })
            .catch(error => {
                alert('An error occurred while saving the programme.');
                console.error(error);
            })
            .finally(() => {
                submitBtn.classList.remove('loading');
                submitBtn.disabled = false;
            });
        });
    }

    // Generate fields based on programme type
    function generateFieldsForType(type) {
        let html = '<div class="cpd-dynamic-fields">';

        switch (type) {
            case 'jingiaseng_1pm':
                html += `
                    <div class="cpd-form-group">
                        <label for="field-nongiathuh">NONGIATHUH KHANA POR 1:00PM</label>
                        <input type="text" id="field-nongiathuh" class="cpd-input" placeholder="Enter participant names">
                    </div>
                `;
                break;

            case 'miet_balang':
                html += `
                    <div class="cpd-form-group">
                        <label for="field-pule-sdang">PULE SDANG & DUWAI</label>
                        <input type="text" id="field-pule-sdang" class="cpd-input" placeholder="Enter participant names">
                    </div>
                    <div class="cpd-form-group">
                        <label for="field-nongkren">NONGKREN</label>
                        <input type="text" id="field-nongkren" class="cpd-input" placeholder="Enter participant names">
                    </div>
                    <div class="cpd-form-group">
                        <label for="field-khubor">KHUBOR</label>
                        <input type="text" id="field-khubor" class="cpd-input" placeholder="Enter participant names">
                    </div>
                `;
                break;

            case 'jingiaseng_samla':
                html += `
                    <div class="cpd-form-group">
                        <label>
                            <input type="checkbox" id="field-special-item-check"> This is a special item
                        </label>
                    </div>
                    <div id="special-item-fields" style="display: none;">
                        <div class="cpd-form-group">
                            <label for="field-special-item">Special Item Name</label>
                            <input type="text" id="field-special-item" class="cpd-input" placeholder="e.g., JINGIASENG IALAP">
                        </div>
                    </div>
                    <div id="regular-samla-fields">
                        <div class="cpd-form-group">
                            <label for="field-pulesdang">PULESDANG & DUWAI</label>
                            <input type="text" id="field-pulesdang" class="cpd-input" placeholder="Enter participant names">
                        </div>
                        <div class="cpd-form-group">
                            <label for="field-jingainguh">JINGAINGUH</label>
                            <input type="text" id="field-jingainguh" class="cpd-input" placeholder="Enter participant names">
                        </div>
                        <div class="cpd-form-group">
                            <label for="field-special-no">SPECIAL NO.</label>
                            <input type="text" id="field-special-no" class="cpd-input" placeholder="Enter special number">
                        </div>
                        <div class="cpd-form-group">
                            <label for="field-nongkren-samla">NONGKREN</label>
                            <input type="text" id="field-nongkren-samla" class="cpd-input" placeholder="Enter participant names">
                        </div>
                    </div>
                `;

                // Add event listener for special item checkbox
                setTimeout(() => {
                    const checkbox = document.getElementById('field-special-item-check');
                    const specialFields = document.getElementById('special-item-fields');
                    const regularFields = document.getElementById('regular-samla-fields');

                    if (checkbox) {
                        checkbox.addEventListener('change', function() {
                            if (this.checked) {
                                specialFields.style.display = 'block';
                                regularFields.style.display = 'none';
                            } else {
                                specialFields.style.display = 'none';
                                regularFields.style.display = 'block';
                            }
                        });
                    }
                }, 100);
                break;

            case 'jingiaseng_khynnah':
                html += `
                    <div class="cpd-form-group">
                        <label for="field-jingrwai-iaroh">JINGRWAI IAROH</label>
                        <input type="text" id="field-jingrwai-iaroh" class="cpd-input" placeholder="Enter participant names">
                    </div>
                    <div class="cpd-form-row">
                        <div class="cpd-form-group">
                            <label for="field-nongpule-old">NONGPULE SDANG - OLD TESTAMENT</label>
                            <input type="text" id="field-nongpule-old" class="cpd-input" placeholder="Enter participant names">
                        </div>
                        <div class="cpd-form-group">
                            <label for="field-nongpule-new">NONGPULE SDANG - NEW TESTAMENT</label>
                            <input type="text" id="field-nongpule-new" class="cpd-input" placeholder="Enter participant names">
                        </div>
                    </div>
                    <div class="cpd-form-group">
                        <label for="field-nong-duwai">NONG DUWAI</label>
                        <input type="text" id="field-nong-duwai" class="cpd-input" placeholder="Enter participant names">
                    </div>
                    <div class="cpd-form-group">
                        <label for="field-lum-jingainguh">LUM JINGAINGUH</label>
                        <input type="text" id="field-lum-jingainguh" class="cpd-input" placeholder="Enter participant names">
                    </div>
                    <div class="cpd-form-group">
                        <label for="field-jingrwai-kyrpang">JINGRWAI KYRPANG</label>
                        <input type="text" id="field-jingrwai-kyrpang" class="cpd-input" placeholder="Enter participant names">
                    </div>
                    <div class="cpd-form-group">
                        <label for="field-duwai-jingainguh">DUWAI JINGAINGUH</label>
                        <input type="text" id="field-duwai-jingainguh" class="cpd-input" placeholder="Enter participant names">
                    </div>
                    <div class="cpd-form-group">
                        <label for="field-nongkren-activities">NONGKREN/ACTIVITIES</label>
                        <input type="text" id="field-nongkren-activities" class="cpd-input" placeholder="Enter participant names or activities">
                    </div>
                `;
                break;

            case 'jingiaseng_iing':
                html += `
                    <h4 style="margin-top: 1rem; margin-bottom: 0.5rem; color: var(--primary-color);">ZONE-1</h4>
                    <div class="cpd-form-group">
                        <label for="field-zone1-ing">ING</label>
                        <input type="text" id="field-zone1-ing" class="cpd-input" placeholder="Enter participant names">
                    </div>
                    <div class="cpd-form-group">
                        <label for="field-zone1-nongpule">NONGPULE & DUWAI</label>
                        <input type="text" id="field-zone1-nongpule" class="cpd-input" placeholder="Enter participant names">
                    </div>
                    <div class="cpd-form-group">
                        <label for="field-zone1-nongkren">NONGKREN</label>
                        <input type="text" id="field-zone1-nongkren" class="cpd-input" placeholder="Enter participant names">
                    </div>

                    <h4 style="margin-top: 1.5rem; margin-bottom: 0.5rem; color: var(--primary-color);">ZONE-2</h4>
                    <div class="cpd-form-group">
                        <label for="field-zone2-ing">ING</label>
                        <input type="text" id="field-zone2-ing" class="cpd-input" placeholder="Enter participant names">
                    </div>
                    <div class="cpd-form-group">
                        <label for="field-zone2-nongpule">NONGPULE & DUWAI</label>
                        <input type="text" id="field-zone2-nongpule" class="cpd-input" placeholder="Enter participant names">
                    </div>
                    <div class="cpd-form-group">
                        <label for="field-zone2-nongkren">NONGKREN</label>
                        <input type="text" id="field-zone2-nongkren" class="cpd-input" placeholder="Enter participant names">
                    </div>
                `;
                break;
        }

        html += '</div>';
        return html;
    }

    // Collect programme data from form fields
    function collectProgrammeData(type) {
        let data = {};

        switch (type) {
            case 'jingiaseng_1pm':
                data.nongiathuh_khana_por = document.getElementById('field-nongiathuh')?.value || '';
                break;

            case 'miet_balang':
                data.pule_sdang_duwai = document.getElementById('field-pule-sdang')?.value || '';
                data.nongkren = document.getElementById('field-nongkren')?.value || '';
                data.khubor = document.getElementById('field-khubor')?.value || '';
                break;

            case 'jingiaseng_samla':
                const isSpecialItem = document.getElementById('field-special-item-check')?.checked;

                if (isSpecialItem) {
                    data.special_item = document.getElementById('field-special-item')?.value || '';
                    data.pulesdang_duwai = '';
                    data.jingainguh = '';
                    data.special_no = '';
                    data.nongkren = '';
                } else {
                    data.pulesdang_duwai = document.getElementById('field-pulesdang')?.value || '';
                    data.jingainguh = document.getElementById('field-jingainguh')?.value || '';
                    data.special_no = document.getElementById('field-special-no')?.value || '';
                    data.nongkren = document.getElementById('field-nongkren-samla')?.value || '';
                    data.special_item = '';
                }
                break;

            case 'jingiaseng_khynnah':
                data.jingrwai_iaroh = document.getElementById('field-jingrwai-iaroh')?.value || '';
                data.nongpule_sdang_old_testament = document.getElementById('field-nongpule-old')?.value || '';
                data.nongpule_sdang_new_testament = document.getElementById('field-nongpule-new')?.value || '';
                data.nong_duwai = document.getElementById('field-nong-duwai')?.value || '';
                data.lum_jingainguh = document.getElementById('field-lum-jingainguh')?.value || '';
                data.jingrwai_kyrpang = document.getElementById('field-jingrwai-kyrpang')?.value || '';
                data.duwai_jingainguh = document.getElementById('field-duwai-jingainguh')?.value || '';
                data.nongkren_activities = document.getElementById('field-nongkren-activities')?.value || '';
                break;

            case 'jingiaseng_iing':
                data.zone_1 = {
                    ing: document.getElementById('field-zone1-ing')?.value || '',
                    nongpule_duwai: document.getElementById('field-zone1-nongpule')?.value || '',
                    nongkren: document.getElementById('field-zone1-nongkren')?.value || ''
                };
                data.zone_2 = {
                    ing: document.getElementById('field-zone2-ing')?.value || '',
                    nongpule_duwai: document.getElementById('field-zone2-nongpule')?.value || '',
                    nongkren: document.getElementById('field-zone2-nongkren')?.value || ''
                };
                break;
        }

        return data;
    }

    // Initialize Filter Handlers
    function initFilterHandlers() {
        const applyFilterBtn = document.getElementById('apply-filter-btn');
        const clearFilterBtn = document.getElementById('clear-filter-btn');

        if (applyFilterBtn) {
            applyFilterBtn.addEventListener('click', function() {
                loadProgrammes();
            });
        }

        if (clearFilterBtn) {
            clearFilterBtn.addEventListener('click', function() {
                document.getElementById('filter-type').value = '';
                document.getElementById('filter-start-date').value = '';
                document.getElementById('filter-end-date').value = '';
                loadProgrammes();
            });
        }
    }

    // Load Programmes
    function loadProgrammes() {
        const listContainer = document.getElementById('programmes-list');
        if (!listContainer) return;

        // Show loading state
        listContainer.innerHTML = '<p style="text-align: center; padding: 2rem; color: #718096;">Loading programmes...</p>';

        // Get filter values
        const filterType = document.getElementById('filter-type')?.value || '';
        const filterStartDate = document.getElementById('filter-start-date')?.value || '';
        const filterEndDate = document.getElementById('filter-end-date')?.value || '';

        // Build query string
        let queryParams = [];
        if (filterType) queryParams.push('type=' + encodeURIComponent(filterType));
        if (filterStartDate) queryParams.push('start_date=' + encodeURIComponent(filterStartDate));
        if (filterEndDate) queryParams.push('end_date=' + encodeURIComponent(filterEndDate));

        const queryString = queryParams.length > 0 ? '?' + queryParams.join('&') : '';

        // Fetch programmes
        fetch(window.cpdEditor.restUrl + 'editor/programmes' + queryString, {
            headers: {
                'X-WP-Nonce': window.cpdEditor.nonce
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success && data.programmes && data.programmes.length > 0) {
                renderProgrammesList(data.programmes);
            } else {
                listContainer.innerHTML = '<p style="text-align: center; padding: 2rem; color: #718096;">No programmes found. Try adjusting your filters or add a new programme.</p>';
            }
        })
        .catch(error => {
            listContainer.innerHTML = '<p style="text-align: center; padding: 2rem; color: #f56565;">Error loading programmes. Please try again.</p>';
            console.error('Error loading programmes:', error);
        });
    }

    // Render Programmes List
    function renderProgrammesList(programmes) {
        const listContainer = document.getElementById('programmes-list');
        if (!listContainer) return;

        let html = '';

        programmes.forEach(programme => {
            const typeLabel = getProgrammeTypeLabel(programme.programme_type);
            const formattedDate = formatDate(programme.programme_date);
            const formattedTime = formatTime(programme.programme_time);

            html += `
                <div class="cpd-programme-item" data-id="${programme.id}">
                    <div class="cpd-programme-info">
                        <h4>${typeLabel}</h4>
                        <div class="cpd-programme-meta">
                            <span><strong>Date:</strong> ${formattedDate}</span> |
                            <span><strong>Time:</strong> ${formattedTime}</span>
                        </div>
                        <div class="cpd-programme-preview" style="margin-top: 0.5rem; font-size: 0.85rem; color: #718096;">
                            ${generateProgrammePreview(programme)}
                        </div>
                    </div>
                    <div class="cpd-programme-actions">
                        <button class="cpd-btn cpd-btn-secondary cpd-edit-programme-btn" data-id="${programme.id}">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                                <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                            </svg>
                            Edit
                        </button>
                        <button class="cpd-btn cpd-btn-danger cpd-delete-programme-btn" data-id="${programme.id}">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <polyline points="3 6 5 6 21 6"></polyline>
                                <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
                            </svg>
                            Delete
                        </button>
                    </div>
                </div>
            `;
        });

        listContainer.innerHTML = html;

        // Attach event listeners
        attachProgrammeActionListeners();
    }

    // Attach event listeners to programme action buttons
    function attachProgrammeActionListeners() {
        // Edit buttons
        document.querySelectorAll('.cpd-edit-programme-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const programmeId = this.dataset.id;
                editProgramme(programmeId);
            });
        });

        // Delete buttons
        document.querySelectorAll('.cpd-delete-programme-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const programmeId = this.dataset.id;
                deleteProgramme(programmeId);
            });
        });
    }

    // Edit Programme
    function editProgramme(programmeId) {
        // Fetch programme details
        fetch(window.cpdEditor.restUrl + 'editor/programmes/' + programmeId, {
            headers: {
                'X-WP-Nonce': window.cpdEditor.nonce
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success && data.programme) {
                // Switch to manual entry tab
                document.querySelector('[data-tab="manual-entry"]').click();

                // Populate form with programme data
                populateEditForm(data.programme);
            } else {
                alert('Failed to load programme details.');
            }
        })
        .catch(error => {
            alert('Error loading programme details.');
            console.error(error);
        });
    }

    // Populate edit form with programme data
    function populateEditForm(programme) {
        const form = document.getElementById('manual-entry-form');
        const typeSelect = document.getElementById('manual-type');
        const dateInput = document.getElementById('manual-date');
        const timeInput = document.getElementById('manual-time');

        // Set basic fields
        typeSelect.value = programme.programme_type;
        dateInput.value = programme.programme_date;
        timeInput.value = programme.programme_time;

        // Trigger change event to generate fields
        typeSelect.dispatchEvent(new Event('change'));

        // Wait for fields to be generated, then populate them
        setTimeout(() => {
            const programmeData = JSON.parse(programme.programme_data);
            populateFieldsWithData(programme.programme_type, programmeData);

            // Store programme ID for update
            form.dataset.editId = programme.id;

            // Change submit button text
            const submitBtn = form.querySelector('button[type="submit"]');
            submitBtn.innerHTML = '<span class="cpd-btn-text">Update Programme</span><span class="cpd-btn-loading"><span class="cpd-spinner-small"></span> Updating...</span>';
        }, 200);
    }

    // Populate fields with data
    function populateFieldsWithData(type, data) {
        switch (type) {
            case 'jingiaseng_1pm':
                document.getElementById('field-nongiathuh').value = data.nongiathuh_khana_por || '';
                break;

            case 'miet_balang':
                document.getElementById('field-pule-sdang').value = data.pule_sdang_duwai || '';
                document.getElementById('field-nongkren').value = data.nongkren || '';
                document.getElementById('field-khubor').value = data.khubor || '';
                break;

            case 'jingiaseng_samla':
                if (data.special_item) {
                    document.getElementById('field-special-item-check').checked = true;
                    document.getElementById('field-special-item-check').dispatchEvent(new Event('change'));
                    document.getElementById('field-special-item').value = data.special_item;
                } else {
                    document.getElementById('field-pulesdang').value = data.pulesdang_duwai || '';
                    document.getElementById('field-jingainguh').value = data.jingainguh || '';
                    document.getElementById('field-special-no').value = data.special_no || '';
                    document.getElementById('field-nongkren-samla').value = data.nongkren || '';
                }
                break;

            case 'jingiaseng_khynnah':
                document.getElementById('field-jingrwai-iaroh').value = data.jingrwai_iaroh || '';
                document.getElementById('field-nongpule-old').value = data.nongpule_sdang_old_testament || '';
                document.getElementById('field-nongpule-new').value = data.nongpule_sdang_new_testament || '';
                document.getElementById('field-nong-duwai').value = data.nong_duwai || '';
                document.getElementById('field-lum-jingainguh').value = data.lum_jingainguh || '';
                document.getElementById('field-jingrwai-kyrpang').value = data.jingrwai_kyrpang || '';
                document.getElementById('field-duwai-jingainguh').value = data.duwai_jingainguh || '';
                document.getElementById('field-nongkren-activities').value = data.nongkren_activities || '';
                break;

            case 'jingiaseng_iing':
                document.getElementById('field-zone1-ing').value = data.zone_1?.ing || '';
                document.getElementById('field-zone1-nongpule').value = data.zone_1?.nongpule_duwai || '';
                document.getElementById('field-zone1-nongkren').value = data.zone_1?.nongkren || '';
                document.getElementById('field-zone2-ing').value = data.zone_2?.ing || '';
                document.getElementById('field-zone2-nongpule').value = data.zone_2?.nongpule_duwai || '';
                document.getElementById('field-zone2-nongkren').value = data.zone_2?.nongkren || '';
                break;
        }
    }

    // Delete Programme
    function deleteProgramme(programmeId) {
        if (!confirm('Are you sure you want to delete this programme? This action cannot be undone.')) {
            return;
        }

        fetch(window.cpdEditor.restUrl + 'editor/programmes/' + programmeId, {
            method: 'DELETE',
            headers: {
                'X-WP-Nonce': window.cpdEditor.nonce
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Programme deleted successfully.');
                loadProgrammes();
            } else {
                alert('Error: ' + (data.message || 'Failed to delete programme'));
            }
        })
        .catch(error => {
            alert('An error occurred while deleting the programme.');
            console.error(error);
        });
    }

    // Helper: Get programme type label
    function getProgrammeTypeLabel(type) {
        const labels = {
            'jingiaseng_1pm': 'JINGIASENG 1:00 Baje',
            'miet_balang': 'MIET BALANG',
            'jingiaseng_samla': 'JINGIASENG SAMLA',
            'jingiaseng_khynnah': 'JINGIASENG KHYNNAH',
            'jingiaseng_iing': 'JINGIASENG IING'
        };
        return labels[type] || type;
    }

    // Helper: Format date
    function formatDate(dateString) {
        const date = new Date(dateString + 'T00:00:00');
        const options = { year: 'numeric', month: 'long', day: 'numeric' };
        return date.toLocaleDateString('en-US', options);
    }

    // Helper: Format time
    function formatTime(timeString) {
        if (!timeString) {
            return 'N/A';
        }

        // If already formatted (contains AM/PM), return as is
        if (timeString.includes('AM') || timeString.includes('PM') || timeString.includes('am') || timeString.includes('pm')) {
            return timeString;
        }

        const parts = timeString.split(':');
        if (parts.length < 2) {
            return timeString; // Return as is if not in expected format
        }

        const [hours, minutes] = parts;
        const hour = parseInt(hours);
        const ampm = hour >= 12 ? 'PM' : 'AM';
        const displayHour = hour > 12 ? hour - 12 : (hour === 0 ? 12 : hour);
        return `${displayHour}:${minutes} ${ampm}`;
    }

    // Helper: Generate programme preview
    function generateProgrammePreview(programme) {
        try {
            const data = JSON.parse(programme.programme_data);
            let preview = '';

            switch (programme.programme_type) {
                case 'jingiaseng_1pm':
                    preview = `<strong>NONGIATHUH:</strong> ${data.nongiathuh_khana_por || 'N/A'}`;
                    break;

                case 'miet_balang':
                    preview = `<strong>PULE SDANG:</strong> ${data.pule_sdang_duwai || 'N/A'} | <strong>NONGKREN:</strong> ${data.nongkren || 'N/A'}`;
                    break;

                case 'jingiaseng_samla':
                    if (data.special_item) {
                        preview = `<strong>SPECIAL ITEM:</strong> ${data.special_item}`;
                    } else {
                        preview = `<strong>PULESDANG:</strong> ${data.pulesdang_duwai || 'N/A'} | <strong>JINGAINGUH:</strong> ${data.jingainguh || 'N/A'}`;
                    }
                    break;

                case 'jingiaseng_khynnah':
                    preview = `<strong>JINGRWAI IAROH:</strong> ${data.jingrwai_iaroh || 'N/A'} | <strong>LUM JINGAINGUH:</strong> ${data.lum_jingainguh || 'N/A'}`;
                    break;

                case 'jingiaseng_iing':
                    preview = `<strong>Zone 1 ING:</strong> ${data.zone_1?.ing || 'N/A'} | <strong>Zone 2 ING:</strong> ${data.zone_2?.ing || 'N/A'}`;
                    break;

                default:
                    preview = 'Programme details available';
            }

            return preview;
        } catch (e) {
            return 'Unable to preview programme data';
        }
    }

})();

