<?php
/**
 * Fix Settings Script
 * This script will reset the dashboard title and primary color to the correct values
 */

// Include WordPress
require_once('../../../wp-config.php');

echo "<h1>Church Programme Dashboard - Settings Fix</h1>";

// Check current values
echo "<h2>Current Values in Database:</h2>";
echo "<table border='1' cellpadding='10'>";
echo "<tr><th>Setting</th><th>Current Value</th></tr>";

$current_title = get_option('cpd_dashboard_title');
$current_primary_color = get_option('cpd_primary_color');

echo "<tr><td>cpd_dashboard_title</td><td>" . esc_html($current_title) . "</td></tr>";
echo "<tr><td>cpd_primary_color</td><td>" . esc_html($current_primary_color) . "</td></tr>";
echo "</table>";

// Fix the values
echo "<h2>Fixing Values:</h2>";

// Set the correct dashboard title
$new_title = 'BALANG KHIMUSNIANG';
$title_result = update_option('cpd_dashboard_title', $new_title);
echo "<p>Setting dashboard title to '$new_title': " . ($title_result ? 'SUCCESS' : 'FAILED') . "</p>";

// Set the correct primary color
$new_primary_color = '#4a5568';
$color_result = update_option('cpd_primary_color', $new_primary_color);
echo "<p>Setting primary color to '$new_primary_color': " . ($color_result ? 'SUCCESS' : 'FAILED') . "</p>";

// Verify the changes
echo "<h2>Updated Values:</h2>";
echo "<table border='1' cellpadding='10'>";
echo "<tr><th>Setting</th><th>New Value</th></tr>";

$updated_title = get_option('cpd_dashboard_title');
$updated_primary_color = get_option('cpd_primary_color');

echo "<tr><td>cpd_dashboard_title</td><td>" . esc_html($updated_title) . "</td></tr>";
echo "<tr><td>cpd_primary_color</td><td>" . esc_html($updated_primary_color) . "</td></tr>";
echo "</table>";

// Clean up any test settings that might exist
echo "<h2>Cleaning up test settings:</h2>";

// Get all options that start with 'cpd_test_'
global $wpdb;
$test_options = $wpdb->get_results(
    "SELECT option_name FROM {$wpdb->options} WHERE option_name LIKE 'cpd_test_%'"
);

if ($test_options) {
    foreach ($test_options as $option) {
        delete_option($option->option_name);
        echo "<p>Removed test setting: {$option->option_name}</p>";
    }
} else {
    echo "<p>No test settings found to clean up.</p>";
}

// Clear any WordPress caches
if (function_exists('wp_cache_flush')) {
    wp_cache_flush();
    echo "<p>WordPress cache cleared.</p>";
}

echo "<h2>✅ Fix Complete!</h2>";
echo "<p>Your dashboard should now show:</p>";
echo "<ul>";
echo "<li><strong>Title:</strong> BALANG KHIMUSNIANG</li>";
echo "<li><strong>Primary Color:</strong> #4a5568 (dark gray)</li>";
echo "</ul>";
echo "<p><strong>Please refresh your dashboard page to see the changes.</strong></p>";

// Additional diagnostic information
echo "<h2>🔍 Additional Diagnostics</h2>";

// Check if there are any other settings that might be causing issues
$all_cpd_options = $wpdb->get_results(
    "SELECT option_name, option_value FROM {$wpdb->options} WHERE option_name LIKE 'cpd_%' ORDER BY option_name"
);

echo "<h3>All CPD Settings in Database:</h3>";
echo "<table border='1' cellpadding='5' style='border-collapse: collapse;'>";
echo "<tr><th>Setting Name</th><th>Value</th></tr>";
foreach ($all_cpd_options as $option) {
    $display_value = strlen($option->option_value) > 100 ?
        substr($option->option_value, 0, 100) . '...' :
        $option->option_value;
    echo "<tr><td>" . esc_html($option->option_name) . "</td><td>" . esc_html($display_value) . "</td></tr>";
}
echo "</table>";

// Check WordPress cache status
echo "<h3>Cache Status:</h3>";
if (defined('WP_CACHE') && WP_CACHE) {
    echo "<p>⚠️ WordPress caching is enabled. You may need to clear your cache.</p>";
} else {
    echo "<p>✅ WordPress caching is not enabled.</p>";
}

// Check if any caching plugins are active
$active_plugins = get_option('active_plugins', array());
$caching_plugins = array('wp-super-cache', 'w3-total-cache', 'wp-rocket', 'litespeed-cache');
$found_caching = false;
foreach ($caching_plugins as $plugin) {
    foreach ($active_plugins as $active_plugin) {
        if (strpos($active_plugin, $plugin) !== false) {
            echo "<p>⚠️ Caching plugin detected: $active_plugin - You may need to clear the cache.</p>";
            $found_caching = true;
        }
    }
}
if (!$found_caching) {
    echo "<p>✅ No common caching plugins detected.</p>";
}

// Show instructions to remove test files
echo "<h2>⚠️ Test Files Status</h2>";
if (file_exists('test-admin-save.php.DISABLED') && file_exists('test-ajax-direct.php.DISABLED')) {
    echo "<p>✅ Test files have been disabled:</p>";
    echo "<ul>";
    echo "<li>test-admin-save.php → test-admin-save.php.DISABLED</li>";
    echo "<li>test-ajax-direct.php → test-ajax-direct.php.DISABLED</li>";
    echo "</ul>";
    echo "<p>These files can no longer accidentally overwrite your settings.</p>";
} else {
    echo "<p>⚠️ To prevent this issue from happening again, you should rename these test files:</p>";
    echo "<ul>";
    if (file_exists('test-admin-save.php')) {
        echo "<li>test-admin-save.php → test-admin-save.php.DISABLED</li>";
    }
    if (file_exists('test-ajax-direct.php')) {
        echo "<li>test-ajax-direct.php → test-ajax-direct.php.DISABLED</li>";
    }
    echo "</ul>";
    echo "<p>These files contain test code that can overwrite your settings if accidentally run.</p>";
}
?>
