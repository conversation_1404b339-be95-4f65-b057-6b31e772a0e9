<?php
/**
 * Secure Admin Panel - Simple Version with Authentication
 * 
 * This script provides a secure admin panel with user authentication.
 * 
 * Instructions:
 * 1. Upload this file to your WordPress root directory
 * 2. Access it via browser: https://yoursite.com/secure-admin-panel-simple.php
 * 3. First time: Use default admin credentials (admin/admin123)
 * 4. Manage users in the Users tab
 * 5. Delete this file after use for security
 */

// Security check
if (!defined('ABSPATH')) {
    require_once('wp-load.php');
}

// Initialize custom user management
function cpd_init_custom_users() {
    $users = get_option('cpd_custom_users', array());
    if (empty($users)) {
        $users = array(
            'admin' => array(
                'username' => 'admin',
                'password' => password_hash('admin123', PASSWORD_DEFAULT),
                'role' => 'administrator',
                'email' => '<EMAIL>',
                'created_at' => date('Y-m-d H:i:s')
            )
        );
        update_option('cpd_custom_users', $users);
    }
    return $users;
}

// Check if user is logged in
function cpd_is_logged_in() {
    return isset($_SESSION['cpd_logged_in']) && $_SESSION['cpd_logged_in'] === true;
}

// Authenticate user
function cpd_authenticate($username, $password) {
    $users = get_option('cpd_custom_users', array());
    if (isset($users[$username]) && password_verify($password, $users[$username]['password'])) {
        $_SESSION['cpd_logged_in'] = true;
        $_SESSION['cpd_username'] = $username;
        $_SESSION['cpd_role'] = $users[$username]['role'];
        return true;
    }
    return false;
}

// Logout user
function cpd_logout() {
    session_destroy();
    header('Location: ' . $_SERVER['PHP_SELF']);
    exit;
}

// Add new user
function cpd_add_user($username, $password, $role, $email) {
    $users = get_option('cpd_custom_users', array());
    if (isset($users[$username])) return false;
    $users[$username] = array(
        'username' => $username,
        'password' => password_hash($password, PASSWORD_DEFAULT),
        'role' => $role,
        'email' => $email,
        'created_at' => date('Y-m-d H:i:s')
    );
    update_option('cpd_custom_users', $users);
    return true;
}

// Delete user
function cpd_delete_user($username) {
    $users = get_option('cpd_custom_users', array());
    if (isset($users[$username]) && $username !== 'admin') {
        unset($users[$username]);
        update_option('cpd_custom_users', $users);
        return true;
    }
    return false;
}

// Change user password
function cpd_change_password($username, $current_password, $new_password) {
    $users = get_option('cpd_custom_users', array());
    if (isset($users[$username]) && password_verify($current_password, $users[$username]['password'])) {
        $users[$username]['password'] = password_hash($new_password, PASSWORD_DEFAULT);
        update_option('cpd_custom_users', $users);
        return true;
    }
    return false;
}

// Start session
session_start();
cpd_init_custom_users();

// Handle authentication
if (isset($_POST['login'])) {
    $username = sanitize_text_field($_POST['username']);
    $password = $_POST['password'];
    if (cpd_authenticate($username, $password)) {
        header('Location: ' . $_SERVER['PHP_SELF']);
        exit;
    } else {
        $login_error = "Invalid username or password";
    }
}

// Handle logout
if (isset($_GET['logout'])) {
    cpd_logout();
}

// Check if user needs to login
if (!cpd_is_logged_in()) {
    ?>
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Login - Church Programme Dashboard</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 0; padding: 0; background: #f0f0f1; display: flex; justify-content: center; align-items: center; min-height: 100vh; }
            .login-container { background: white; padding: 40px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); width: 100%; max-width: 400px; }
            .login-header { text-align: center; margin-bottom: 30px; }
            .login-header h1 { color: #1e73be; margin: 0 0 10px 0; }
            .form-group { margin-bottom: 20px; }
            label { display: block; margin-bottom: 8px; font-weight: 600; }
            input[type="text"], input[type="password"] { width: 100%; padding: 12px; border: 1px solid #8c8f94; border-radius: 4px; font-size: 16px; box-sizing: border-box; }
            button { width: 100%; background: #2271b1; color: white; border: none; padding: 12px; border-radius: 4px; cursor: pointer; font-size: 16px; }
            button:hover { background: #135e96; }
            .error { background: #f8d7da; color: #721c24; padding: 10px; border-radius: 4px; margin-bottom: 20px; border: 1px solid #f5c6cb; }
            .default-credentials { background: #d1ecf1; color: #0c5460; padding: 15px; border-radius: 4px; margin-top: 20px; border: 1px solid #bee5eb; }
        </style>
    </head>
    <body>
        <div class="login-container">
            <div class="login-header">
                <h1>Church Programme Dashboard</h1>
                <p>Secure Admin Panel Login</p>
            </div>
            <?php if (isset($login_error)): ?>
                <div class="error"><?php echo esc_html($login_error); ?></div>
            <?php endif; ?>
            <form method="post">
                <div class="form-group">
                    <label for="username">Username:</label>
                    <input type="text" id="username" name="username" required>
                </div>
                <div class="form-group">
                    <label for="password">Password:</label>
                    <input type="password" id="password" name="password" required>
                </div>
                <button type="submit" name="login">Login</button>
            </form>
        </div>
    </body>
    </html>
    <?php
    exit;
}

// User is logged in, show admin panel
$message = '';
$active_tab = isset($_GET['tab']) ? sanitize_text_field($_GET['tab']) : 'dashboard';

// Handle user management
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $tab = isset($_POST['tab']) ? sanitize_text_field($_POST['tab']) : 'dashboard';
    
    if ($tab === 'users') {
        if (isset($_POST['add_user'])) {
            $username = sanitize_text_field($_POST['new_username']);
            $password = $_POST['new_password'];
            $role = sanitize_text_field($_POST['new_role']);
            $email = sanitize_email($_POST['new_email']);
            
            if (cpd_add_user($username, $password, $role, $email)) {
                $message = "✅ User added successfully!";
            } else {
                $message = "❌ User already exists!";
            }
        } elseif (isset($_POST['delete_user'])) {
            $username = sanitize_text_field($_POST['delete_username']);
            if (cpd_delete_user($username)) {
                $message = "✅ User deleted successfully!";
            } else {
                $message = "❌ Cannot delete this user!";
            }
        } elseif (isset($_POST['change_password'])) {
            $current_password = $_POST['current_password'];
            $new_password = $_POST['new_password'];
            $confirm_password = $_POST['confirm_password'];
            $username = $_SESSION['cpd_username'];
            
            if ($new_password !== $confirm_password) {
                $message = "❌ New passwords do not match!";
            } elseif (cpd_change_password($username, $current_password, $new_password)) {
                $message = "✅ Password changed successfully!";
            } else {
                $message = "❌ Current password is incorrect!";
            }
        }
    }
}

$custom_users = get_option('cpd_custom_users', array());
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Secure Admin Panel - Church Programme Dashboard</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f0f0f1; }
        .container { max-width: 1200px; margin: 0 auto; background: white; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); overflow: hidden; }
        .header { background: #1e73be; color: white; padding: 30px; text-align: center; position: relative; }
        .header h1 { margin: 0; font-size: 28px; }
        .user-info { position: absolute; top: 20px; right: 20px; background: rgba(255,255,255,0.2); padding: 8px 15px; border-radius: 20px; font-size: 14px; }
        .user-info a { color: white; text-decoration: none; margin-left: 10px; }
        .tabs { background: #f6f7f7; border-bottom: 1px solid #c3c4c7; display: flex; flex-wrap: wrap; }
        .tab { padding: 15px 25px; background: transparent; border: none; border-bottom: 3px solid transparent; cursor: pointer; font-weight: 600; color: #2c3338; }
        .tab.active { background: white; border-bottom-color: #1e73be; color: #1e73be; }
        .tab-content { display: none; padding: 30px; }
        .tab-content.active { display: block; }
        .form-group { margin-bottom: 20px; }
        label { display: block; margin-bottom: 8px; font-weight: 600; }
        input[type="text"], input[type="password"], input[type="email"], select { width: 100%; padding: 8px 12px; border: 1px solid #8c8f94; border-radius: 4px; }
        button { background: #2271b1; color: white; border: none; padding: 12px 24px; border-radius: 4px; cursor: pointer; margin-right: 10px; }
        button:hover { background: #135e96; }
        .message { padding: 15px; border-radius: 4px; margin-bottom: 20px; }
        .success { background: #d1e7dd; border: 1px solid #badbcc; color: #0f5132; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .users-list { background: #f9f9f9; border: 1px solid #e2e8f0; border-radius: 4px; padding: 15px; margin-top: 10px; }
        .user-item { display: flex; justify-content: space-between; align-items: center; padding: 10px; border-bottom: 1px solid #e2e8f0; }
        .delete-btn { background: #dc3545; color: white; border: none; padding: 5px 10px; border-radius: 4px; cursor: pointer; font-size: 12px; }
        .add-user-form { background: #f8f9fa; padding: 20px; border-radius: 4px; margin-top: 20px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Church Programme Dashboard - Secure Admin Panel</h1>
            <p>Manage users and access settings</p>
            <div class="user-info">
                Logged in as: <strong><?php echo esc_html($_SESSION['cpd_username']); ?></strong>
                <a href="?logout=true">Logout</a>
            </div>
        </div>

        <?php if ($message): ?>
            <div class="message <?php echo strpos($message, '✅') !== false ? 'success' : 'error'; ?>">
                <?php echo esc_html($message); ?>
            </div>
        <?php endif; ?>

        <div class="tabs">
            <button class="tab <?php echo $active_tab === 'dashboard' ? 'active' : ''; ?>" onclick="switchTab('dashboard')">Dashboard</button>
            <button class="tab <?php echo $active_tab === 'users' ? 'active' : ''; ?>" onclick="switchTab('users')">Users</button>
            <button class="tab <?php echo $active_tab === 'settings' ? 'active' : ''; ?>" onclick="switchTab('settings')">Settings</button>
        </div>

        <!-- Dashboard Tab -->
        <div class="tab-content <?php echo $active_tab === 'dashboard' ? 'active' : ''; ?>" id="dashboard-tab">
            <h2>Welcome to Secure Admin Panel</h2>
            <p>This is a secure admin panel for managing your Church Programme Dashboard.</p>
            <div style="margin-top: 30px;">
                <h3>Quick Links:</h3>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-top: 20px;">
                    <div style="background: #f8f9fa; padding: 20px; border-radius: 4px;">
                        <h4>Complete Admin Panel</h4>
                        <p>Access the full admin panel with all settings</p>
                        <a href="complete-admin-panel.php" style="text-decoration: none;">
                            <button>Open Complete Panel</button>
                        </a>
                    </div>
                    <div style="background: #f8f9fa; padding: 20px; border-radius: 4px;">
                        <h4>View Dashboard</h4>
                        <p>See your church programme dashboard</p>
                        <a href="<?php echo CPD_Subdomain::get_dashboard_url(); ?>" target="_blank" style="text-decoration: none;">
                            <button>View Dashboard</button>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Users Tab -->
        <div class="tab-content <?php echo $active_tab === 'users' ? 'active' : ''; ?>" id="users-tab">
            <h2>User Management</h2>
            <p>Manage users who can access this admin panel.</p>
            
            <div class="users-list">
                <h3>Current Users:</h3>
                <?php foreach($custom_users as $username => $user): ?>
                    <div class="user-item">
                        <div>
                            <strong><?php echo esc_html($user['username']); ?></strong>
                            <br>
                            <small>Role: <?php echo esc_html($user['role']); ?></small>
                            <br>
                            <small>Email: <?php echo esc_html($user['email']); ?></small>
                        </div>
                        <?php if ($username !== 'admin'): ?>
                            <form method="post" style="display: inline;">
                                <input type="hidden" name="tab" value="users">
                                <input type="hidden" name="delete_username" value="<?php echo esc_attr($username); ?>">
                                <button type="submit" name="delete_user" class="delete-btn">Delete</button>
                            </form>
                        <?php endif; ?>
                    </div>
                <?php endforeach; ?>
            </div>

            <div class="add-user-form">
                <h3>Change Your Password</h3>
                <form method="post">
                    <input type="hidden" name="tab" value="users">
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                        <div class="form-group">
                            <label for="current_password">Current Password:</label>
                            <input type="password" id="current_password" name="current_password" required>
                        </div>
                        <div class="form-group">
                            <label for="new_password">New Password:</label>
                            <input type="password" id="new_password" name="new_password" required>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="confirm_password">Confirm New Password:</label>
                        <input type="password" id="confirm_password" name="confirm_password" required>
                    </div>
                    <button type="submit" name="change_password">Change Password</button>
                </form>
            </div>

            <div class="add-user-form" style="margin-top: 30px;">
                <h3>Add New User</h3>
                <form method="post">
                    <input type="hidden" name="tab" value="users">
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                        <div class="form-group">
                            <label for="new_username">Username:</label>
                            <input type="text" id="new_username" name="new_username" required>
                        </div>
                        <div class="form-group">
                            <label for="new_password">Password:</label>
                            <input type="password" id="new_password" name="new_password" required>
                        </div>
                    </div>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                        <div class="form-group">
                            <label for="new_role">Role:</label>
                            <select id="new_role" name="new_role" required>
                                <option value="administrator">Administrator</option>
                                <option value="editor">Editor</option>
                                <option value="user">User</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="new_email">Email:</label>
                            <input type="email" id="new_email" name="new_email" required>
                        </div>
                    </div>
                    <button type="submit" name="add_user">Add User</button>
                </form>
            </div>
        </div>

        <!-- Settings Tab -->
        <div class="tab-content <?php echo $active_tab === 'settings' ? 'active' : ''; ?>" id="settings-tab">
            <h2>Security Settings</h2>
            <p>This secure admin panel provides:</p>
            <ul>
                <li><strong>User Authentication:</strong> Secure login system with password hashing</li>
                <li><strong>User Management:</strong> Add and delete users with different roles</li>
                <li><strong>Session Management:</strong> Secure session handling</li>
                <li><strong>Role-based Access:</strong> Different user roles with permissions</li>
            </ul>
            
            <div style="margin-top: 30px;">
                <h3>Security Recommendations:</h3>
                <div style="background: #f8f9fa; padding: 20px; border-radius: 4px;">
                    <ol>
                        <li><strong>Change Default Password:</strong> Change the default admin password after first login</li>
                        <li><strong>Use Strong Passwords:</strong> Create strong passwords for all users</li>
                        <li><strong>Limit User Access:</strong> Only give admin access to trusted users</li>
                        <li><strong>Delete File After Use:</strong> Delete this file from your server when not in use</li>
                        <li><strong>Regular Backups:</strong> Keep regular backups of your WordPress database</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <script>
    // Tab switching function
    function switchTab(tabName) {
        // Update URL without page reload
        const url = new URL(window.location);
        url.searchParams.set('tab', tabName);
        window.history.pushState({}, '', url);
        
        // Hide all tab contents
        document.querySelectorAll('.tab-content').forEach(tab => {
            tab.classList.remove('active');
        });
        
        // Show selected tab content
        document.getElementById(tabName + '-tab').classList.add('active');
        
        // Update active tab button
        document.querySelectorAll('.tab').forEach(tab => {
            tab.classList.remove('active');
        });
        event.target.classList.add('active');
    }

    // Auto-select active tab based on URL
    document.addEventListener('DOMContentLoaded', function() {
        const urlParams = new URLSearchParams(window.location.search);
        const tab = urlParams.get('tab') || 'dashboard';
        switchTab(tab);
    });
    </script>

    <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; text-align: center;">
        <p><strong>Complete Admin Panel:</strong> Use the "Complete Admin Panel" link in the Dashboard tab for full settings management.</p>
    </div>
</body>
</html>
