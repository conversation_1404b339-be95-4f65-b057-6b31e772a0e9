/**
 * Admin Styles
 */

.cpd-admin-wrap {
    max-width: 1200px;
}

.cpd-admin-notice {
    margin: 20px 0;
}

.cpd-admin-tabs {
    margin-top: 20px;
}

.cpd-tab-content {
    display: none;
    padding: 20px;
    background: #fff;
    border: 1px solid #ccd0d4;
    border-top: none;
}

.cpd-tab-content.cpd-tab-active {
    display: block;
}

.cpd-tab-content h2 {
    margin-top: 0;
}

.cpd-tab-content h3 {
    margin-top: 30px;
    margin-bottom: 10px;
    color: #1d2327;
    font-size: 16px;
}

.form-table th {
    width: 250px;
}

.cpd-color-picker {
    max-width: 100px;
}

.cpd-upload-image,
.cpd-remove-image {
    margin-left: 10px;
}

.cpd-add-user-section {
    background: #fff;
    padding: 20px;
    border: 1px solid #ccd0d4;
    margin: 20px 0;
}

.cpd-users-list-section {
    margin-top: 30px;
}

.cpd-status-badge {
    display: inline-block;
    padding: 3px 8px;
    border-radius: 3px;
    font-size: 12px;
    font-weight: 600;
}

.cpd-status-active {
    background: #d4edda;
    color: #155724;
}

.cpd-status-inactive {
    background: #f8d7da;
    color: #721c24;
}

.required {
    color: #d63638;
}

/* Responsive */
@media screen and (max-width: 782px) {
    .form-table th {
        width: auto;
    }
}

