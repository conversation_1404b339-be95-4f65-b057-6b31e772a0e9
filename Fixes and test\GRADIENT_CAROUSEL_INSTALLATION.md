# Church Programme Dashboard - Gradient Carousel Installation Guide

This guide explains how to install and use the Church Programme Dashboard with the new gradient carousel background customization features.

## 📋 Overview

The Church Programme Dashboard now includes advanced carousel gradient background customization with:
- 10 modern gradient presets
- Custom gradient creation with color pickers
- Live preview in admin panel
- Real-time updates on the dashboard

## 🚀 Installation Steps

### Method 1: Complete Plugin Installation

1. **Upload the Plugin**
   - Download the complete `church-programme-dashboard` folder
   - Upload it to your WordPress `/wp-content/plugins/` directory
   - Activate the plugin in WordPress Admin → Plugins

2. **Configure Settings**
   - Go to WordPress Admin → Church Programme Dashboard
   - Configure general settings, subdomains, colors, and programme labels

3. **Access Complete Admin Panel**
   - Navigate to: `https://yoursite.com/complete-admin-panel.php`
   - Use the secure login system to access all settings

### Method 2: Standalone PHP Files (Alternative)

If you only need the admin panel functionality:

1. **Upload Essential Files**
   ```
   /complete-admin-panel.php
   /secure-admin-panel-simple.php
   /admin/class-cpd-admin-settings.php
   /admin/views/settings-page.php
   /admin/js/admin-script.js
   /admin/css/admin-style.css
   /public/css/dashboard-style.css
   /public/js/dashboard-script.js
   /public/templates/dashboard.php
   ```

2. **Place in WordPress Root**
   - Upload `complete-admin-panel.php` and `secure-admin-panel-simple.php` to your WordPress root directory
   - Create the folder structure for other files

## 🔧 Configuration

### 1. Subdomain Setup (Optional but Recommended)

For best user experience, set up subdomains:

1. **Create Subdomains**
   - `dashboard.yoursite.com` - for public dashboard
   - `editor.yoursite.com` - for programme editing

2. **Configure in Admin Panel**
   - Go to "Subdomain" tab in Complete Admin Panel
   - Set dashboard and editor subdomain names
   - Save settings

### 2. Carousel Gradient Configuration

#### Access Gradient Settings:
1. Open Complete Admin Panel
2. Navigate to "Carousel" tab
3. Scroll to "Carousel Gradient Background" section

#### Using Preset Gradients:
1. Select "Use Preset Gradient"
2. Choose from 10 modern gradient options:
   - Blue to Purple (default)
   - Sunset Orange
   - Ocean Blue
   - Forest Green
   - Royal Purple
   - Sunrise Pink
   - Midnight Blue
   - Emerald Green
   - Fiery Red
   - Golden Yellow
3. View live preview
4. Save settings

#### Creating Custom Gradients:
1. Select "Use Custom Gradient"
2. Choose Start Color using color picker
3. Choose End Color using color picker
4. Set Gradient Angle (0-360 degrees):
   - 0° = Left to Right
   - 90° = Bottom to Top
   - 135° = Diagonal (default)
   - 180° = Right to Left
   - 270° = Top to Bottom
5. View live preview
6. Save settings

## 🎨 Gradient Preset Details

| Preset Name | Colors | Description |
|-------------|--------|-------------|
| Blue to Purple | #667eea → #764ba2 | Professional blue-purple blend |
| Sunset Orange | #f093fb → #f5576c | Warm sunset colors |
| Ocean Blue | #4facfe → #00f2fe | Fresh ocean blue tones |
| Forest Green | #43e97b → #38f9d7 | Natural green gradient |
| Royal Purple | #a18cd1 → #fbc2eb | Elegant purple-pink mix |
| Sunrise Pink | #fa709a → #fee140 | Bright sunrise colors |
| Midnight Blue | #4c6ef5 → #3b5bdb | Deep blue professional look |
| Emerald Green | #0ba360 → #3cba92 | Rich green gradient |
| Fiery Red | #ff6b6b → #ee5a24 | Warm red-orange blend |
| Golden Yellow | #f9d423 → #ff4e50 | Vibrant yellow-red mix |

## 🔒 Security Notes

### Important Security Practices:

1. **Delete Admin Files After Use**
   - Remove `complete-admin-panel.php` and `secure-admin-panel-simple.php` from production after configuration
   - These are temporary admin tools for initial setup

2. **Use WordPress Admin for Regular Maintenance**
   - Once configured, use the WordPress admin interface for daily operations
   - The standalone PHP files are for emergency/initial setup only

3. **Backup Settings**
   - Export your settings before making major changes
   - Keep a record of custom gradient colors if using them

## 🛠️ Troubleshooting

### Common Issues:

1. **Gradient Not Updating**
   - Clear browser cache
   - Check if CSS variables are loading in browser dev tools
   - Verify JavaScript console for errors

2. **Color Picker Not Working**
   - Ensure browser supports HTML5 color input
   - Check if JavaScript is enabled
   - Try different browser

3. **Admin Panel Access Issues**
   - Verify file permissions (644 for PHP files)
   - Check WordPress user permissions
   - Ensure secure-admin-panel-simple.php is accessible

### File Permissions:
```
PHP Files: 644
CSS/JS Files: 644
Directories: 755
```

## 📱 Mobile Compatibility

The gradient carousel is fully responsive:
- Works on all screen sizes
- Touch-friendly color pickers on mobile
- Optimized gradient display on mobile devices

## 🔄 Updates and Maintenance

### Regular Maintenance:
1. **Check Gradient Settings** periodically
2. **Update WordPress** and plugin regularly
3. **Test on Different Devices** to ensure consistency
4. **Backup Custom Settings** before updates

### Future Updates:
- More gradient presets can be added by updating the JavaScript arrays
- Custom gradient functionality can be extended with additional color stops
- Animation effects can be combined with gradient backgrounds

## 📞 Support

For technical support or feature requests:
- Check the plugin documentation
- Contact your developer
- Review browser console for JavaScript errors

---

**Note**: Always test gradient settings on multiple devices and browsers to ensure consistent appearance across all user platforms.
