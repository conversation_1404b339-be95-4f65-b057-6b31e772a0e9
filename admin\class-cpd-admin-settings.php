<?php
/**
 * Admin Settings Class - Rebuilt from Scratch
 */

if (!defined('ABSPATH')) {
    exit;
}

class CPD_Admin_Settings {
    
    /**
     * Initialize
     */
    public static function init() {
        add_action('admin_init', array(__CLASS__, 'register_settings'));
        error_log('CPD_Admin_Settings::init() called');
    }
    
    /**
     * Register settings
     */
    public static function register_settings() {
        // General
        register_setting('cpd_settings', 'cpd_dashboard_title');
        register_setting('cpd_settings', 'cpd_header_bg_color');
        register_setting('cpd_settings', 'cpd_header_bg_image');
        
        // Subdomains
        register_setting('cpd_settings', 'cpd_dashboard_subdomain');
        register_setting('cpd_settings', 'cpd_editor_subdomain');
        
        // Colors
        register_setting('cpd_settings', 'cpd_primary_color');
        register_setting('cpd_settings', 'cpd_secondary_color');
        register_setting('cpd_settings', 'cpd_accent_color');
        register_setting('cpd_settings', 'cpd_text_color');
        
        // Programme labels
        register_setting('cpd_settings', 'cpd_label_miet_balang');
        register_setting('cpd_settings', 'cpd_label_miet_balang_time');
        register_setting('cpd_settings', 'cpd_label_jingiaseng_samla');
        register_setting('cpd_settings', 'cpd_label_jingiaseng_samla_time');
        register_setting('cpd_settings', 'cpd_label_jingiaseng_1pm');
        register_setting('cpd_settings', 'cpd_label_jingiaseng_1pm_time');
        register_setting('cpd_settings', 'cpd_label_jingiaseng_khynnah');
        register_setting('cpd_settings', 'cpd_label_jingiaseng_khynnah_time');
        register_setting('cpd_settings', 'cpd_label_jingiaseng_iing');
        register_setting('cpd_settings', 'cpd_label_jingiaseng_iing_time');
        
        // Notice board
        register_setting('cpd_settings', 'cpd_notice_board_enabled');
        register_setting('cpd_settings', 'cpd_notice_board_title');
        register_setting('cpd_settings', 'cpd_notice_board_content');
        
        // Cookie consent
        register_setting('cpd_settings', 'cpd_cookie_consent_text');
        register_setting('cpd_settings', 'cpd_cookie_consent_button');
        
        // Carousel
        register_setting('cpd_settings', 'cpd_carousel_animation_type');
        register_setting('cpd_settings', 'cpd_carousel_animation_speed');
        register_setting('cpd_settings', 'cpd_carousel_auto_interval');
        register_setting('cpd_settings', 'cpd_carousel_direction');
        
        error_log('CPD_Admin_Settings: Settings registered');
    }
    
    /**
     * Get all settings
     */
    public static function get_all_settings() {
        return array(
            'general' => array(
                'dashboard_title' => get_option('cpd_dashboard_title', 'Church Programme'),
                'subdomain_name' => get_option('cpd_subdomain_name', 'programme'),
                'header_bg_color' => get_option('cpd_header_bg_color', '#ffffff'),
                'header_bg_image' => get_option('cpd_header_bg_image', ''),
            ),
            'colors' => array(
                'primary_color' => get_option('cpd_primary_color', '#4a5568'),
                'secondary_color' => get_option('cpd_secondary_color', '#718096'),
                'accent_color' => get_option('cpd_accent_color', '#3182ce'),
                'text_color' => get_option('cpd_text_color', '#2d3748'),
            ),
            'notice' => array(
                'enabled' => get_option('cpd_notice_board_enabled', '0'),
                'title' => get_option('cpd_notice_board_title', 'Notice Board'),
                'content' => get_option('cpd_notice_board_content', ''),
            ),
            'labels' => array(
                'miet_balang' => get_option('cpd_label_miet_balang', 'MIET BALANG'),
                'miet_balang_time' => get_option('cpd_label_miet_balang_time', '6:30 PM'),
                'jingiaseng_samla' => get_option('cpd_label_jingiaseng_samla', 'JINGIASENG SAMLA'),
                'jingiaseng_samla_time' => get_option('cpd_label_jingiaseng_samla_time', '6:30 PM'),
                'jingiaseng_1pm' => get_option('cpd_label_jingiaseng_1pm', 'JINGIASENG 1:00 Baje'),
                'jingiaseng_1pm_time' => get_option('cpd_label_jingiaseng_1pm_time', '1:00 PM'),
                'jingiaseng_khynnah' => get_option('cpd_label_jingiaseng_khynnah', 'JINGIASENG KHYNNAH'),
                'jingiaseng_khynnah_time' => get_option('cpd_label_jingiaseng_khynnah_time', '3:00 PM'),
                'jingiaseng_iing' => get_option('cpd_label_jingiaseng_iing', 'JINGIASENG IING'),
                'jingiaseng_iing_time' => get_option('cpd_label_jingiaseng_iing_time', '6:30 PM'),
            ),
            'cookie' => array(
                'consent_text' => get_option('cpd_cookie_consent_text', 'We use cookies to enhance your experience.'),
                'consent_button' => get_option('cpd_cookie_consent_button', 'Accept'),
            ),
            'carousel' => array(
                'animation_type' => get_option('cpd_carousel_animation_type', 'slide'),
                'animation_speed' => get_option('cpd_carousel_animation_speed', '500'),
                'auto_interval' => get_option('cpd_carousel_auto_interval', '10000'),
                'direction' => get_option('cpd_carousel_direction', 'right-to-left'),
            ),
        );
    }
}

