<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Calendar Navigation Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        .calendar-container {
            max-width: 400px;
            margin: 0 auto;
        }
        .calendar-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        .calendar-nav-btn {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
        }
        .calendar-nav-btn:hover {
            background: #005a87;
        }
        .calendar-title {
            margin: 0;
            font-size: 1.5em;
        }
        .calendar-grid {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 5px;
        }
        .calendar-day-header {
            text-align: center;
            font-weight: bold;
            padding: 10px;
            background: #f0f0f0;
        }
        .calendar-day {
            text-align: center;
            padding: 10px;
            border: 1px solid #ddd;
            min-height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .calendar-day.current-month {
            background: white;
        }
        .calendar-day.other-month {
            background: #f9f9f9;
            color: #999;
        }
        .calendar-day.today {
            background: #e6f3ff;
            border-color: #007cba;
        }
        .test-info {
            margin-top: 30px;
            padding: 15px;
            background: #f5f5f5;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <h1>Calendar Navigation Test</h1>
    
    <div class="calendar-container">
        <div class="calendar-header">
            <button class="calendar-nav-btn" id="calendar-prev">← Previous</button>
            <h2 class="calendar-title" id="calendar-title">Loading...</h2>
            <button class="calendar-nav-btn" id="calendar-next">Next →</button>
        </div>
        
        <div class="calendar-grid" id="calendar-grid">
            <!-- Calendar will be dynamically loaded -->
        </div>
    </div>

    <div class="test-info">
        <h3>Test Instructions:</h3>
        <p>1. Click "Next" and "Previous" buttons to navigate through months</p>
        <p>2. Verify that months transition correctly (no skipping)</p>
        <p>3. Verify that year transitions work correctly at January and December</p>
        <p>4. Check that the calendar grid displays the correct days for each month</p>
    </div>

    <script>
        // State
        let currentMonth = new Date().getMonth();
        let currentYear = new Date().getFullYear();

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            renderCalendar();
            initCalendarNavigation();
        });

        // Render Calendar
        function renderCalendar() {
            const title = document.getElementById('calendar-title');
            const grid = document.getElementById('calendar-grid');

            const monthNames = ['January', 'February', 'March', 'April', 'May', 'June',
                               'July', 'August', 'September', 'October', 'November', 'December'];

            title.textContent = `${monthNames[currentMonth]} ${currentYear}`;

            // Get first day of month and number of days
            const firstDay = new Date(currentYear, currentMonth, 1).getDay();
            const daysInMonth = new Date(currentYear, currentMonth + 1, 0).getDate();
            const daysInPrevMonth = new Date(currentYear, currentMonth, 0).getDate();

            let html = '';

            // Day headers
            const dayHeaders = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
            dayHeaders.forEach(day => {
                html += `<div class="calendar-day-header">${day}</div>`;
            });

            // Previous month days
            for (let i = firstDay - 1; i >= 0; i--) {
                const day = daysInPrevMonth - i;
                html += `<div class="calendar-day other-month">${day}</div>`;
            }

            // Current month days
            const today = new Date();
            const todayStr = today.toISOString().split('T')[0];

            for (let day = 1; day <= daysInMonth; day++) {
                const dateStr = `${currentYear}-${String(currentMonth + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
                const isToday = dateStr === todayStr;

                let classes = 'calendar-day current-month';
                if (isToday) classes += ' today';

                html += `<div class="${classes}">${day}</div>`;
            }

            // Next month days
            const totalCells = Math.ceil((firstDay + daysInMonth) / 7) * 7;
            const remainingCells = totalCells - (firstDay + daysInMonth);

            for (let day = 1; day <= remainingCells; day++) {
                html += `<div class="calendar-day other-month">${day}</div>`;
            }

            grid.innerHTML = html;
        }

        // Initialize calendar navigation (FIXED VERSION)
        function initCalendarNavigation() {
            const prevBtn = document.getElementById('calendar-prev');
            const nextBtn = document.getElementById('calendar-next');

            if (prevBtn) {
                prevBtn.addEventListener('click', () => {
                    // Create a new date object to handle month transitions correctly
                    const newDate = new Date(currentYear, currentMonth - 1, 1);
                    currentMonth = newDate.getMonth();
                    currentYear = newDate.getFullYear();
                    renderCalendar();
                });
            }

            if (nextBtn) {
                nextBtn.addEventListener('click', () => {
                    // Create a new date object to handle month transitions correctly
                    const newDate = new Date(currentYear, currentMonth + 1, 1);
                    currentMonth = newDate.getMonth();
                    currentYear = newDate.getFullYear();
                    renderCalendar();
                });
            }
        }
    </script>
</body>
</html>
