# Testing Guide - Church Programme Dashboard

Complete testing checklist to ensure all features work correctly.

## Pre-Testing Setup

### 1. Fresh WordPress Installation
- WordPress 5.8+ or higher
- PHP 7.4+ or higher
- MySQL 5.6+ or higher
- Clean database (or test on staging site)

### 2. Plugin Installation
1. Upload `church-programme-dashboard` folder to `wp-content/plugins/`
2. Activate plugin via WordPress admin
3. Verify no PHP errors in debug.log

### 3. Initial Configuration
1. Go to **Church Programme** > **Settings**
2. Set dashboard title: "Test Church Programme"
3. Set custom path: "programme"
4. Save settings

---

## Test Suite 1: Installation & Activation

### Test 1.1: Plugin Activation
- [ ] Plugin activates without errors
- [ ] No PHP warnings or notices
- [ ] Admin menu "Church Programme" appears

### Test 1.2: Database Tables
Run this SQL to verify tables were created:
```sql
SHOW TABLES LIKE 'wp_cpd_%';
```
Expected: 4 tables
- [ ] wp_cpd_programmes
- [ ] wp_cpd_editor_users
- [ ] wp_cpd_ai_settings
- [ ] wp_cpd_extraction_history

### Test 1.3: Default Options
Check WordPress options table:
- [ ] cpd_dashboard_title exists
- [ ] cpd_subdomain_name exists
- [ ] cpd_primary_color exists

### Test 1.4: Rewrite Rules
- [ ] Visit `yoursite.com/programme/` - should load (even if empty)
- [ ] Visit `yoursite.com/programme/editor/` - should show login screen
- [ ] No 404 errors

**If 404 errors occur:**
1. Go to Settings > Permalinks
2. Click "Save Changes"
3. Try again

---

## Test Suite 2: Admin Panel

### Test 2.1: Settings Page
- [ ] Navigate to Church Programme > Settings
- [ ] All tabs visible: General, Colors & Styling, Programme Labels, Notice Board, Cookie Consent, Quick Links
- [ ] Tab switching works smoothly

### Test 2.2: General Settings
- [ ] Change dashboard title - saves correctly
- [ ] Change custom path - saves correctly
- [ ] Upload header background image - saves correctly
- [ ] Change header background color - saves correctly
- [ ] Click "Save All Settings" - success message appears

### Test 2.3: Colors & Styling
- [ ] Click color picker for Primary Color - picker opens
- [ ] Select a color - preview updates
- [ ] Save settings - color persists on dashboard
- [ ] Test all 4 color pickers (Primary, Secondary, Accent, Text)

### Test 2.4: Programme Labels
- [ ] Change label for "MIET BALANG" - saves correctly
- [ ] Change default time - saves correctly
- [ ] Labels appear on dashboard with new names

### Test 2.5: Notice Board
- [ ] Enable notice board checkbox
- [ ] Enter title: "Important Announcement"
- [ ] Enter content with formatting (bold, italic)
- [ ] Save settings
- [ ] Visit dashboard - notice board appears

### Test 2.6: Cookie Consent
- [ ] Change consent message
- [ ] Change button text
- [ ] Save settings
- [ ] Visit dashboard - new message appears

### Test 2.7: User Management
Navigate to Church Programme > Editor Users

- [ ] Click "Generate Strong Password" - password appears
- [ ] Enter username: "testuser"
- [ ] Enter generated password
- [ ] Enter email: "<EMAIL>"
- [ ] Click "Add User" - success message
- [ ] User appears in table below
- [ ] Click "Delete" on user - confirmation dialog
- [ ] Confirm deletion - user removed

---

## Test Suite 3: Dashboard (Public View)

### Test 3.1: Basic Display
Visit `yoursite.com/programme/`

- [ ] Page loads without errors
- [ ] Header displays with title
- [ ] Header background color/image shows
- [ ] Footer displays with attribution
- [ ] "Powered by Jermesa Studio" link works
- [ ] Privacy policy link works
- [ ] Font attribution visible

### Test 3.2: Carousel (Empty State)
- [ ] Carousel section visible
- [ ] Shows "No upcoming programmes" or similar message
- [ ] No JavaScript errors in console (F12)

### Test 3.3: Calendar
- [ ] Calendar displays current month
- [ ] Days of week headers visible (Sun-Sat)
- [ ] Current day highlighted
- [ ] Previous/Next month buttons visible
- [ ] Click next month - calendar updates
- [ ] Click previous month - calendar updates

### Test 3.4: Notice Board
If enabled in settings:
- [ ] Notice board section visible
- [ ] Title displays correctly
- [ ] Content displays with formatting
- [ ] Section appears between carousel and calendar

If disabled:
- [ ] Notice board section not visible

### Test 3.5: Cookie Consent
On first visit:
- [ ] Cookie consent banner appears at bottom
- [ ] Message displays correctly
- [ ] Button text correct
- [ ] Click accept button - banner disappears
- [ ] Refresh page - banner does not reappear
- [ ] Check localStorage - cpd_cookie_consent = "accepted"

### Test 3.6: Responsive Design
Test on different screen sizes:

**Desktop (1920x1080):**
- [ ] Layout looks good
- [ ] Carousel full width
- [ ] Calendar grid displays properly

**Tablet (768x1024):**
- [ ] Layout adapts
- [ ] Navigation still accessible
- [ ] Text readable

**Mobile (375x667):**
- [ ] Mobile-optimized layout
- [ ] Carousel slides work
- [ ] Calendar scrollable if needed
- [ ] Buttons large enough to tap

---

## Test Suite 4: Editor Page

### Test 4.1: Login Screen
Visit `yoursite.com/programme/editor/`

- [ ] Login screen displays
- [ ] Title visible
- [ ] Username field present
- [ ] Password field present
- [ ] Login button present

### Test 4.2: Authentication
- [ ] Enter wrong username - error message
- [ ] Enter wrong password - error message
- [ ] Enter correct credentials - redirects to editor
- [ ] User info displays in header
- [ ] Logout button visible

### Test 4.3: Logout
- [ ] Click logout button
- [ ] Redirects to login screen
- [ ] Cannot access editor without re-login

### Test 4.4: Tab Navigation
After logging in:
- [ ] Three tabs visible: AI Extraction, Manual Entry, Manage Programmes
- [ ] Click each tab - content switches
- [ ] Active tab highlighted
- [ ] No JavaScript errors

---

## Test Suite 5: AI Extraction

### Test 5.1: AI Configuration
In AI Extraction tab:

- [ ] Provider dropdown visible
- [ ] Select "OpenRouter"
- [ ] API key field visible
- [ ] Enter API key
- [ ] Click "Fetch Available Models"
- [ ] Loading indicator appears
- [ ] Models populate in dropdown
- [ ] Select a model
- [ ] Configuration saved to localStorage

**Test with each provider:**
- [ ] OpenRouter
- [ ] Google Gemini
- [ ] DeepSeek

### Test 5.2: Image Upload
For each programme type:

**JINGIASENG 1:00 Baje:**
- [ ] Click "Choose Image" or drag & drop
- [ ] Select image file
- [ ] Preview appears below
- [ ] Image displays correctly

**MIET BALANG:**
- [ ] Upload image
- [ ] Preview displays

**JINGIASENG SAMLA:**
- [ ] Upload image
- [ ] Preview displays

**JINGIASENG KHYNNAH:**
- [ ] Upload image
- [ ] Preview displays

**JINGIASENG IING:**
- [ ] Upload image
- [ ] Preview displays

### Test 5.3: Data Extraction
For each programme type with uploaded image:

- [ ] Click "Extract Data" button
- [ ] Button shows loading state
- [ ] Wait 10-30 seconds
- [ ] Success message appears
- [ ] Result box shows extracted data
- [ ] No errors in console

**If extraction fails:**
- Check API key is valid
- Check model supports vision
- Check image is clear and readable
- Check browser console for errors

### Test 5.4: Extracted Data Verification
After successful extraction:

1. Switch to "Manage Programmes" tab
2. [ ] Extracted programme appears in list
3. [ ] Date is correct
4. [ ] Time is correct
5. [ ] Participant names extracted correctly

---

## Test Suite 6: Manual Entry

### Test 6.1: Form Display
In Manual Entry tab:

- [ ] Programme type dropdown visible
- [ ] Date field visible
- [ ] Time field visible
- [ ] Fields container empty initially

### Test 6.2: Dynamic Fields - JINGIASENG 1:00 Baje
- [ ] Select "JINGIASENG 1:00 Baje"
- [ ] Field appears: NONGIATHUH KHANA POR 1:00PM
- [ ] Enter test data: "John Doe & Jane Smith"
- [ ] Enter date: 2025-10-05
- [ ] Enter time: 13:00
- [ ] Click "Save Programme"
- [ ] Success message appears
- [ ] Form resets

### Test 6.3: Dynamic Fields - MIET BALANG
- [ ] Select "MIET BALANG"
- [ ] Three fields appear:
  - PULE SDANG & DUWAI
  - NONGKREN
  - KHUBOR
- [ ] Fill all fields
- [ ] Enter date and time
- [ ] Save successfully

### Test 6.4: Dynamic Fields - JINGIASENG SAMLA
- [ ] Select "JINGIASENG SAMLA"
- [ ] Regular fields appear
- [ ] Check "This is a special item"
- [ ] Regular fields hide
- [ ] Special item field appears
- [ ] Uncheck - regular fields return
- [ ] Test saving both regular and special item

### Test 6.5: Dynamic Fields - JINGIASENG KHYNNAH
- [ ] Select "JINGIASENG KHYNNAH"
- [ ] Eight fields appear
- [ ] Fill all fields
- [ ] Save successfully

### Test 6.6: Dynamic Fields - JINGIASENG IING
- [ ] Select "JINGIASENG IING"
- [ ] Zone-1 section appears (3 fields)
- [ ] Zone-2 section appears (3 fields)
- [ ] Fill all fields
- [ ] Save successfully

### Test 6.7: Validation
- [ ] Try to save without selecting type - error message
- [ ] Try to save without date - error message
- [ ] Try to save without time - error message

---

## Test Suite 7: Manage Programmes

### Test 7.1: Programme List Display
In Manage Programmes tab:

- [ ] Programmes list loads automatically
- [ ] Each programme shows:
  - Type label
  - Date
  - Time
  - Preview of data
  - Edit button
  - Delete button

### Test 7.2: Filters
- [ ] Type filter dropdown works
- [ ] Start date filter works
- [ ] End date filter works
- [ ] Click "Apply Filter" - list updates
- [ ] Click "Clear Filter" - shows all programmes

### Test 7.3: Edit Programme
- [ ] Click "Edit" on a programme
- [ ] Switches to Manual Entry tab
- [ ] Form populates with programme data
- [ ] All fields filled correctly
- [ ] Button text changes to "Update Programme"
- [ ] Modify data
- [ ] Click "Update Programme"
- [ ] Success message
- [ ] Changes reflected in list

### Test 7.4: Delete Programme
- [ ] Click "Delete" on a programme
- [ ] Confirmation dialog appears
- [ ] Click "Cancel" - nothing happens
- [ ] Click "Delete" again
- [ ] Confirm - programme deleted
- [ ] Success message
- [ ] Programme removed from list

---

## Test Suite 8: Dashboard with Data

After adding programmes, test dashboard display:

### Test 8.1: Carousel with Data
- [ ] Add 3+ future programmes
- [ ] Visit dashboard
- [ ] Carousel shows 3 slides
- [ ] Each slide shows correct programme info
- [ ] Auto-advances every 5 seconds
- [ ] Click left arrow - previous slide
- [ ] Click right arrow - next slide
- [ ] Click indicator dots - jumps to slide

### Test 8.2: Calendar with Data
- [ ] Add programmes for current month
- [ ] Visit dashboard
- [ ] Programme dates marked with circles
- [ ] Click marked date - modal opens
- [ ] Modal shows programme details
- [ ] Details formatted correctly
- [ ] Click close button - modal closes
- [ ] Click outside modal - modal closes
- [ ] Press Escape key - modal closes

### Test 8.3: Multiple Programmes Same Date
- [ ] Add 2+ programmes for same date
- [ ] Click date on calendar
- [ ] Modal shows all programmes for that date
- [ ] Each programme clearly separated

---

## Test Suite 9: Security

### Test 9.1: API Key Security
- [ ] Enter API key in editor
- [ ] Check browser localStorage - key present
- [ ] Check WordPress database - key NOT present
- [ ] Check network requests - key sent in X-API-Key header only

### Test 9.2: Authentication
- [ ] Try to access editor without login - redirects to login
- [ ] Try to access REST API without auth - returns error
- [ ] Session expires after logout

### Test 9.3: Input Sanitization
- [ ] Try to enter `<script>alert('xss')</script>` in programme field
- [ ] Save programme
- [ ] View on dashboard - script not executed
- [ ] HTML escaped properly

---

## Test Suite 10: Browser Compatibility

Test on multiple browsers:

### Chrome
- [ ] All features work
- [ ] No console errors
- [ ] Styling correct

### Firefox
- [ ] All features work
- [ ] No console errors
- [ ] Styling correct

### Safari
- [ ] All features work
- [ ] No console errors
- [ ] Styling correct

### Edge
- [ ] All features work
- [ ] No console errors
- [ ] Styling correct

### Mobile Safari (iOS)
- [ ] Dashboard loads
- [ ] Touch interactions work
- [ ] Carousel swipeable
- [ ] Modal works

### Chrome Mobile (Android)
- [ ] Dashboard loads
- [ ] Touch interactions work
- [ ] All features accessible

---

## Test Suite 11: Performance

### Test 11.1: Load Times
- [ ] Dashboard loads in < 3 seconds
- [ ] Editor loads in < 3 seconds
- [ ] Calendar renders in < 1 second
- [ ] Modal opens instantly

### Test 11.2: Large Data Sets
- [ ] Add 50+ programmes
- [ ] Dashboard still loads quickly
- [ ] Calendar still responsive
- [ ] Programme list paginated or scrollable

---

## Bug Reporting Template

If you find issues, document them:

```
**Bug Title:** [Short description]

**Severity:** Critical / High / Medium / Low

**Steps to Reproduce:**
1. 
2. 
3. 

**Expected Result:**
[What should happen]

**Actual Result:**
[What actually happens]

**Browser:** [Chrome 90, Firefox 88, etc.]

**Screenshots:** [If applicable]

**Console Errors:** [Copy from browser console]
```

---

## Success Criteria

Plugin is ready for production when:
- [ ] All Test Suites pass
- [ ] No critical or high severity bugs
- [ ] Works on all major browsers
- [ ] Mobile responsive
- [ ] No security vulnerabilities
- [ ] Performance acceptable
- [ ] Documentation complete

---

**Testing completed by:** _______________  
**Date:** _______________  
**Result:** Pass / Fail  
**Notes:** _______________

