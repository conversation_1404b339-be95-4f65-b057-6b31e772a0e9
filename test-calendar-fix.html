<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Calendar Navigation Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .calendar-container {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .calendar-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .nav-btn {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .nav-btn:hover {
            background: #005a87;
        }
        
        .calendar-title {
            font-size: 1.5em;
            font-weight: bold;
        }
        
        .calendar-grid {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 1px;
            background: #ddd;
            border: 1px solid #ddd;
        }
        
        .calendar-day-header {
            background: #f5f5f5;
            padding: 10px;
            text-align: center;
            font-weight: bold;
        }
        
        .calendar-day {
            background: white;
            padding: 10px;
            text-align: center;
            min-height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .calendar-day.other-month {
            color: #ccc;
        }
        
        .calendar-day.today {
            background: #007cba;
            color: white;
            font-weight: bold;
        }
        
        .test-info {
            background: #f0f8ff;
            border: 1px solid #007cba;
            border-radius: 4px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .test-log {
            background: #f9f9f9;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin: 20px 0;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .log-entry {
            margin: 5px 0;
            padding: 5px;
            border-left: 3px solid #007cba;
            background: white;
        }
    </style>
</head>
<body>
    <h1>Calendar Navigation Fix Test</h1>
    
    <div class="test-info">
        <h3>Test Instructions:</h3>
        <ol>
            <li>Click the "Previous" and "Next" buttons multiple times</li>
            <li>Verify that months advance/retreat sequentially without skipping</li>
            <li>Test year transitions (December ↔ January)</li>
            <li>Check the log below for navigation events</li>
        </ol>
    </div>
    
    <div class="calendar-container">
        <div class="calendar-header">
            <button id="calendar-prev" class="nav-btn">← Previous</button>
            <div id="calendar-title" class="calendar-title"></div>
            <button id="calendar-next" class="nav-btn">Next →</button>
        </div>
        <div id="calendar-grid" class="calendar-grid"></div>
    </div>
    
    <div class="test-log">
        <h3>Navigation Log:</h3>
        <div id="log-container"></div>
    </div>

    <script>
        // State
        let currentMonth = new Date().getMonth();
        let currentYear = new Date().getFullYear();
        let navigationCount = 0;

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            renderCalendar();
            initCalendarNavigation(); // Initialize navigation only once
            logEvent('Calendar initialized', `${getMonthName(currentMonth)} ${currentYear}`);
        });

        // Render Calendar
        function renderCalendar() {
            const title = document.getElementById('calendar-title');
            const grid = document.getElementById('calendar-grid');

            title.textContent = `${getMonthName(currentMonth)} ${currentYear}`;

            // Get first day of month and number of days
            const firstDay = new Date(currentYear, currentMonth, 1).getDay();
            const daysInMonth = new Date(currentYear, currentMonth + 1, 0).getDate();
            const daysInPrevMonth = new Date(currentYear, currentMonth, 0).getDate();

            let html = '';

            // Day headers
            const dayHeaders = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
            dayHeaders.forEach(day => {
                html += `<div class="calendar-day-header">${day}</div>`;
            });

            // Previous month days
            for (let i = firstDay - 1; i >= 0; i--) {
                const day = daysInPrevMonth - i;
                html += `<div class="calendar-day other-month">${day}</div>`;
            }

            // Current month days
            const today = new Date();
            const todayStr = today.toISOString().split('T')[0];

            for (let day = 1; day <= daysInMonth; day++) {
                const dateStr = `${currentYear}-${String(currentMonth + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
                const isToday = dateStr === todayStr;

                let classes = 'calendar-day current-month';
                if (isToday) classes += ' today';

                html += `<div class="${classes}">${day}</div>`;
            }

            // Next month days
            const totalCells = Math.ceil((firstDay + daysInMonth) / 7) * 7;
            const remainingCells = totalCells - (firstDay + daysInMonth);

            for (let day = 1; day <= remainingCells; day++) {
                html += `<div class="calendar-day other-month">${day}</div>`;
            }

            grid.innerHTML = html;
        }

        // Initialize calendar navigation (FIXED VERSION - called only once)
        function initCalendarNavigation() {
            const prevBtn = document.getElementById('calendar-prev');
            const nextBtn = document.getElementById('calendar-next');

            if (prevBtn) {
                prevBtn.addEventListener('click', () => {
                    navigationCount++;
                    const oldMonth = getMonthName(currentMonth);
                    const oldYear = currentYear;
                    
                    // Create a new date object to handle month transitions correctly
                    const newDate = new Date(currentYear, currentMonth - 1, 1);
                    currentMonth = newDate.getMonth();
                    currentYear = newDate.getFullYear();
                    
                    renderCalendar();
                    logEvent('Previous clicked', `${oldMonth} ${oldYear} → ${getMonthName(currentMonth)} ${currentYear}`);
                });
            }

            if (nextBtn) {
                nextBtn.addEventListener('click', () => {
                    navigationCount++;
                    const oldMonth = getMonthName(currentMonth);
                    const oldYear = currentYear;
                    
                    // Create a new date object to handle month transitions correctly
                    const newDate = new Date(currentYear, currentMonth + 1, 1);
                    currentMonth = newDate.getMonth();
                    currentYear = newDate.getFullYear();
                    
                    renderCalendar();
                    logEvent('Next clicked', `${oldMonth} ${oldYear} → ${getMonthName(currentMonth)} ${currentYear}`);
                });
            }
        }

        // Helper functions
        function getMonthName(monthIndex) {
            const monthNames = ['January', 'February', 'March', 'April', 'May', 'June',
                               'July', 'August', 'September', 'October', 'November', 'December'];
            return monthNames[monthIndex];
        }

        function logEvent(action, details) {
            const logContainer = document.getElementById('log-container');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = 'log-entry';
            logEntry.innerHTML = `<strong>${timestamp}</strong> - ${action}: ${details} (Navigation #${navigationCount})`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }
    </script>
</body>
</html>
