/**
 * Editor Styles
 */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body.cpd-editor-body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: #f7fafc;
    color: #2d3748;
    line-height: 1.6;
}

/* Login Screen */
.cpd-login-screen {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.cpd-login-container {
    background: #fff;
    border-radius: 16px;
    padding: 2.5rem;
    max-width: 400px;
    width: 100%;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.cpd-login-header {
    text-align: center;
    margin-bottom: 2rem;
}

.cpd-login-header h1 {
    font-family: 'Poppins', sans-serif;
    font-size: 1.75rem;
    color: #2d3748;
    margin-bottom: 0.5rem;
}

.cpd-login-header p {
    color: #718096;
}

.cpd-login-form {
    display: flex;
    flex-direction: column;
    gap: 1.25rem;
}

/* Form Elements */
.cpd-form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.cpd-form-group label {
    font-weight: 500;
    color: #4a5568;
    font-size: 0.9rem;
}

.cpd-input,
.cpd-select {
    padding: 0.75rem 1rem;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
    font-family: inherit;
}

.cpd-input:focus,
.cpd-select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.cpd-form-error {
    padding: 0.75rem;
    background: #fed7d7;
    color: #c53030;
    border-radius: 8px;
    font-size: 0.9rem;
}

/* Buttons */
.cpd-btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: inherit;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.cpd-btn-primary {
    background: #667eea;
    color: #fff;
}

.cpd-btn-primary:hover {
    background: #5568d3;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.cpd-btn-secondary {
    background: #e2e8f0;
    color: #4a5568;
}

.cpd-btn-secondary:hover {
    background: #cbd5e0;
}

.cpd-btn-accent {
    background: #48bb78;
    color: #fff;
}

.cpd-btn-accent:hover {
    background: #38a169;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(72, 187, 120, 0.4);
}

.cpd-btn-danger {
    background: #f56565;
    color: #fff;
}

.cpd-btn-danger:hover {
    background: #e53e3e;
}

.cpd-btn-block {
    width: 100%;
}

.cpd-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

.cpd-btn-loading {
    display: none;
}

.cpd-btn.loading .cpd-btn-text {
    display: none;
}

.cpd-btn.loading .cpd-btn-loading {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* Spinner */
.cpd-spinner-small {
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top-color: #fff;
    border-radius: 50%;
    animation: spin 0.8s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Editor Screen */
.cpd-editor-screen {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.cpd-editor-header {
    background: #fff;
    border-bottom: 1px solid #e2e8f0;
    padding: 1rem 1.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.cpd-editor-header-content {
    max-width: 1400px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.cpd-editor-header h1 {
    font-family: 'Poppins', sans-serif;
    font-size: 1.5rem;
    color: #2d3748;
}

.cpd-editor-header-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.cpd-user-info {
    color: #718096;
    font-size: 0.9rem;
}

/* Main Content */
.cpd-editor-main {
    flex: 1;
    max-width: 1400px;
    width: 100%;
    margin: 0 auto;
    padding: 2rem 1.5rem;
}

/* Tabs */
.cpd-editor-tabs {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 2rem;
    border-bottom: 2px solid #e2e8f0;
    overflow-x: auto;
}

.cpd-tab-btn {
    padding: 0.75rem 1.5rem;
    background: none;
    border: none;
    border-bottom: 3px solid transparent;
    font-size: 1rem;
    font-weight: 600;
    color: #718096;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.cpd-tab-btn:hover {
    color: #4a5568;
}

.cpd-tab-btn.active {
    color: #667eea;
    border-bottom-color: #667eea;
}

.cpd-tab-content {
    display: none;
}

.cpd-tab-content.active {
    display: block;
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Sections */
.cpd-section {
    background: #fff;
    border-radius: 12px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.cpd-section h2 {
    font-family: 'Poppins', sans-serif;
    font-size: 1.5rem;
    color: #2d3748;
    margin-bottom: 0.5rem;
}

.cpd-section-description {
    color: #718096;
    margin-bottom: 1.5rem;
}

/* AI Config */
.cpd-ai-config {
    display: flex;
    flex-direction: column;
    gap: 1.25rem;
}

.cpd-help-text {
    font-size: 0.85rem;
    color: #718096;
    display: flex;
    align-items: flex-start;
    gap: 0.5rem;
    margin-top: 0.25rem;
}

.cpd-help-text svg {
    flex-shrink: 0;
    margin-top: 0.1rem;
}

/* Extraction Cards */
.cpd-extraction-card {
    background: #f7fafc;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.cpd-extraction-card h3 {
    font-family: 'Poppins', sans-serif;
    font-size: 1.25rem;
    color: #2d3748;
    margin-bottom: 0.5rem;
}

.cpd-card-description {
    color: #718096;
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

/* Image Upload */
.cpd-image-upload {
    margin-bottom: 1rem;
}

.cpd-file-input {
    display: none;
}

.cpd-file-label {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    padding: 2rem;
    border: 2px dashed #cbd5e0;
    border-radius: 8px;
    background: #fff;
    cursor: pointer;
    transition: all 0.3s ease;
}

.cpd-file-label:hover {
    border-color: #667eea;
    background: #f7fafc;
}

.cpd-file-label svg {
    color: #718096;
}

.cpd-file-label span {
    color: #4a5568;
    font-weight: 500;
}

.cpd-image-preview {
    margin-top: 1rem;
    border-radius: 8px;
    overflow: hidden;
}

.cpd-image-preview img {
    max-width: 100%;
    height: auto;
    display: block;
}

/* Extraction Result */
.cpd-extraction-result {
    margin-top: 1rem;
    padding: 1rem;
    border-radius: 8px;
    background: #f0fff4;
    border: 1px solid #9ae6b4;
}

.cpd-extraction-result.error {
    background: #fff5f5;
    border-color: #fc8181;
}

/* Manual Form */
.cpd-manual-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.cpd-form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

/* Filter Bar */
.cpd-filter-bar {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
    align-items: end;
}

/* Programmes List */
.cpd-programmes-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.cpd-programme-item {
    background: #f7fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 1rem;
}

.cpd-programme-info h4 {
    font-family: 'Poppins', sans-serif;
    color: #2d3748;
    margin-bottom: 0.5rem;
}

.cpd-programme-meta {
    font-size: 0.9rem;
    color: #718096;
}

.cpd-programme-actions {
    display: flex;
    gap: 0.5rem;
}

/* Responsive */
@media (max-width: 768px) {
    .cpd-editor-header-content {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .cpd-form-row,
    .cpd-filter-bar {
        grid-template-columns: 1fr;
    }
    
    .cpd-programme-item {
        flex-direction: column;
    }
}

