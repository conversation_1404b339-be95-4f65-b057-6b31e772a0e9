<?php
/**
 * Test Carousel Animations
 * 
 * This file tests the carousel animation functionality
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

// Test if the carousel animation settings are working
function test_carousel_animations() {
    echo "<h2>Carousel Animation Settings Test</h2>";
    
    // Test default values
    $animation_type = get_option('cpd_carousel_animation_type', 'slide');
    $animation_speed = get_option('cpd_carousel_animation_speed', '500');
    $auto_interval = get_option('cpd_carousel_auto_interval', '5000');
    $direction = get_option('cpd_carousel_direction', 'right-to-left');
    
    echo "<p><strong>Animation Type:</strong> " . esc_html($animation_type) . "</p>";
    echo "<p><strong>Animation Speed:</strong> " . esc_html($animation_speed) . "ms</p>";
    echo "<p><strong>Auto Interval:</strong> " . esc_html($auto_interval) . "ms</p>";
    echo "<p><strong>Direction:</strong> " . esc_html($direction) . "</p>";
    
    // Test if settings are properly registered
    $settings = CPD_Admin_Settings::get_all_settings();
    if (isset($settings['carousel'])) {
        echo "<p style='color: green;'><strong>✓ Carousel settings are properly registered</strong></p>";
        echo "<pre>" . print_r($settings['carousel'], true) . "</pre>";
    } else {
        echo "<p style='color: red;'><strong>✗ Carousel settings are NOT registered</strong></p>";
    }
    
    // Test JavaScript data
    echo "<h3>JavaScript Data Test</h3>";
    echo "<script>
        console.log('Carousel Settings:', " . json_encode($settings['carousel']) . ");
    </script>";
    echo "<p>Check browser console for carousel settings data</p>";
}

// Run the test
test_carousel_animations();
?>
