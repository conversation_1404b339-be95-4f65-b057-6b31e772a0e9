<?php
/**
 * Debug Page - Runs inside WordPress Admin
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}
?>

<div class="wrap">
    <h1>🔍 Admin Save Debug - Live Test</h1>
    
    <div class="notice notice-info">
        <p><strong>This page runs inside WordPress admin context to test the actual environment.</strong></p>
    </div>

    <!-- Test 1: Check if we're in admin -->
    <div class="card">
        <h2>Test 1: WordPress Context</h2>
        <table class="widefat">
            <tr>
                <th>Check</th>
                <th>Value</th>
                <th>Status</th>
            </tr>
            <tr>
                <td>is_admin()</td>
                <td><?php echo is_admin() ? 'true' : 'false'; ?></td>
                <td><?php echo is_admin() ? '✅' : '❌'; ?></td>
            </tr>
            <tr>
                <td>Current Screen</td>
                <td><?php $screen = get_current_screen(); echo $screen ? $screen->id : 'N/A'; ?></td>
                <td>-</td>
            </tr>
            <tr>
                <td>Current Hook</td>
                <td><code><?php global $hook_suffix; echo esc_html($hook_suffix); ?></code></td>
                <td>-</td>
            </tr>
            <tr>
                <td>Current User Can Manage</td>
                <td><?php echo current_user_can('manage_options') ? 'Yes' : 'No'; ?></td>
                <td><?php echo current_user_can('manage_options') ? '✅' : '❌'; ?></td>
            </tr>
        </table>
    </div>

    <!-- Test 2: Check Classes -->
    <div class="card" style="margin-top: 20px;">
        <h2>Test 2: Class Loading</h2>
        <table class="widefat">
            <tr>
                <th>Class</th>
                <th>Status</th>
                <th>Methods</th>
            </tr>
            <?php
            $classes = array(
                'CPD_Admin' => array('init', 'add_admin_menu', 'enqueue_scripts'),
                'CPD_Admin_Settings' => array('init', 'register_settings', 'get_all_settings'),
                'CPD_AJAX' => array('init', 'save_settings'),
            );
            
            foreach ($classes as $class => $methods) {
                $exists = class_exists($class);
                echo '<tr>';
                echo '<td><strong>' . esc_html($class) . '</strong></td>';
                echo '<td>' . ($exists ? '✅ Loaded' : '❌ Not Loaded') . '</td>';
                echo '<td>';
                if ($exists) {
                    foreach ($methods as $method) {
                        $has_method = method_exists($class, $method);
                        echo '<span style="color: ' . ($has_method ? 'green' : 'red') . ';">';
                        echo ($has_method ? '✓' : '✗') . ' ' . esc_html($method) . '</span><br>';
                    }
                }
                echo '</td>';
                echo '</tr>';
            }
            ?>
        </table>
    </div>

    <!-- Test 3: Check Hooks -->
    <div class="card" style="margin-top: 20px;">
        <h2>Test 3: Hook Registration</h2>
        <table class="widefat">
            <tr>
                <th>Hook</th>
                <th>Status</th>
                <th>Callback</th>
            </tr>
            <?php
            $hooks = array(
                'admin_menu' => 'CPD_Admin::add_admin_menu',
                'admin_enqueue_scripts' => 'CPD_Admin::enqueue_scripts',
                'wp_ajax_cpd_save_settings' => 'CPD_AJAX::save_settings',
            );
            
            foreach ($hooks as $hook => $expected_callback) {
                $has_action = has_action($hook);
                echo '<tr>';
                echo '<td><code>' . esc_html($hook) . '</code></td>';
                echo '<td>' . ($has_action ? '✅ Registered' : '❌ Not Registered') . '</td>';
                echo '<td>';
                if ($has_action) {
                    global $wp_filter;
                    if (isset($wp_filter[$hook])) {
                        echo '<pre style="font-size: 11px; max-height: 100px; overflow: auto;">';
                        foreach ($wp_filter[$hook]->callbacks as $priority => $callbacks) {
                            foreach ($callbacks as $callback) {
                                if (is_array($callback['function'])) {
                                    echo 'Priority ' . $priority . ': ' . 
                                         (is_object($callback['function'][0]) ? get_class($callback['function'][0]) : $callback['function'][0]) . 
                                         '::' . $callback['function'][1] . "\n";
                                } else {
                                    echo 'Priority ' . $priority . ': ' . $callback['function'] . "\n";
                                }
                            }
                        }
                        echo '</pre>';
                    }
                }
                echo '</td>';
                echo '</tr>';
            }
            ?>
        </table>
    </div>

    <!-- Test 4: Check Scripts -->
    <div class="card" style="margin-top: 20px;">
        <h2>Test 4: Enqueued Scripts & Styles</h2>
        <table class="widefat">
            <tr>
                <th>Handle</th>
                <th>Status</th>
                <th>Source</th>
            </tr>
            <?php
            global $wp_scripts, $wp_styles;
            
            $expected_scripts = array('cpd-admin-script', 'cpd-admin-programmes', 'wp-color-picker');
            foreach ($expected_scripts as $handle) {
                $enqueued = wp_script_is($handle, 'enqueued');
                $registered = wp_script_is($handle, 'registered');
                
                echo '<tr>';
                echo '<td><code>' . esc_html($handle) . '</code></td>';
                echo '<td>';
                if ($enqueued) {
                    echo '✅ Enqueued';
                } elseif ($registered) {
                    echo '⚠️ Registered but not enqueued';
                } else {
                    echo '❌ Not registered';
                }
                echo '</td>';
                echo '<td>';
                if ($registered && isset($wp_scripts->registered[$handle])) {
                    echo '<small>' . esc_html($wp_scripts->registered[$handle]->src) . '</small>';
                }
                echo '</td>';
                echo '</tr>';
            }
            ?>
        </table>
    </div>

    <!-- Test 5: JavaScript Test -->
    <div class="card" style="margin-top: 20px;">
        <h2>Test 5: JavaScript Environment</h2>
        <div id="js-test-results"></div>
        <button type="button" class="button button-primary" id="run-js-test">Run JavaScript Test</button>
    </div>

    <!-- Test 6: Live AJAX Test -->
    <div class="card" style="margin-top: 20px;">
        <h2>Test 6: Live AJAX Save Test</h2>
        <button type="button" class="button button-primary" id="run-ajax-test">Test AJAX Save</button>
        <div id="ajax-test-results" style="margin-top: 10px;"></div>
    </div>

    <!-- Test 7: Form Test -->
    <div class="card" style="margin-top: 20px;">
        <h2>Test 7: Form Submit Test</h2>
        <form id="test-form">
            <input type="text" name="test_field" value="Test Value" class="regular-text">
            <button type="submit" class="button button-primary">Test Submit</button>
        </form>
        <div id="form-test-results" style="margin-top: 10px;"></div>
    </div>

    <!-- Console Log -->
    <div class="card" style="margin-top: 20px;">
        <h2>Console Log</h2>
        <button type="button" class="button" id="clear-console">Clear</button>
        <div id="console-log" style="background: #f0f0f0; padding: 10px; margin-top: 10px; max-height: 300px; overflow-y: auto; font-family: monospace; font-size: 12px;"></div>
    </div>
</div>

<script>
jQuery(document).ready(function($) {
    
    function log(message, type = 'info') {
        const colors = {
            'info': '#333',
            'success': 'green',
            'error': 'red',
            'warning': 'orange'
        };
        
        const entry = $('<div>').css({
            'padding': '5px',
            'border-bottom': '1px solid #ddd',
            'color': colors[type]
        }).text(new Date().toLocaleTimeString() + ': ' + message);
        
        $('#console-log').prepend(entry);
    }
    
    $('#clear-console').on('click', function() {
        $('#console-log').empty();
        log('Console cleared');
    });
    
    // Test 5: JavaScript Environment
    $('#run-js-test').on('click', function() {
        let results = '<table class="widefat"><tr><th>Check</th><th>Status</th></tr>';
        
        const checks = {
            'jQuery': typeof jQuery !== 'undefined',
            'jQuery.ajax': typeof jQuery.ajax !== 'undefined',
            'cpdAdmin': typeof cpdAdmin !== 'undefined',
            'cpdAdmin.ajaxUrl': typeof cpdAdmin !== 'undefined' && cpdAdmin.ajaxUrl,
            'cpdAdmin.nonce': typeof cpdAdmin !== 'undefined' && cpdAdmin.nonce,
            'wp': typeof wp !== 'undefined',
            'wp.media': typeof wp !== 'undefined' && typeof wp.media !== 'undefined'
        };
        
        for (let check in checks) {
            results += '<tr><td>' + check + '</td><td>' + 
                      (checks[check] ? '✅ Available' : '❌ Missing') + '</td></tr>';
        }
        
        results += '</table>';
        
        if (typeof cpdAdmin !== 'undefined') {
            results += '<h4>cpdAdmin Object:</h4><pre>' + JSON.stringify(cpdAdmin, null, 2) + '</pre>';
        }
        
        $('#js-test-results').html(results);
        log('JavaScript test completed');
    });
    
    // Test 6: AJAX Test
    $('#run-ajax-test').on('click', function() {
        const button = $(this);
        button.prop('disabled', true).text('Testing...');
        
        log('Starting AJAX test');
        
        if (typeof cpdAdmin === 'undefined') {
            $('#ajax-test-results').html('<div class="notice notice-error"><p>❌ cpdAdmin is not defined! Scripts not loaded properly.</p></div>');
            button.prop('disabled', false).text('Test AJAX Save');
            log('AJAX test failed: cpdAdmin not defined', 'error');
            return;
        }
        
        $.ajax({
            url: cpdAdmin.ajaxUrl,
            type: 'POST',
            data: {
                action: 'cpd_save_settings',
                nonce: cpdAdmin.nonce,
                settings: {
                    test_setting: 'test_value_' + Date.now()
                }
            },
            success: function(response) {
                log('AJAX response received: ' + JSON.stringify(response), 'success');
                if (response.success) {
                    $('#ajax-test-results').html('<div class="notice notice-success"><p>✅ AJAX Save Works!</p><pre>' + JSON.stringify(response, null, 2) + '</pre></div>');
                } else {
                    $('#ajax-test-results').html('<div class="notice notice-error"><p>❌ AJAX Failed</p><pre>' + JSON.stringify(response, null, 2) + '</pre></div>');
                }
            },
            error: function(xhr, status, error) {
                log('AJAX error: ' + error, 'error');
                $('#ajax-test-results').html('<div class="notice notice-error"><p>❌ AJAX Error: ' + error + '</p></div>');
            },
            complete: function() {
                button.prop('disabled', false).text('Test AJAX Save');
            }
        });
    });
    
    // Test 7: Form Test
    $('#test-form').on('submit', function(e) {
        e.preventDefault();
        log('Form submitted!', 'success');
        $('#form-test-results').html('<div class="notice notice-success"><p>✅ Form submit event works!</p></div>');
    });
    
    log('Debug page loaded');
    
    // Auto-run JavaScript test
    $('#run-js-test').trigger('click');
});
</script>

<style>
.card {
    background: white;
    padding: 20px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}
.widefat th {
    background: #f0f0f0;
}
</style>

