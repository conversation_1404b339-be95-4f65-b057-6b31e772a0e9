# Installation Guide - Church Programme Dashboard

## Quick Start

### Step 1: Upload Plugin
1. Download the entire `church-programme-dashboard` folder
2. Upload to your WordPress installation at: `wp-content/plugins/`
3. The folder structure should be: `wp-content/plugins/church-programme-dashboard/`

### Step 2: Activate Plugin
1. Log in to your WordPress admin panel
2. Go to **Plugins** > **Installed Plugins**
3. Find "Church Programme Dashboard"
4. Click **Activate**

### Step 3: Initial Configuration
After activation, the plugin will:
- Create 4 custom database tables
- Set default options
- Flush rewrite rules

## Post-Installation Setup

### 1. Access Admin Panel
Go to **Church Programme** in your WordPress admin menu

### 2. Configure General Settings

#### Dashboard Title
- Default: "Church Programme"
- This appears as the main heading on your dashboard

#### Custom Path Name
- Default: "programme"
- Your dashboard URL will be: `yoursite.com/programme/`
- Your editor URL will be: `yoursite.com/programme/editor/`
- **Important:** After changing this, save settings and visit the dashboard to ensure rewrite rules are updated

#### Header Styling
- **Background Color:** Choose a color for the header
- **Background Image:** Upload an optional background image
- The image will overlay the background color

### 3. Configure Colors & Styling

Navigate to the **Colors & Styling** tab:

- **Primary Color** (Default: #4a5568) - Main UI elements
- **Secondary Color** (Default: #718096) - Secondary text and elements
- **Accent Color** (Default: #3182ce) - Buttons, links, highlights
- **Text Color** (Default: #2d3748) - Main text color

These colors use CSS variables and update the entire dashboard theme.

### 4. Configure Programme Labels

Navigate to the **Programme Labels** tab:

For each programme type, you can customize:
- Display label (name shown on dashboard)
- Default time

**Programme Types:**
1. MIET BALANG (Default: 6:30 PM)
2. JINGIASENG SAMLA (Default: 6:30 PM)
3. JINGIASENG 1:00 Baje (Default: 1:00 PM)
4. JINGIASENG KHYNNAH (Default: 3:00 PM)
5. JINGIASENG IING (Default: 6:30 PM)

### 5. Configure Notice Board

Navigate to the **Notice Board** tab:

- **Enable Notice Board:** Check to show on dashboard
- **Title:** Heading for the notice board
- **Content:** Rich text editor for your message
  - Supports bold, italic, lists, links
  - HTML is allowed (sanitized for security)

### 6. Configure Cookie Consent

Navigate to the **Cookie Consent** tab:

- **Consent Message:** Text shown to users
- **Button Text:** Text on the accept button

The cookie consent:
- Appears at bottom of dashboard
- Stores acceptance in localStorage
- Disappears after user accepts
- Complies with basic cookie regulations

### 7. Create Editor Users

Go to **Church Programme** > **Editor Users**

#### Add New User
1. Enter username (required)
2. Enter password (required)
   - Use "Generate Strong Password" button for security
3. Enter email (optional, for your records)
4. Click **Add User**

#### Manage Users
- View all editor users in the table
- See last login times
- Delete users as needed
- **Note:** Only WordPress admins can manage editor users

## Setting Up AI Extraction

### 1. Choose an AI Provider

#### Option A: OpenRouter
1. Sign up at https://openrouter.ai
2. Add credits to your account
3. Get API key from dashboard
4. **Recommended Models:**
   - GPT-4 Vision
   - Claude 3 Opus
   - Claude 3 Sonnet

#### Option B: Google Gemini
1. Visit https://makersuite.google.com/app/apikey
2. Create a new API key
3. **Recommended Models:**
   - Gemini Pro Vision
   - Gemini 1.5 Pro
   - Gemini 2.0 Flash

#### Option C: DeepSeek
1. Sign up at https://platform.deepseek.com
2. Get API key from dashboard
3. **Recommended Models:**
   - DeepSeek VL

### 2. Configure AI in Editor

1. Access editor at: `yoursite.com/programme/editor/`
2. Login with editor credentials
3. Go to **AI Extraction** tab
4. Select your AI provider
5. Enter your API key
   - **Security Note:** API key is stored in browser localStorage only
   - Never sent to WordPress server
   - Stored per browser/device
6. Click **Fetch Available Models**
7. Select a vision-capable model from the dropdown
8. Configuration is saved automatically

### 3. Extract Programme Data

For each programme type:

1. Click **Choose Image** or drag & drop
2. Select the programme image from your computer
3. Preview will appear below
4. Click **Extract Data** button
5. Wait for AI to process (may take 10-30 seconds)
6. Review extracted data in the result box
7. Data is automatically saved to database

**Tips for Best Results:**
- Use clear, high-resolution images
- Ensure text is readable
- Avoid blurry or low-quality scans
- Good lighting in photos
- Straight, not angled photos

## Adding Programme Data Manually

If you prefer not to use AI or need to make corrections:

1. Go to **Manual Entry** tab
2. Select programme type
3. Enter date (YYYY-MM-DD format)
4. Enter time (HH:MM format)
5. Fill in participant fields (varies by programme type)
6. Click **Save Programme**

## Managing Existing Programmes

1. Go to **Manage Programmes** tab
2. Use filters to find specific programmes:
   - Filter by type
   - Filter by date range
3. Click **Apply Filter**
4. View list of programmes
5. Edit or delete as needed

## Viewing Your Dashboard

### Public Dashboard
- URL: `yoursite.com/programme/` (or your custom path)
- Accessible to anyone with the link
- No login required
- Shows:
  - Header with title
  - Carousel with 3 upcoming programmes
  - Notice board (if enabled)
  - Interactive calendar
  - Footer with attribution

### Editor Page
- URL: `yoursite.com/programme/editor/`
- Requires login
- Only accessible to editor users
- Shows:
  - AI extraction interface
  - Manual entry form
  - Programme management

## Troubleshooting Installation

### Dashboard Shows 404 Error
**Solution:**
1. Go to **Settings** > **Permalinks** in WordPress
2. Click **Save Changes** (don't change anything)
3. This flushes rewrite rules
4. Try accessing dashboard again

### Rewrite Rules Not Working
**Solution:**
1. Deactivate the plugin
2. Reactivate the plugin
3. Visit dashboard URL

### Database Tables Not Created
**Solution:**
1. Check PHP error logs
2. Ensure database user has CREATE TABLE permissions
3. Try deactivating and reactivating plugin

### AI Extraction Not Working
**Possible Issues:**
1. **Invalid API Key**
   - Verify key is correct
   - Check if key has credits/quota
2. **Model Not Supporting Vision**
   - Select a different model
   - Ensure model name contains "vision" or "vl"
3. **Image Too Large**
   - Resize image to under 5MB
   - Use JPEG format for smaller file size
4. **Browser Console Errors**
   - Open browser developer tools (F12)
   - Check Console tab for errors
   - Report errors for support

### Editor Login Not Working
**Possible Issues:**
1. **User Not Created**
   - Verify user exists in Editor Users page
   - Check username spelling
2. **Session Issues**
   - Clear browser cookies
   - Try different browser
3. **Server Configuration**
   - Ensure PHP sessions are enabled
   - Check session save path permissions

## Server Requirements

### Minimum Requirements
- WordPress 5.8+
- PHP 7.4+
- MySQL 5.6+ or MariaDB 10.0+
- 64MB PHP memory limit (128MB recommended)

### Recommended Requirements
- WordPress 6.0+
- PHP 8.0+
- MySQL 8.0+ or MariaDB 10.5+
- 256MB PHP memory limit
- HTTPS enabled
- Modern web server (Apache 2.4+ or Nginx 1.18+)

### PHP Extensions Required
- mysqli or PDO
- json
- fileinfo (for image uploads)
- curl (for AI API calls)

## Security Considerations

### API Key Storage
- Stored in browser localStorage
- Never transmitted to WordPress server
- Separate per browser/device
- Users must configure on each device

### Editor Authentication
- Session-based authentication
- Passwords hashed with PHP password_hash()
- WordPress nonce verification
- Prepared SQL statements

### File Uploads
- Only image files accepted
- WordPress media library security
- File type validation
- Size limits enforced

## Performance Optimization

### Caching
- Dashboard pages set cache headers
- Consider using a caching plugin
- Exclude editor page from caching

### Database
- Indexed columns for fast queries
- Efficient date range queries
- Minimal database calls

### Assets
- CSS and JS versioned for cache busting
- Google Fonts loaded from CDN
- Minimal external dependencies

## Backup Recommendations

### Before Updates
1. Backup WordPress database
2. Backup plugin files
3. Export programme data (future feature)

### Regular Backups
- Use WordPress backup plugin
- Include database tables:
  - wp_cpd_programmes
  - wp_cpd_editor_users
  - wp_cpd_ai_settings
  - wp_cpd_extraction_history

## Getting Help

### Documentation
- README.md - Feature overview
- INSTALLATION.md - This file
- Inline code comments

### Support
- Website: https://www.jermesa.com
- Check browser console for errors
- Check WordPress debug.log
- Provide error messages when requesting support

## Next Steps

After installation:
1. ✅ Configure general settings
2. ✅ Set up colors and styling
3. ✅ Create editor users
4. ✅ Configure AI (optional)
5. ✅ Add programme data
6. ✅ Test dashboard display
7. ✅ Share dashboard URL with congregation

---

**Congratulations!** Your Church Programme Dashboard is now installed and ready to use.

For questions or support, visit https://www.jermesa.com

