<?php
/**
 * Subdomain Handler Class
 * 
 * Handles custom subdomain routing for the dashboard
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

class CPD_Subdomain {

    /**
     * Initialize
     */
    public static function init() {
        add_action('init', array(__CLASS__, 'handle_subdomain_request'), 1);
        add_action('template_redirect', array(__CLASS__, 'handle_path_fallback'), 1);
    }

    /**
     * Handle subdomain request
     */
    public static function handle_subdomain_request() {
        // Get current host
        $current_host = isset($_SERVER['HTTP_HOST']) ? strtolower($_SERVER['HTTP_HOST']) : '';

        if (empty($current_host)) {
            return;
        }

        // Get main site URL
        $site_url = parse_url(home_url(), PHP_URL_HOST);

        // Get configured subdomains
        $dashboard_subdomain = get_option('cpd_dashboard_subdomain', 'churchprogramme');
        $editor_subdomain = get_option('cpd_editor_subdomain', 'churcheditor');

        // Extract base domain (remove www if present)
        $base_domain = preg_replace('/^www\./', '', strtolower($site_url));

        // Build expected subdomain hosts
        $dashboard_host = strtolower($dashboard_subdomain . '.' . $base_domain);
        $editor_host = strtolower($editor_subdomain . '.' . $base_domain);

        // Remove www from current host for comparison
        $current_host_clean = preg_replace('/^www\./', '', $current_host);

        // Check if current request is for dashboard subdomain
        if ($current_host_clean === $dashboard_host) {
            self::render_dashboard();
            exit;
        }

        // Check if current request is for editor subdomain
        if ($current_host_clean === $editor_host) {
            self::render_editor();
            exit;
        }
    }

    /**
     * Handle path-based fallback (for testing before DNS is configured)
     */
    public static function handle_path_fallback() {
        $request_uri = isset($_SERVER['REQUEST_URI']) ? $_SERVER['REQUEST_URI'] : '';

        // Check for dashboard path
        if (preg_match('#^/cpd-dashboard/?$#', $request_uri)) {
            self::render_dashboard();
            exit;
        }

        // Check for editor path
        if (preg_match('#^/cpd-editor/?$#', $request_uri)) {
            self::render_editor();
            exit;
        }
    }

    /**
     * Render dashboard
     */
    private static function render_dashboard() {
        // Prevent WordPress from trying to load a post/page
        global $wp_query;
        $wp_query->is_404 = false;
        $wp_query->is_home = false;

        status_header(200);

        // Prevent caching
        header('Cache-Control: no-cache, no-store, must-revalidate');
        header('Pragma: no-cache');
        header('Expires: 0');

        // Add CORS headers to allow subdomain access
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
        header('Access-Control-Allow-Headers: Content-Type, Authorization, X-WP-Nonce');

        // Prevent theme from loading
        add_filter('template_include', '__return_false');
        add_filter('show_admin_bar', '__return_false');

        // Remove all theme actions
        remove_all_actions('wp_head');
        remove_all_actions('wp_footer');
        remove_all_actions('wp_enqueue_scripts');

        include CPD_PLUGIN_DIR . 'public/templates/dashboard.php';
    }

    /**
     * Render editor
     */
    private static function render_editor() {
        // Prevent WordPress from trying to load a post/page
        global $wp_query;
        $wp_query->is_404 = false;
        $wp_query->is_home = false;

        status_header(200);

        // Prevent caching
        header('Cache-Control: no-cache, no-store, must-revalidate');
        header('Pragma: no-cache');
        header('Expires: 0');

        // Add CORS headers to allow subdomain access
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
        header('Access-Control-Allow-Headers: Content-Type, Authorization, X-WP-Nonce');

        // Prevent theme from loading
        add_filter('template_include', '__return_false');
        add_filter('show_admin_bar', '__return_false');

        // Remove all theme actions
        remove_all_actions('wp_head');
        remove_all_actions('wp_footer');
        remove_all_actions('wp_enqueue_scripts');

        include CPD_PLUGIN_DIR . 'public/templates/editor.php';
    }

    /**
     * Get dashboard URL
     */
    public static function get_dashboard_url($fallback = false) {
        if ($fallback) {
            // Return path-based URL for testing before DNS is configured
            return home_url('/cpd-dashboard/');
        }

        $dashboard_subdomain = get_option('cpd_dashboard_subdomain', 'churchprogramme');
        $site_url = parse_url(home_url(), PHP_URL_HOST);
        $base_domain = preg_replace('/^www\./', '', $site_url);

        $protocol = is_ssl() ? 'https://' : 'http://';
        return $protocol . $dashboard_subdomain . '.' . $base_domain . '/';
    }

    /**
     * Get editor URL
     */
    public static function get_editor_url($fallback = false) {
        if ($fallback) {
            // Return path-based URL for testing before DNS is configured
            return home_url('/cpd-editor/');
        }

        $editor_subdomain = get_option('cpd_editor_subdomain', 'churcheditor');
        $site_url = parse_url(home_url(), PHP_URL_HOST);
        $base_domain = preg_replace('/^www\./', '', $site_url);

        $protocol = is_ssl() ? 'https://' : 'http://';
        return $protocol . $editor_subdomain . '.' . $base_domain . '/';
    }

    /**
     * Get base domain
     */
    public static function get_base_domain() {
        $site_url = parse_url(home_url(), PHP_URL_HOST);
        return preg_replace('/^www\./', '', $site_url);
    }
}

