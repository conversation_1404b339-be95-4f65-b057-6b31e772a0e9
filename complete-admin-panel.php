<?php
/**
 * Complete Admin Panel - Single Page with Tabs
 * 
 * This script provides a complete admin panel with all settings organized in tabs
 * when the original admin panel save functionality is not working.
 * 
 * Instructions:
 * 1. Upload this file to your WordPress root directory (same level as wp-config.php)
 * 2. Access it via browser: https://yoursite.com/complete-admin-panel.php
 * 3. Configure all settings across different tabs
 * 4. Click "Save All Settings" on each tab
 * 5. Delete this file after use for security
 */

// Security check
if (!defined('ABSPATH')) {
    // Load WordPress
    require_once('wp-load.php');
}

// Check if user is logged in to secure admin panel
session_start();
$custom_users = get_option('cpd_custom_users', array());

function cpd_is_logged_in() {
    return isset($_SESSION['cpd_logged_in']) && $_SESSION['cpd_logged_in'] === true;
}

// Check if user needs to login
if (!cpd_is_logged_in()) {
    // Redirect to secure login
    header('Location: secure-admin-panel-simple.php');
    exit;
}

// Check if user has admin role in our custom system
$current_user_role = isset($_SESSION['cpd_role']) ? $_SESSION['cpd_role'] : '';
if ($current_user_role !== 'administrator') {
    die('You do not have permission to access this page. Only administrators can access the complete admin panel.');
}

// Load current settings
$current_settings = array(
    // General Settings
    'dashboard_title' => get_option('cpd_dashboard_title', 'Church Programme Dashboard'),
    'header_bg_color' => get_option('cpd_header_bg_color', '#1e73be'),
    'header_bg_image' => get_option('cpd_header_bg_image', ''),
    
    // Subdomain Settings
    'dashboard_subdomain' => get_option('cpd_dashboard_subdomain', 'dashboard'),
    'editor_subdomain' => get_option('cpd_editor_subdomain', 'editor'),
    
    // Color Settings
    'primary_color' => get_option('cpd_primary_color', '#1e73be'),
    'secondary_color' => get_option('cpd_secondary_color', '#2c3e50'),
    'accent_color' => get_option('cpd_accent_color', '#e74c3c'),
    'text_color' => get_option('cpd_text_color', '#333333'),
    
    // Programme Labels
    'label_miet_balang' => get_option('cpd_label_miet_balang', 'Miet Balang'),
    'label_miet_balang_time' => get_option('cpd_label_miet_balang_time', '6:00 AM'),
    'label_jingiaseng_samla' => get_option('cpd_label_jingiaseng_samla', 'Jingiaseng Samla'),
    'label_jingiaseng_samla_time' => get_option('cpd_label_jingiaseng_samla_time', '8:00 AM'),
    'label_jingiaseng_1pm' => get_option('cpd_label_jingiaseng_1pm', 'Jingiaseng 1PM'),
    'label_jingiaseng_1pm_time' => get_option('cpd_label_jingiaseng_1pm_time', '1:00 PM'),
    'label_jingiaseng_khynnah' => get_option('cpd_label_jingiaseng_khynnah', 'Jingiaseng Khynnah'),
    'label_jingiaseng_khynnah_time' => get_option('cpd_label_jingiaseng_khynnah_time', '4:00 PM'),
    'label_jingiaseng_iing' => get_option('cpd_label_jingiaseng_iing', 'Jingiaseng Iing'),
    'label_jingiaseng_iing_time' => get_option('cpd_label_jingiaseng_iing_time', '6:00 PM'),
    
    // Notice Board Settings
    'notice_board_enabled' => get_option('cpd_notice_board_enabled', '0'),
    'notice_board_title' => get_option('cpd_notice_board_title', 'Notice Board'),
    'notice_board_content' => get_option('cpd_notice_board_content', ''),
    
    // Carousel Settings
    'carousel_animation_type' => get_option('cpd_carousel_animation_type', 'slide'),
    'carousel_animation_speed' => get_option('cpd_carousel_animation_speed', '500'),
    'carousel_auto_interval' => get_option('cpd_carousel_auto_interval', '5000'),
    'carousel_direction' => get_option('cpd_carousel_direction', 'right-to-left'),
    'carousel_gradient_type' => get_option('cpd_carousel_gradient_type', 'preset'),
    'carousel_gradient_preset' => get_option('cpd_carousel_gradient_preset', 'blue-purple'),
    'carousel_gradient_custom_start' => get_option('cpd_carousel_gradient_custom_start', '#667eea'),
    'carousel_gradient_custom_end' => get_option('cpd_carousel_gradient_custom_end', '#764ba2'),
    'carousel_gradient_custom_angle' => get_option('cpd_carousel_gradient_custom_angle', '135'),
    
    // Cookie Consent Settings
    'cookie_consent_text' => get_option('cpd_cookie_consent_text', 'This website uses cookies to ensure you get the best experience.'),
    'cookie_consent_button' => get_option('cpd_cookie_consent_button', 'Accept'),
    
    // Quick Links
    'quick_links' => get_option('cpd_quick_links', '[]'),
);

$message = '';
$active_tab = isset($_GET['tab']) ? sanitize_text_field($_GET['tab']) : 'general';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $tab = isset($_POST['tab']) ? sanitize_text_field($_POST['tab']) : 'general';
    
    switch($tab) {
        case 'general':
            update_option('cpd_dashboard_title', sanitize_text_field($_POST['dashboard_title']));
            update_option('cpd_header_bg_color', sanitize_hex_color($_POST['header_bg_color']));
            update_option('cpd_header_bg_image', esc_url_raw($_POST['header_bg_image']));
            $message = "✅ General settings saved successfully!";
            break;
            
        case 'subdomain':
            update_option('cpd_dashboard_subdomain', sanitize_text_field($_POST['dashboard_subdomain']));
            update_option('cpd_editor_subdomain', sanitize_text_field($_POST['editor_subdomain']));
            $message = "✅ Subdomain settings saved successfully!";
            break;
            
        case 'colors':
            update_option('cpd_primary_color', sanitize_hex_color($_POST['primary_color']));
            update_option('cpd_secondary_color', sanitize_hex_color($_POST['secondary_color']));
            update_option('cpd_accent_color', sanitize_hex_color($_POST['accent_color']));
            update_option('cpd_text_color', sanitize_hex_color($_POST['text_color']));
            $message = "✅ Color settings saved successfully!";
            break;
            
        case 'labels':
            update_option('cpd_label_miet_balang', sanitize_text_field($_POST['label_miet_balang']));
            update_option('cpd_label_miet_balang_time', sanitize_text_field($_POST['label_miet_balang_time']));
            update_option('cpd_label_jingiaseng_samla', sanitize_text_field($_POST['label_jingiaseng_samla']));
            update_option('cpd_label_jingiaseng_samla_time', sanitize_text_field($_POST['label_jingiaseng_samla_time']));
            update_option('cpd_label_jingiaseng_1pm', sanitize_text_field($_POST['label_jingiaseng_1pm']));
            update_option('cpd_label_jingiaseng_1pm_time', sanitize_text_field($_POST['label_jingiaseng_1pm_time']));
            update_option('cpd_label_jingiaseng_khynnah', sanitize_text_field($_POST['label_jingiaseng_khynnah']));
            update_option('cpd_label_jingiaseng_khynnah_time', sanitize_text_field($_POST['label_jingiaseng_khynnah_time']));
            update_option('cpd_label_jingiaseng_iing', sanitize_text_field($_POST['label_jingiaseng_iing']));
            update_option('cpd_label_jingiaseng_iing_time', sanitize_text_field($_POST['label_jingiaseng_iing_time']));
            $message = "✅ Programme labels saved successfully!";
            break;
            
        case 'notice':
            update_option('cpd_notice_board_enabled', isset($_POST['notice_board_enabled']) ? '1' : '0');
            update_option('cpd_notice_board_title', sanitize_text_field($_POST['notice_board_title']));
            update_option('cpd_notice_board_content', wp_kses_post($_POST['notice_board_content']));
            $message = "✅ Notice board settings saved successfully!";
            break;
            
        case 'carousel':
            update_option('cpd_carousel_animation_type', sanitize_text_field($_POST['carousel_animation_type']));
            update_option('cpd_carousel_animation_speed', intval($_POST['carousel_animation_speed']));
            update_option('cpd_carousel_auto_interval', intval($_POST['carousel_auto_interval']));
            update_option('cpd_carousel_direction', sanitize_text_field($_POST['carousel_direction']));
            update_option('cpd_carousel_gradient_type', sanitize_text_field($_POST['carousel_gradient_type']));
            update_option('cpd_carousel_gradient_preset', sanitize_text_field($_POST['carousel_gradient_preset']));
            update_option('cpd_carousel_gradient_custom_start', sanitize_hex_color($_POST['carousel_gradient_custom_start']));
            update_option('cpd_carousel_gradient_custom_end', sanitize_hex_color($_POST['carousel_gradient_custom_end']));
            update_option('cpd_carousel_gradient_custom_angle', intval($_POST['carousel_gradient_custom_angle']));
            $message = "✅ Carousel settings saved successfully!";
            break;
            
        case 'cookie':
            update_option('cpd_cookie_consent_text', sanitize_text_field($_POST['cookie_consent_text']));
            update_option('cpd_cookie_consent_button', sanitize_text_field($_POST['cookie_consent_button']));
            $message = "✅ Cookie consent settings saved successfully!";
            break;
    }
    
    // Refresh current settings
    foreach($current_settings as $key => $value) {
        $current_settings[$key] = get_option('cpd_' . $key, $value);
    }
}

// Get all users for editor management
$all_users = get_users(array('role__in' => array('administrator', 'editor', 'author')));
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Complete Admin Panel - Church Programme Dashboard</title>
    <!-- Include jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f0f0f1;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: #1e73be;
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 28px;
        }
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }
        .tabs {
            background: #f6f7f7;
            border-bottom: 1px solid #c3c4c7;
            display: flex;
            flex-wrap: wrap;
        }
        .tab {
            padding: 15px 25px;
            background: transparent;
            border: none;
            border-bottom: 3px solid transparent;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            color: #2c3338;
            transition: all 0.3s ease;
        }
        .tab:hover {
            background: #e2e8f0;
        }
        .tab.active {
            background: white;
            border-bottom-color: #1e73be;
            color: #1e73be;
        }
        .tab-content {
            display: none;
            padding: 30px;
        }
        .tab-content.active {
            display: block;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            font-size: 14px;
        }
        input[type="text"], input[type="url"], input[type="number"], select, textarea {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #8c8f94;
            border-radius: 4px;
            font-size: 14px;
        }
        input[type="color"] {
            width: 60px;
            height: 40px;
            border: 1px solid #8c8f94;
            border-radius: 4px;
        }
        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .checkbox-group input[type="checkbox"] {
            width: auto;
        }
        button {
            background: #2271b1;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        button:hover {
            background: #135e96;
        }
        .message {
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .success {
            background: #d1e7dd;
            border: 1px solid #badbcc;
            color: #0f5132;
        }
        .setting-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 15px;
        }
        @media (max-width: 768px) {
            .setting-row {
                grid-template-columns: 1fr;
            }
        }
        .editor-toolbar {
            background: #f6f7f7;
            padding: 10px;
            border-bottom: 1px solid #c3c4c7;
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
        }
        .editor-toolbar button {
            background: #fff;
            color: #2c3338;
            border: 1px solid #c3c4c7;
            padding: 6px 12px;
            font-size: 14px;
            margin-right: 0;
        }
        .editor-toolbar button:hover {
            background: #f6f7f7;
        }
        .color-picker {
            position: relative;
            display: inline-block;
        }
        .color-picker input[type="color"] {
            position: absolute;
            opacity: 0;
            width: 100%;
            height: 100%;
            cursor: pointer;
        }
        .color-picker label {
            display: inline-block;
            padding: 6px 12px;
            background: #fff;
            border: 1px solid #c3c4c7;
            border-radius: 4px;
            cursor: pointer;
            margin: 0;
            font-size: 14px;
        }
        .color-picker label:hover {
            background: #f6f7f7;
        }
        .users-list {
            background: #f9f9f9;
            border: 1px solid #e2e8f0;
            border-radius: 4px;
            padding: 15px;
            margin-top: 10px;
        }
        .user-item {
            display: flex;
            justify-content: between;
            align-items: center;
            padding: 10px;
            border-bottom: 1px solid #e2e8f0;
        }
        .user-item:last-child {
            border-bottom: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Church Programme Dashboard - Complete Admin Panel</h1>
            <p>Manage all settings for your church programme dashboard</p>
        </div>

        <?php if ($message): ?>
            <div class="message success">
                <?php echo esc_html($message); ?>
            </div>
        <?php endif; ?>

        <div class="tabs">
            <button class="tab <?php echo $active_tab === 'general' ? 'active' : ''; ?>" onclick="switchTab('general')">General</button>
            <button class="tab <?php echo $active_tab === 'subdomain' ? 'active' : ''; ?>" onclick="switchTab('subdomain')">Subdomain</button>
            <button class="tab <?php echo $active_tab === 'colors' ? 'active' : ''; ?>" onclick="switchTab('colors')">Colors & Styling</button>
            <button class="tab <?php echo $active_tab === 'labels' ? 'active' : ''; ?>" onclick="switchTab('labels')">Programme Labels</button>
            <button class="tab <?php echo $active_tab === 'programmes' ? 'active' : ''; ?>" onclick="switchTab('programmes')">Manage Programmes</button>
            <button class="tab <?php echo $active_tab === 'notice' ? 'active' : ''; ?>" onclick="switchTab('notice')">Notice Board</button>
            <button class="tab <?php echo $active_tab === 'carousel' ? 'active' : ''; ?>" onclick="switchTab('carousel')">Carousel</button>
            <button class="tab <?php echo $active_tab === 'cookie' ? 'active' : ''; ?>" onclick="switchTab('cookie')">Cookie Consent</button>
            <button class="tab <?php echo $active_tab === 'links' ? 'active' : ''; ?>" onclick="switchTab('links')">Quick Links</button>
            <button class="tab <?php echo $active_tab === 'users' ? 'active' : ''; ?>" onclick="switchTab('users')">Editor Users</button>
        </div>

        <!-- General Tab -->
        <div class="tab-content <?php echo $active_tab === 'general' ? 'active' : ''; ?>" id="general-tab">
            <h2>General Settings</h2>
            <form method="post">
                <input type="hidden" name="tab" value="general">
                <div class="form-group">
                    <label for="dashboard_title">Dashboard Title:</label>
                    <input type="text" id="dashboard_title" name="dashboard_title" 
                           value="<?php echo esc_attr($current_settings['dashboard_title']); ?>" 
                           placeholder="Enter dashboard title">
                </div>
                <div class="form-group">
                    <label for="header_bg_color">Header Background Color:</label>
                    <input type="color" id="header_bg_color" name="header_bg_color" 
                           value="<?php echo esc_attr($current_settings['header_bg_color']); ?>">
                </div>
                <div class="form-group">
                    <label for="header_bg_image">Header Background Image URL:</label>
                    <input type="url" id="header_bg_image" name="header_bg_image" 
                           value="<?php echo esc_attr($current_settings['header_bg_image']); ?>" 
                           placeholder="https://example.com/image.jpg">
                </div>
                <button type="submit">Save General Settings</button>
            </form>
        </div>

        <!-- Subdomain Tab -->
        <div class="tab-content <?php echo $active_tab === 'subdomain' ? 'active' : ''; ?>" id="subdomain-tab">
            <h2>Subdomain Configuration</h2>
            <form method="post">
                <input type="hidden" name="tab" value="subdomain">
                <div class="form-group">
                    <label for="dashboard_subdomain">Dashboard Subdomain:</label>
                    <input type="text" id="dashboard_subdomain" name="dashboard_subdomain" 
                           value="<?php echo esc_attr($current_settings['dashboard_subdomain']); ?>" 
                           placeholder="dashboard">
                    <p style="margin-top: 5px; color: #666; font-size: 14px;">
                        Will be accessible at: https://<?php echo esc_attr($current_settings['dashboard_subdomain']); ?>.yoursite.com
                    </p>
                </div>
                <div class="form-group">
                    <label for="editor_subdomain">Editor Subdomain:</label>
                    <input type="text" id="editor_subdomain" name="editor_subdomain" 
                           value="<?php echo esc_attr($current_settings['editor_subdomain']); ?>" 
                           placeholder="editor">
                    <p style="margin-top: 5px; color: #666; font-size: 14px;">
                        Will be accessible at: https://<?php echo esc_attr($current_settings['editor_subdomain']); ?>.yoursite.com
                    </p>
                </div>
                <button type="submit">Save Subdomain Settings</button>
            </form>
        </div>

        <!-- Colors Tab -->
        <div class="tab-content <?php echo $active_tab === 'colors' ? 'active' : ''; ?>" id="colors-tab">
            <h2>Colors & Styling</h2>
            <form method="post">
                <input type="hidden" name="tab" value="colors">
                <div class="setting-row">
                    <div class="form-group">
                        <label for="primary_color">Primary Color:</label>
                        <input type="color" id="primary_color" name="primary_color" 
                               value="<?php echo esc_attr($current_settings['primary_color']); ?>">
                    </div>
                    <div class="form-group">
                        <label for="secondary_color">Secondary Color:</label>
                        <input type="color" id="secondary_color" name="secondary_color" 
                               value="<?php echo esc_attr($current_settings['secondary_color']); ?>">
                    </div>
                </div>
                <div class="setting-row">
                    <div class="form-group">
                        <label for="accent_color">Accent Color:</label>
                        <input type="color" id="accent_color" name="accent_color" 
                               value="<?php echo esc_attr($current_settings['accent_color']); ?>">
                    </div>
                    <div class="form-group">
                        <label for="text_color">Text Color:</label>
                        <input type="color" id="text_color" name="text_color" 
                               value="<?php echo esc_attr($current_settings['text_color']); ?>">
                    </div>
                </div>
                <button type="submit">Save Color Settings</button>
            </form>
        </div>

        <!-- Programme Labels Tab -->
        <div class="tab-content <?php echo $active_tab === 'labels' ? 'active' : ''; ?>" id="labels-tab">
            <h2>Programme Labels</h2>
            <form method="post">
                <input type="hidden" name="tab" value="labels">
                <div class="setting-row">
                    <div class="form-group">
                        <label for="label_miet_balang">Miet Balang Label:</label>
                        <input type="text" id="label_miet_balang" name="label_miet_balang" 
                               value="<?php echo esc_attr($current_settings['label_miet_balang']); ?>">
                    </div>
                    <div class="form-group">
                        <label for="label_miet_balang_time">Miet Balang Time:</label>
                        <input type="text" id="label_miet_balang_time" name="label_miet_balang_time" 
                               value="<?php echo esc_attr($current_settings['label_miet_balang_time']); ?>">
                    </div>
                </div>
                <div class="setting-row">
                    <div class="form-group">
                        <label for="label_jingiaseng_samla">Jingiaseng Samla Label:</label>
                        <input type="text" id="label_jingiaseng_samla" name="label_jingiaseng_samla" 
                               value="<?php echo esc_attr($current_settings['label_jingiaseng_samla']); ?>">
                    </div>
                    <div class="form-group">
                        <label for="label_jingiaseng_samla_time">Jingiaseng Samla Time:</label>
                        <input type="text" id="label_jingiaseng_samla_time" name="label_jingiaseng_samla_time" 
                               value="<?php echo esc_attr($current_settings['label_jingiaseng_samla_time']); ?>">
                    </div>
                </div>
                <div class="setting-row">
                    <div class="form-group">
                        <label for="label_jingiaseng_1pm">Jingiaseng 1PM Label:</label>
                        <input type="text" id="label_jingiaseng_1pm" name="label_jingiaseng_1pm" 
                               value="<?php echo esc_attr($current_settings['label_jingiaseng_1pm']); ?>">
                    </div>
                    <div class="form-group">
                        <label for="label_jingiaseng_1pm_time">Jingiaseng 1PM Time:</label>
                        <input type="text" id="label_jingiaseng_1pm_time" name="label_jingiaseng_1pm_time" 
                               value="<?php echo esc_attr($current_settings['label_jingiaseng_1pm_time']); ?>">
                    </div>
                </div>
                <div class="setting-row">
                    <div class="form-group">
                        <label for="label_jingiaseng_khynnah">Jingiaseng Khynnah Label:</label>
                        <input type="text" id="label_jingiaseng_khynnah" name="label_jingiaseng_khynnah" 
                               value="<?php echo esc_attr($current_settings['label_jingiaseng_khynnah']); ?>">
                    </div>
                    <div class="form-group">
                        <label for="label_jingiaseng_khynnah_time">Jingiaseng Khynnah Time:</label>
                        <input type="text" id="label_jingiaseng_khynnah_time" name="label_jingiaseng_khynnah_time" 
                               value="<?php echo esc_attr($current_settings['label_jingiaseng_khynnah_time']); ?>">
                    </div>
                </div>
                <div class="setting-row">
                    <div class="form-group">
                        <label for="label_jingiaseng_iing">Jingiaseng Iing Label:</label>
                        <input type="text" id="label_jingiaseng_iing" name="label_jingiaseng_iing" 
                               value="<?php echo esc_attr($current_settings['label_jingiaseng_iing']); ?>">
                    </div>
                    <div class="form-group">
                        <label for="label_jingiaseng_iing_time">Jingiaseng Iing Time:</label>
                        <input type="text" id="label_jingiaseng_iing_time" name="label_jingiaseng_iing_time" 
                               value="<?php echo esc_attr($current_settings['label_jingiaseng_iing_time']); ?>">
                    </div>
                </div>
                <button type="submit">Save Programme Labels</button>
            </form>
        </div>

        <!-- Manage Programmes Tab -->
        <div class="tab-content <?php echo $active_tab === 'programmes' ? 'active' : ''; ?>" id="programmes-tab">
            <h2>Manage Programmes</h2>
            <p>This feature requires direct database access. Please use the WordPress admin panel or contact your developer for programme management.</p>
            <div style="margin-top: 20px;">
                <a href="<?php echo admin_url('admin.php?page=church-programme-dashboard'); ?>" style="text-decoration: none;">
                    <button type="button">Go to WordPress Admin</button>
                </a>
            </div>
        </div>

        <!-- Notice Board Tab -->
        <div class="tab-content <?php echo $active_tab === 'notice' ? 'active' : ''; ?>" id="notice-tab">
            <h2>Notice Board Settings</h2>
            <form method="post">
                <input type="hidden" name="tab" value="notice">
                <div class="form-group">
                    <div class="checkbox-group">
                        <input type="checkbox" id="notice_board_enabled" name="notice_board_enabled" value="1" 
                               <?php checked($current_settings['notice_board_enabled'], '1'); ?>>
                        <label for="notice_board_enabled" style="display: inline; margin-bottom: 0;">Enable Notice Board</label>
                    </div>
                </div>
                <div class="form-group">
                    <label for="notice_board_title">Notice Board Title:</label>
                    <input type="text" id="notice_board_title" name="notice_board_title" 
                           value="<?php echo esc_attr($current_settings['notice_board_title']); ?>">
                </div>
                <div class="form-group">
                    <label for="notice_board_content">Notice Board Content:</label>
                    <div class="editor-container">
                        <div class="editor-toolbar" id="editor-toolbar">
                            <button type="button" onclick="formatText('bold')" title="Bold"><strong>B</strong></button>
                            <button type="button" onclick="formatText('italic')" title="Italic"><em>I</em></button>
                            <button type="button" onclick="formatText('underline')" title="Underline"><u>U</u></button>
                            <div class="color-picker">
                                <input type="color" id="text-color" onchange="changeTextColor(this.value)" title="Text Color">
                                <label for="text-color">🎨</label>
                            </div>
                            <button type="button" onclick="insertList('ul')" title="Bullet List">• List</button>
                            <button type="button" onclick="insertList('ol')" title="Numbered List">1. List</button>
                            <button type="button" onclick="insertLink()" title="Insert Link">🔗 Link</button>
                            <button type="button" onclick="insertImage()" title="Insert Image">🖼️ Image</button>
                            <button type="button" onclick="toggleView()" title="Toggle HTML/Visual" id="toggle-view">HTML</button>
                        </div>
                        <textarea id="notice_board_content" name="notice_board_content" 
                                  placeholder="Enter your notice board content here..." 
                                  style="min-height: 300px;"><?php echo esc_textarea($current_settings['notice_board_content']); ?></textarea>
                    </div>
                </div>
                <button type="submit">Save Notice Board Settings</button>
            </form>
        </div>

        <!-- Carousel Tab -->
        <div class="tab-content <?php echo $active_tab === 'carousel' ? 'active' : ''; ?>" id="carousel-tab">
            <h2>Carousel Animation Settings</h2>
            <form method="post">
                <input type="hidden" name="tab" value="carousel">
                <div class="form-group">
                    <label for="carousel_animation_type">Animation Type:</label>
                    <select id="carousel_animation_type" name="carousel_animation_type">
                        <option value="slide" <?php selected($current_settings['carousel_animation_type'], 'slide'); ?>>Slide</option>
                        <option value="fade" <?php selected($current_settings['carousel_animation_type'], 'fade'); ?>>Fade</option>
                        <option value="zoom" <?php selected($current_settings['carousel_animation_type'], 'zoom'); ?>>Zoom</option>
                        <option value="flip" <?php selected($current_settings['carousel_animation_type'], 'flip'); ?>>3D Flip</option>
                        <option value="continuous-scroll" <?php selected($current_settings['carousel_animation_type'], 'continuous-scroll'); ?>>Continuous Scroll</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="carousel_animation_speed">Animation Speed (ms):</label>
                    <input type="number" id="carousel_animation_speed" name="carousel_animation_speed" 
                           value="<?php echo esc_attr($current_settings['carousel_animation_speed']); ?>" 
                           min="100" max="2000" step="100">
                    <p style="margin-top: 5px; color: #666; font-size: 14px;">
                        100-2000ms (lower = faster)
                    </p>
                </div>
                <div class="form-group">
                    <label for="carousel_auto_interval">Auto-advance Interval (ms):</label>
                    <input type="number" id="carousel_auto_interval" name="carousel_auto_interval" 
                           value="<?php echo esc_attr($current_settings['carousel_auto_interval']); ?>" 
                           min="1000" max="30000" step="1000">
                    <p style="margin-top: 5px; color: #666; font-size: 14px;">
                        1000-30000ms (1-30 seconds)
                    </p>
                </div>
                <div class="form-group">
                    <label for="carousel_direction">Slide Direction:</label>
                    <select id="carousel_direction" name="carousel_direction">
                        <option value="right-to-left" <?php selected($current_settings['carousel_direction'], 'right-to-left'); ?>>Right to Left</option>
                        <option value="left-to-right" <?php selected($current_settings['carousel_direction'], 'left-to-right'); ?>>Left to Right</option>
                        <option value="top-to-bottom" <?php selected($current_settings['carousel_direction'], 'top-to-bottom'); ?>>Top to Bottom</option>
                        <option value="bottom-to-top" <?php selected($current_settings['carousel_direction'], 'bottom-to-top'); ?>>Bottom to Top</option>
                    </select>
                </div>

                <!-- Gradient Background Settings -->
                <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd;">
                    <h3>Carousel Gradient Background</h3>
                    
                    <div class="form-group">
                        <label for="carousel_gradient_type">Gradient Type:</label>
                        <select id="carousel_gradient_type" name="carousel_gradient_type" onchange="toggleGradientOptions()">
                            <option value="preset" <?php selected($current_settings['carousel_gradient_type'], 'preset'); ?>>Use Preset Gradient</option>
                            <option value="custom" <?php selected($current_settings['carousel_gradient_type'], 'custom'); ?>>Use Custom Gradient</option>
                        </select>
                    </div>

                    <!-- Preset Gradients -->
                    <div id="preset-gradients" class="form-group" style="<?php echo $current_settings['carousel_gradient_type'] === 'custom' ? 'display: none;' : ''; ?>">
                        <label for="carousel_gradient_preset">Select Preset Gradient:</label>
                        <select id="carousel_gradient_preset" name="carousel_gradient_preset">
                            <option value="blue-purple" <?php selected($current_settings['carousel_gradient_preset'], 'blue-purple'); ?>>Blue to Purple</option>
                            <option value="sunset-orange" <?php selected($current_settings['carousel_gradient_preset'], 'sunset-orange'); ?>>Sunset Orange</option>
                            <option value="ocean-blue" <?php selected($current_settings['carousel_gradient_preset'], 'ocean-blue'); ?>>Ocean Blue</option>
                            <option value="forest-green" <?php selected($current_settings['carousel_gradient_preset'], 'forest-green'); ?>>Forest Green</option>
                            <option value="royal-purple" <?php selected($current_settings['carousel_gradient_preset'], 'royal-purple'); ?>>Royal Purple</option>
                            <option value="sunrise-pink" <?php selected($current_settings['carousel_gradient_preset'], 'sunrise-pink'); ?>>Sunrise Pink</option>
                            <option value="midnight-blue" <?php selected($current_settings['carousel_gradient_preset'], 'midnight-blue'); ?>>Midnight Blue</option>
                            <option value="emerald-green" <?php selected($current_settings['carousel_gradient_preset'], 'emerald-green'); ?>>Emerald Green</option>
                            <option value="fiery-red" <?php selected($current_settings['carousel_gradient_preset'], 'fiery-red'); ?>>Fiery Red</option>
                            <option value="golden-yellow" <?php selected($current_settings['carousel_gradient_preset'], 'golden-yellow'); ?>>Golden Yellow</option>
                        </select>
                        
                        <!-- Gradient Preview -->
                        <div id="gradient-preview" style="margin-top: 15px; padding: 20px; border-radius: 8px; text-align: center; color: white; font-weight: bold; min-height: 80px; display: flex; align-items: center; justify-content: center;">
                            Preview will appear here
                        </div>
                    </div>

                    <!-- Custom Gradient -->
                    <div id="custom-gradient" class="form-group" style="<?php echo $current_settings['carousel_gradient_type'] === 'preset' ? 'display: none;' : ''; ?>">
                        <div class="setting-row">
                            <div class="form-group">
                                <label for="carousel_gradient_custom_start">Start Color:</label>
                                <input type="color" id="carousel_gradient_custom_start" name="carousel_gradient_custom_start" 
                                       value="<?php echo esc_attr($current_settings['carousel_gradient_custom_start']); ?>">
                            </div>
                            <div class="form-group">
                                <label for="carousel_gradient_custom_end">End Color:</label>
                                <input type="color" id="carousel_gradient_custom_end" name="carousel_gradient_custom_end" 
                                       value="<?php echo esc_attr($current_settings['carousel_gradient_custom_end']); ?>">
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="carousel_gradient_custom_angle">Gradient Angle (degrees):</label>
                            <input type="number" id="carousel_gradient_custom_angle" name="carousel_gradient_custom_angle" 
                                   value="<?php echo esc_attr($current_settings['carousel_gradient_custom_angle']); ?>" 
                                   min="0" max="360" step="1">
                            <p style="margin-top: 5px; color: #666; font-size: 14px;">
                                0-360 degrees (0 = left to right, 90 = bottom to top, 135 = diagonal)
                            </p>
                        </div>
                    </div>
                </div>

                <button type="submit">Save Carousel Settings</button>
            </form>
        </div>

        <!-- Cookie Consent Tab -->
        <div class="tab-content <?php echo $active_tab === 'cookie' ? 'active' : ''; ?>" id="cookie-tab">
            <h2>Cookie Consent Settings</h2>
            <form method="post">
                <input type="hidden" name="tab" value="cookie">
                <div class="form-group">
                    <label for="cookie_consent_text">Cookie Consent Text:</label>
                    <textarea id="cookie_consent_text" name="cookie_consent_text" rows="3"><?php echo esc_textarea($current_settings['cookie_consent_text']); ?></textarea>
                </div>
                <div class="form-group">
                    <label for="cookie_consent_button">Cookie Consent Button Text:</label>
                    <input type="text" id="cookie_consent_button" name="cookie_consent_button" 
                           value="<?php echo esc_attr($current_settings['cookie_consent_button']); ?>">
                </div>
                <button type="submit">Save Cookie Settings</button>
            </form>
        </div>

        <!-- Quick Links Tab -->
        <div class="tab-content <?php echo $active_tab === 'links' ? 'active' : ''; ?>" id="links-tab">
            <h2>Quick Links</h2>
            <p>Quick links configuration requires direct database access. Please use the WordPress admin panel for this feature.</p>
            <div style="margin-top: 20px;">
                <a href="<?php echo admin_url('admin.php?page=church-programme-dashboard'); ?>" style="text-decoration: none;">
                    <button type="button">Go to WordPress Admin</button>
                </a>
            </div>
        </div>

        <!-- Editor Users Tab -->
        <div class="tab-content <?php echo $active_tab === 'users' ? 'active' : ''; ?>" id="users-tab">
            <h2>Editor Users</h2>
            <p>Current users with editor access:</p>
            <div class="users-list">
                <?php foreach($all_users as $user): ?>
                    <div class="user-item">
                        <div>
                            <strong><?php echo esc_html($user->display_name); ?></strong>
                            <br>
                            <small><?php echo esc_html($user->user_email); ?></small>
                            <br>
                            <small>Role: <?php echo esc_html(implode(', ', $user->roles)); ?></small>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
            <p style="margin-top: 20px; color: #666;">
                To manage users, please use the WordPress Users admin panel.
            </p>
            <div style="margin-top: 20px;">
                <a href="<?php echo admin_url('users.php'); ?>" style="text-decoration: none;">
                    <button type="button">Manage Users in WordPress</button>
                </a>
            </div>
        </div>
    </div>

    <script>
    // Tab switching function
    function switchTab(tabName) {
        // Update URL without page reload
        const url = new URL(window.location);
        url.searchParams.set('tab', tabName);
        window.history.pushState({}, '', url);
        
        // Hide all tab contents
        document.querySelectorAll('.tab-content').forEach(tab => {
            tab.classList.remove('active');
        });
        
        // Show selected tab content
        document.getElementById(tabName + '-tab').classList.add('active');
        
        // Update active tab button
        document.querySelectorAll('.tab').forEach(tab => {
            tab.classList.remove('active');
        });
        event.target.classList.add('active');
    }

    // Text formatting functions for notice board editor
    function formatText(type) {
        const textarea = document.getElementById('notice_board_content');
        const start = textarea.selectionStart;
        const end = textarea.selectionEnd;
        const selectedText = textarea.value.substring(start, end);
        
        let formattedText = '';
        
        switch(type) {
            case 'bold':
                formattedText = `<strong>${selectedText}</strong>`;
                break;
            case 'italic':
                formattedText = `<em>${selectedText}</em>`;
                break;
            case 'underline':
                formattedText = `<u>${selectedText}</u>`;
                break;
        }
        
        textarea.value = textarea.value.substring(0, start) + formattedText + textarea.value.substring(end);
        textarea.focus();
        textarea.setSelectionRange(start + formattedText.length, start + formattedText.length);
    }
    
    function insertList(type) {
        const textarea = document.getElementById('notice_board_content');
        const start = textarea.selectionStart;
        const end = textarea.selectionEnd;
        
        let listText = '';
        if (type === 'ul') {
            listText = '\n• List item 1\n• List item 2\n• List item 3\n';
        } else {
            listText = '\n1. List item 1\n2. List item 2\n3. List item 3\n';
        }
        
        textarea.value = textarea.value.substring(0, start) + listText + textarea.value.substring(end);
        textarea.focus();
        textarea.setSelectionRange(start + listText.length, start + listText.length);
    }
    
    function insertLink() {
        const url = prompt('Enter URL:');
        if (url) {
            const text = prompt('Enter link text (optional):', url);
            const linkText = text || url;
            const link = `<a href="${url}" target="_blank">${linkText}</a>`;
            
            const textarea = document.getElementById('notice_board_content');
            const start = textarea.selectionStart;
            const end = textarea.selectionEnd;
            
            textarea.value = textarea.value.substring(0, start) + link + textarea.value.substring(end);
            textarea.focus();
            textarea.setSelectionRange(start + link.length, start + link.length);
        }
    }
    
    function insertImage() {
        const url = prompt('Enter image URL:');
        if (url) {
            const alt = prompt('Enter alt text (optional):', '');
            const altText = alt ? ` alt="${alt}"` : '';
            const img = `<img src="${url}"${altText} style="max-width: 100%; height: auto;">`;
            
            const textarea = document.getElementById('notice_board_content');
            const start = textarea.selectionStart;
            const end = textarea.selectionEnd;
            
            textarea.value = textarea.value.substring(0, start) + img + textarea.value.substring(end);
            textarea.focus();
            textarea.setSelectionRange(start + img.length, start + img.length);
        }
    }
    
    function changeTextColor(color) {
        const textarea = document.getElementById('notice_board_content');
        const start = textarea.selectionStart;
        const end = textarea.selectionEnd;
        const selectedText = textarea.value.substring(start, end);
        
        if (selectedText) {
            const coloredText = `<span style="color: ${color};">${selectedText}</span>`;
            textarea.value = textarea.value.substring(0, start) + coloredText + textarea.value.substring(end);
            textarea.focus();
            textarea.setSelectionRange(start + coloredText.length, start + coloredText.length);
        }
    }
    
    function toggleView() {
        const textarea = document.getElementById('notice_board_content');
        const toggleBtn = document.getElementById('toggle-view');
        
        if (toggleBtn.textContent === 'HTML') {
            // Convert to visual preview
            let content = textarea.value;
            content = content.replace(/<strong>(.*?)<\/strong>/g, '**$1**');
            content = content.replace(/<em>(.*?)<\/em>/g, '*$1*');
            content = content.replace(/<u>(.*?)<\/u>/g, '_$1_');
            content = content.replace(/<span style="color: (.*?);">(.*?)<\/span>/g, '[$2]($1)');
            content = content.replace(/<a href="(.*?)".*?>(.*?)<\/a>/g, '[$2]($1)');
            content = content.replace(/<img src="(.*?)".*?>/g, '![Image]($1)');
            content = content.replace(/<br\s*\/?>/g, '\n');
            content = content.replace(/<p>(.*?)<\/p>/g, '$1\n\n');
            content = content.replace(/<ul>(.*?)<\/ul>/gs, '$1');
            content = content.replace(/<ol>(.*?)<\/ol>/gs, '$1');
            content = content.replace(/<li>(.*?)<\/li>/g, '• $1\n');
            
            textarea.value = content;
            toggleBtn.textContent = 'Visual';
        } else {
            // Convert back to HTML
            let content = textarea.value;
            content = content.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
            content = content.replace(/\*(.*?)\*/g, '<em>$1</em>');
            content = content.replace(/_(.*?)_/g, '<u>$1</u>');
            content = content.replace(/\[(.*?)\]\((.*?)\)/g, function(match, text, url) {
                // Check if it's a color or a link
                if (url.startsWith('#')) {
                    return `<span style="color: ${url};">${text}</span>`;
                } else {
                    return `<a href="${url}" target="_blank">${text}</a>`;
                }
            });
            content = content.replace(/!\[(.*?)\]\((.*?)\)/g, '<img src="$2" alt="$1" style="max-width: 100%; height: auto;">');
            content = content.replace(/\n/g, '<br>');
            content = content.replace(/(•\s.*?)(?=\n|$)/g, '<li>$1</li>');
            
            textarea.value = content;
            toggleBtn.textContent = 'HTML';
        }
    }

    // Auto-resize textarea
    document.addEventListener('DOMContentLoaded', function() {
        const textarea = document.getElementById('notice_board_content');
        if (textarea) {
            textarea.style.minHeight = '300px';
        }
        
        // Initialize gradient preview
        updateGradientPreview();
        
        // Add event listeners for gradient changes
        document.getElementById('carousel_gradient_preset').addEventListener('change', updateGradientPreview);
        document.getElementById('carousel_gradient_custom_start').addEventListener('input', updateGradientPreview);
        document.getElementById('carousel_gradient_custom_end').addEventListener('input', updateGradientPreview);
        document.getElementById('carousel_gradient_custom_angle').addEventListener('input', updateGradientPreview);
    });

    // Toggle between preset and custom gradient options
    function toggleGradientOptions() {
        const gradientType = document.getElementById('carousel_gradient_type').value;
        const presetSection = document.getElementById('preset-gradients');
        const customSection = document.getElementById('custom-gradient');
        
        if (gradientType === 'preset') {
            presetSection.style.display = 'block';
            customSection.style.display = 'none';
        } else {
            presetSection.style.display = 'none';
            customSection.style.display = 'block';
        }
        
        updateGradientPreview();
    }

    // Update gradient preview
    function updateGradientPreview() {
        const gradientType = document.getElementById('carousel_gradient_type').value;
        const preview = document.getElementById('gradient-preview');
        
        let gradientCSS = '';
        let gradientName = '';
        
        if (gradientType === 'preset') {
            const preset = document.getElementById('carousel_gradient_preset').value;
            gradientCSS = getPresetGradient(preset);
            gradientName = getPresetName(preset);
        } else {
            const startColor = document.getElementById('carousel_gradient_custom_start').value;
            const endColor = document.getElementById('carousel_gradient_custom_end').value;
            const angle = document.getElementById('carousel_gradient_custom_angle').value;
            gradientCSS = `linear-gradient(${angle}deg, ${startColor}, ${endColor})`;
            gradientName = 'Custom Gradient';
        }
        
        preview.style.background = gradientCSS;
        preview.textContent = gradientName;
    }

    // Get preset gradient CSS
    function getPresetGradient(preset) {
        const gradients = {
            'blue-purple': 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            'sunset-orange': 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
            'ocean-blue': 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
            'forest-green': 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
            'royal-purple': 'linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%)',
            'sunrise-pink': 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
            'midnight-blue': 'linear-gradient(135deg, #4c6ef5 0%, #3b5bdb 100%)',
            'emerald-green': 'linear-gradient(135deg, #0ba360 0%, #3cba92 100%)',
            'fiery-red': 'linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%)',
            'golden-yellow': 'linear-gradient(135deg, #f9d423 0%, #ff4e50 100%)'
        };
        return gradients[preset] || gradients['blue-purple'];
    }

    // Get preset gradient name
    function getPresetName(preset) {
        const names = {
            'blue-purple': 'Blue to Purple',
            'sunset-orange': 'Sunset Orange',
            'ocean-blue': 'Ocean Blue',
            'forest-green': 'Forest Green',
            'royal-purple': 'Royal Purple',
            'sunrise-pink': 'Sunrise Pink',
            'midnight-blue': 'Midnight Blue',
            'emerald-green': 'Emerald Green',
            'fiery-red': 'Fiery Red',
            'golden-yellow': 'Golden Yellow'
        };
        return names[preset] || 'Blue to Purple';
    }
    </script>

    <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; text-align: center;">
        <p><strong>Preview:</strong> After updating settings, visit your dashboard to see the changes.</p>
        <div style="margin-top: 20px;">
            <a href="<?php echo CPD_Subdomain::get_dashboard_url(); ?>" target="_blank" style="text-decoration: none;">
                <button type="button">View Dashboard</button>
            </a>
            <a href="<?php echo CPD_Subdomain::get_editor_url(); ?>" target="_blank" style="text-decoration: none;">
                <button type="button">View Editor</button>
            </a>
        </div>
    </div>
</body>
</html>
