# CRITICAL: Admin Save Not Working - Advanced Debugging

## Current Status
- ✅ <PERSON><PERSON><PERSON> handler works (test confirmed)
- ✅ Database writes work
- ❌ CPD_Admin class NOT loading
- ❌ CPD_Admin_Settings class NOT loading
- ❌ cpdAdmin JavaScript object MISSING
- ❌ Save button not responding

## Root Cause
The admin classes are not being loaded, which means scripts are not being enqueued.

## IMMEDIATE ACTION REQUIRED

### Step 1: Enable Debug Mode (REQUIRED)

Add to `wp-config.php` (before "That's all, stop editing!"):

```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
define('WP_DEBUG_DISPLAY', false);
define('SCRIPT_DEBUG', true);
```

### Step 2: Access the NEW Debug Page

I've created a debug page that runs INSIDE WordPress admin:

1. **Go to:** WordPress Admin > Church Programme > **🔍 Debug**
   
   (If you don't see it, WP_DEBUG is not enabled)

2. **This page will show:**
   - ✅ Whether classes are loaded
   - ✅ Whether hooks are registered
   - ✅ Whether scripts are enqueued
   - ✅ Live JavaScript test
   - ✅ Live AJAX test

3. **Take a screenshot of ALL sections** and share it

### Step 3: Check Debug Log

After visiting the debug page, check:
```
wp-content/debug.log
```

Look for lines containing:
- "CPD Admin: enqueue_scripts called"
- "CPD Admin: Scripts will be enqueued"
- Any PHP errors or warnings

### Step 4: Check Browser Console on Settings Page

1. Go to: WordPress Admin > Church Programme > Settings
2. Press F12
3. Go to Console tab
4. Type: `console.log(cpdAdmin)`
5. Take a screenshot of the result

### Step 5: Check Network Tab

1. Stay on Settings page with F12 open
2. Go to Network tab
3. Click "Save All Settings"
4. Look for ANY network requests
5. Take a screenshot

## Possible Issues & Solutions

### Issue A: Classes Not Loading Because is_admin() is False

**Check in debug page:** Look at "Test 1: WordPress Context"

**If is_admin() shows false:**

Edit `church-programme-dashboard.php` line 80-83:

**Change from:**
```php
// Admin classes
if (is_admin()) {
    require_once CPD_PLUGIN_DIR . 'admin/class-cpd-admin.php';
    require_once CPD_PLUGIN_DIR . 'admin/class-cpd-admin-settings.php';
}
```

**To:**
```php
// Admin classes - Always load to ensure availability
require_once CPD_PLUGIN_DIR . 'admin/class-cpd-admin.php';
require_once CPD_PLUGIN_DIR . 'admin/class-cpd-admin-settings.php';
```

### Issue B: Scripts Not Enqueuing Due to Hook Mismatch

**Check in debug page:** Look at "Test 4: Enqueued Scripts & Styles"

**If scripts show "Not registered":**

The hook check is failing. Check "Test 1" for the actual hook name.

Then edit `admin/class-cpd-admin.php` line 78-91 to match your hook.

### Issue C: JavaScript File Not Found

**Check in browser console:** Look for 404 errors

**If you see 404 for admin-script.js:**

Verify file exists at:
```
church-programme-dashboard/admin/js/admin-script.js
```

### Issue D: jQuery Conflict

**Check in debug page:** JavaScript test should show jQuery available

**If jQuery is missing:**

Another plugin might be removing it. Try disabling other plugins.

### Issue E: Form ID Mismatch

**Check in browser console on settings page:**

```javascript
jQuery('#cpd-settings-form').length
```

Should return 1. If it returns 0, the form ID is wrong.

## Manual Override Test

If nothing works, try this manual test:

1. **Go to Settings page**
2. **Open browser console (F12)**
3. **Paste this code:**

```javascript
// Manual script injection
var script = document.createElement('script');
script.textContent = `
    window.cpdAdmin = {
        ajaxUrl: '<?php echo admin_url("admin-ajax.php"); ?>',
        nonce: '<?php echo wp_create_nonce("cpd_ajax_nonce"); ?>'
    };
    
    jQuery(document).ready(function($) {
        console.log('Manual script loaded');
        console.log('cpdAdmin:', cpdAdmin);
        
        $('#cpd-settings-form').on('submit', function(e) {
            e.preventDefault();
            console.log('Form submitted!');
            
            var formData = {};
            $(this).find('input, textarea, select').each(function() {
                var input = $(this);
                var name = input.attr('name');
                if (name && name !== 'cpd_settings_nonce' && name !== '_wp_http_referer') {
                    if (input.attr('type') === 'checkbox') {
                        formData[name] = input.is(':checked') ? '1' : '0';
                    } else {
                        formData[name] = input.val();
                    }
                }
            });
            
            console.log('Sending:', formData);
            
            $.ajax({
                url: cpdAdmin.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'cpd_save_settings',
                    nonce: cpdAdmin.nonce,
                    settings: formData
                },
                success: function(response) {
                    console.log('Success:', response);
                    alert('Settings saved!');
                },
                error: function(xhr, status, error) {
                    console.error('Error:', error);
                    alert('Error: ' + error);
                }
            });
        });
    });
`;
document.head.appendChild(script);
```

4. **Try clicking "Save All Settings"**
5. **Check console for messages**

If this works, it confirms the issue is with script loading, not the AJAX handler.

## What to Report Back

Please provide:

1. **Screenshot of Debug page** (all sections)
2. **Contents of debug.log** (last 50 lines)
3. **Screenshot of browser console** on settings page
4. **Screenshot of Network tab** when clicking save
5. **Result of manual override test** (did it work?)

## Files Modified

1. `admin/class-cpd-admin.php` - Added debug page menu item
2. `admin/views/debug-page.php` - NEW comprehensive debug page

## Next Steps

1. Enable WP_DEBUG
2. Visit the Debug page (Church Programme > Debug)
3. Take screenshots
4. Check debug.log
5. Report findings

The debug page will tell us EXACTLY what's not loading and why.

