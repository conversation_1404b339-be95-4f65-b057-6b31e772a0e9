# Quick Start Guide - Church Programme Dashboard

Get your church programme dashboard up and running in 10 minutes!

## 1. Install Plugin (2 minutes)

1. Upload `church-programme-dashboard` folder to `wp-content/plugins/`
2. Go to WordPress Admin > Plugins
3. Click **Activate** on "Church Programme Dashboard"
4. Done! Plugin is now active.

## 2. Basic Configuration (3 minutes)

### Access Settings
Go to **Church Programme** > **Settings** in WordPress admin

### Set Dashboard Title
- Enter your church name or "Church Programme"
- This appears at the top of your dashboard

### Configure Subdomains
- Go to **Subdomains** tab
- Set dashboard subdomain (e.g., "churchprogramme")
- Set editor subdomain (e.g., "churcheditor")
- Your dashboard URL: `churchprogramme.yoursite.com`
- Your editor URL: `churcheditor.yoursite.com`
- **Important:** You must configure DNS records (see SUBDOMAIN_SETUP.md)

### Choose Colors (Optional)
- Go to **Colors & Styling** tab
- Pick colors that match your church branding
- Or keep the defaults (they look great!)

### Click "Save All Settings"

## 3. Create Editor User (1 minute)

### Add Your First User
Go to **Church Programme** > **Editor Users**

1. Enter a username (e.g., "admin" or your name)
2. Click "Generate Strong Password" button
3. **IMPORTANT:** Copy the password immediately!
4. Enter your email (optional but recommended)
5. Click **Add User**

## 4. Test Your Dashboard (1 minute)

### View Public Dashboard
1. Click **View Dashboard** in admin menu
2. Or visit: `yoursite.com/programme/`
3. You should see:
   - Header with your title
   - Empty carousel (no programmes yet)
   - Calendar for current month
   - Footer with attribution

### Access Editor
1. Visit: `yoursite.com/programme/editor/`
2. Login with the username and password you created
3. You should see the editor interface

## 5. Add Your First Programme (3 minutes)

### Option A: Using AI (Recommended if you have images)

1. In the editor, go to **AI Extraction** tab
2. Select AI provider (OpenRouter recommended)
3. Enter your API key
4. Click **Fetch Available Models**
5. Select a vision model (e.g., GPT-4 Vision)
6. Scroll down to programme type
7. Upload your programme image
8. Click **Extract Data**
9. Wait 10-30 seconds
10. Data is automatically saved!

### Option B: Manual Entry (If no AI or images)

1. In the editor, go to **Manual Entry** tab
2. Select programme type
3. Enter date (e.g., 2025-10-05)
4. Enter time (e.g., 18:30 for 6:30 PM)
5. Fill in participant names
6. Click **Save Programme**

**Note:** Manual entry form needs JavaScript completion (see PROJECT_SUMMARY.md)

### Option C: Direct Database Entry (Quick Test)

Use this SQL to add a test programme:

```sql
INSERT INTO wp_cpd_programmes (programme_type, programme_date, programme_time, programme_data)
VALUES (
    'miet_balang',
    '2025-10-05',
    '18:30',
    '{"pule_sdang_duwai":"John Doe","nongkren":"Jane Smith","khubor":"Bob Johnson"}'
);
```

## 6. View Your Programme (1 minute)

1. Go back to your dashboard: `yoursite.com/programme/`
2. You should now see:
   - Programme in the carousel (if it's upcoming)
   - Date marked on calendar
3. Click the marked date on calendar
4. Modal popup shows programme details!

## That's It! 🎉

Your church programme dashboard is now live and working!

---

## Next Steps

### Customize Appearance
- Change colors to match your church branding
- Upload a header background image
- Customize programme labels

### Add More Programmes
- Extract from your programme images using AI
- Or add manually through the editor
- Build up your programme calendar

### Enable Notice Board
- Go to Settings > Notice Board
- Enable it and add important announcements
- Shows between carousel and calendar

### Share With Congregation
- Share the dashboard URL: `yoursite.com/programme/`
- No login required for viewing
- Works great on mobile phones!

### Add More Editor Users
- Go to Editor Users page
- Add users for other church staff
- Each gets their own login

---

## Quick Reference

### URLs
- **Dashboard:** `yoursite.com/programme/`
- **Editor:** `yoursite.com/programme/editor/`
- **Admin Settings:** WordPress Admin > Church Programme > Settings
- **User Management:** WordPress Admin > Church Programme > Editor Users

### Programme Types
1. **JINGIASENG RANGBAH** - Sunday 1:00 PM & 6:30 PM
2. **JINGIASENG IING** - Two zones, 6:30 PM
3. **JINGIASENG SAMLA** - Friday 6:30 PM
4. **JINGIASENG KHYNNAH** - Sunday 3:00 PM

### Default Times
- JINGIASENG 1:00 Baje: 1:00 PM
- MIET BALANG: 6:30 PM
- JINGIASENG SAMLA: 6:30 PM
- JINGIASENG KHYNNAH: 3:00 PM
- JINGIASENG IING: 6:30 PM

### AI Providers
- **OpenRouter:** https://openrouter.ai (Recommended)
- **Google Gemini:** https://makersuite.google.com/app/apikey
- **DeepSeek:** https://platform.deepseek.com

### Recommended AI Models
- GPT-4 Vision (OpenRouter)
- Claude 3 Opus/Sonnet (OpenRouter)
- Gemini Pro Vision (Google)
- Gemini 1.5 Pro (Google)
- DeepSeek VL (DeepSeek)

---

## Troubleshooting

### Dashboard shows 404
**Fix:** Go to Settings > Permalinks, click Save Changes

### Can't login to editor
**Fix:** Verify username/password, clear browser cookies

### AI extraction fails
**Fix:** Check API key, ensure model supports vision, try smaller image

### Calendar not loading
**Fix:** Check browser console (F12) for errors

### Programmes not showing
**Fix:** Verify programme date is in the future, check database

---

## Support

**Documentation:**
- README.md - Full feature list
- INSTALLATION.md - Detailed setup guide
- PROJECT_SUMMARY.md - Technical details

**Website:** https://www.jermesa.com

**Privacy Policy:** https://jermesa.com/privacy-policy/

---

## Tips for Success

### 1. Start Simple
- Add just one programme to test
- Verify it displays correctly
- Then add more programmes

### 2. Use Good Images
- Clear, high-resolution photos
- Good lighting
- Straight, not angled
- Text should be readable

### 3. Test on Mobile
- Most users will view on phones
- Dashboard is mobile-optimized
- Test on your phone before sharing

### 4. Keep API Key Safe
- Don't share your API key
- It's stored in browser only
- Each editor user needs their own

### 5. Regular Updates
- Update programmes every 3 months
- Use AI extraction to save time
- Keep notice board current

### 6. Share Widely
- Add link to church website
- Share in WhatsApp groups
- Post on social media
- Print QR code for bulletin

---

## Sample Programme Data

For testing, here's sample data for each type:

### MIET BALANG
```json
{
  "pule_sdang_duwai": "John Doe & Jane Smith",
  "nongkren": "Youth Group",
  "khubor": "Pastor David"
}
```

### JINGIASENG SAMLA
```json
{
  "pulesdang_duwai": "Mary Johnson",
  "jingainguh": "Choir",
  "special_no": "Hymn 123",
  "nongkren": "Sunday School"
}
```

### JINGIASENG 1:00 Baje
```json
{
  "nongiathuh_khana_por": "Elder Peter & Elder Paul"
}
```

### JINGIASENG KHYNNAH
```json
{
  "jingrwai_iaroh": "Brother James",
  "nongpule_sdang_old_testament": "Sister Mary",
  "nongpule_sdang_new_testament": "Brother John",
  "nong_duwai": "Youth Leader",
  "lum_jingainguh": "Worship Team",
  "jingrwai_kyrpang": "Sister Grace",
  "duwai_jingainguh": "Children's Ministry",
  "nongkren_activities": "Fellowship Lunch"
}
```

### JINGIASENG IING
```json
{
  "zone_1": {
    "ing": "Brother Tom",
    "nongpule_duwai": "Sister Lisa & Brother Mike",
    "nongkren": "Zone 1 Youth"
  },
  "zone_2": {
    "ing": "Brother Sam",
    "nongpule_duwai": "Sister Anna & Brother Luke",
    "nongkren": "Zone 2 Youth"
  }
}
```

---

**Ready to go? Start with Step 1!**

Need help? Visit https://www.jermesa.com

