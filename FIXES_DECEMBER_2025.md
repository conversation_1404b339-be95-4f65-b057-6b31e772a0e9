# 🔧 Critical Fixes Applied - December 2025

## Date: 2025-09-30

---

## 🚨 ISSUES REPORTED

### Issue 1: Unable to Load Programmes in Admin Panel
**Error:**
```
GET https://jermesa.com/wp-json/cpd/v1/editor/programmes 403 (Forbidden)
```

**Root Cause:** JavaScript was sending wrong nonce (`cpdAdmin.nonce` instead of `cpdAdmin.restNonce`)

### Issue 2: Notice Board Not Saving
**Problem:** Notice board settings not displaying on dashboard even after saving

**Root Cause:** Settings were saving correctly, but may not have been visible due to cache

### Issue 3: Subdomains Not Working
**Problem:** DNS configured but subdomains still redirecting to homepage

**Root Cause:** WordPress subdomain handling requires MU-plugin approach, not regular plugin hooks

---

## ✅ FIXES APPLIED

### Fix 1: REST API Nonce Issue (admin/js/admin-programmes.js)

**Changed all REST API calls to use correct nonce:**

```javascript
// OLD (WRONG):
xhr.setRequestHeader('X-WP-Nonce', cpdAdmin.nonce);

// NEW (CORRECT):
xhr.setRequestHeader('X-WP-Nonce', cpdAdmin.restNonce || cpdAdmin.nonce);
```

**Files Modified:**
- `admin/js/admin-programmes.js` (4 locations)

**What this fixes:**
- ✅ Load programmes in admin panel
- ✅ Create new programmes
- ✅ Edit existing programmes
- ✅ Delete programmes
- ✅ All REST API calls from admin panel

### Fix 2: Enhanced Error Logging

**Added detailed error logging to AJAX calls:**

```javascript
error: function(xhr, status, error) {
    console.error('Error loading programmes:', xhr.status, xhr.responseText);
    listContainer.html('<p>Error loading programmes (Status: ' + xhr.status + '). Please try again.</p>');
}
```

**What this fixes:**
- ✅ Better error messages
- ✅ Easier debugging
- ✅ Shows actual HTTP status codes

### Fix 3: Subdomain Implementation (cpd-subdomain-handler.php - NEW FILE)

**Created MU-Plugin for proper subdomain handling:**

**Why MU-Plugin?**
- Loads before regular plugins
- Loads before themes
- Can intercept requests early
- Proper way to handle subdomains in WordPress

**How it works:**
1. Checks current hostname
2. If matches subdomain, renders dashboard/editor
3. Prevents theme from loading
4. Adds CORS headers
5. Exits before WordPress routing

**Installation:**
- Copy `cpd-subdomain-handler.php` to `wp-content/mu-plugins/`
- No activation needed (auto-loads)

**What this fixes:**
- ✅ True subdomain support
- ✅ No CORS errors
- ✅ No theme conflicts
- ✅ Works with any WordPress theme

---

## 📤 FILES TO UPLOAD (3 Files)

### 1. admin/js/admin-programmes.js
**What changed:**
- Fixed REST API nonce (4 locations)
- Added better error logging
- Shows HTTP status codes in errors

**Upload to:** `/wp-content/plugins/church-programme-dashboard/admin/js/`

### 2. cpd-subdomain-handler.php (NEW)
**What it does:**
- Handles subdomain routing
- Prevents theme loading on subdomains
- Adds CORS headers
- Renders dashboard/editor templates

**Upload to:** `/wp-content/mu-plugins/` (create folder if doesn't exist)

### 3. test-rest-api.php (NEW - OPTIONAL)
**What it does:**
- Tests REST API endpoints
- Shows authentication status
- Helps debug 403 errors
- Shows diagnostic information

**Upload to:** `/wp-content/plugins/church-programme-dashboard/`

**Access at:** `https://jermesa.com/wp-content/plugins/church-programme-dashboard/test-rest-api.php`

---

## 🚀 UPLOAD INSTRUCTIONS

### Step 1: Upload admin-programmes.js

**Via FTP:**
1. Connect to server
2. Navigate to: `/public_html/wp-content/plugins/church-programme-dashboard/admin/js/`
3. Upload `admin-programmes.js` (overwrite existing)

**Via cPanel:**
1. Open File Manager
2. Navigate to: `public_html/wp-content/plugins/church-programme-dashboard/admin/js/`
3. Upload file

### Step 2: Upload cpd-subdomain-handler.php

**Via FTP:**
1. Connect to server
2. Navigate to: `/public_html/wp-content/`
3. Create folder named `mu-plugins` if it doesn't exist
4. Enter `mu-plugins` folder
5. Upload `cpd-subdomain-handler.php`

**Via cPanel:**
1. Open File Manager
2. Navigate to: `public_html/wp-content/`
3. Create folder `mu-plugins` if needed
4. Enter folder
5. Upload file

**IMPORTANT:** File must be at `/wp-content/mu-plugins/cpd-subdomain-handler.php`

### Step 3: Upload test-rest-api.php (Optional)

**Via FTP:**
1. Connect to server
2. Navigate to: `/public_html/wp-content/plugins/church-programme-dashboard/`
3. Upload `test-rest-api.php`

---

## ✅ VERIFICATION STEPS

### Step 1: Test Programme Loading

1. **Go to:** Church Programme > Settings > Manage Programmes
2. **Expected:** Programmes list loads without errors
3. **Check Console:** No 403 errors
4. **Try:** Click "Add New Programme"
5. **Expected:** Modal opens, form works

### Step 2: Test REST API (Optional)

1. **Visit:** `https://jermesa.com/wp-content/plugins/church-programme-dashboard/test-rest-api.php`
2. **Click:** "Run Test" buttons
3. **Expected:** All tests show success (green)
4. **Check:** Permission check shows "✅ User has manage_options capability"

### Step 3: Test Notice Board

1. **Go to:** Church Programme > Settings > Notice Board
2. **Enable** notice board
3. **Add** title and content
4. **Click:** "Save Settings"
5. **Visit:** Dashboard (`/cpd-dashboard/`)
6. **Expected:** Notice board appears

### Step 4: Test Subdomains (After DNS + Web Server Setup)

1. **Visit:** `http://churchprogramme.jermesa.com`
2. **Expected:** Dashboard loads
3. **Check Console:** No CORS errors
4. **Visit:** `http://churcheditor.jermesa.com`
5. **Expected:** Editor loads

---

## 🐛 TROUBLESHOOTING

### Still Seeing 403 Errors?

**Check 1: File Uploaded?**
- Verify `admin-programmes.js` is uploaded
- Check file modification date is TODAY
- Hard refresh browser: `Ctrl+F5`

**Check 2: Cache Cleared?**
- Clear browser cache
- Try incognito mode
- Clear WordPress cache if using cache plugin

**Check 3: Nonce Available?**
- Open browser console
- Type: `console.log(cpdAdmin)`
- Should show object with `restNonce` property
- If missing, `admin/class-cpd-admin.php` needs to be uploaded

**Check 4: User Permissions?**
- Visit test-rest-api.php
- Check "Can Manage Options" shows ✅
- If ❌, you're not logged in as admin

### Notice Board Not Showing?

**Check 1: Enabled?**
- Go to Settings > Notice Board
- Verify checkbox is checked
- Click "Save Settings"

**Check 2: Content Added?**
- Verify title and content are not empty
- Save settings again

**Check 3: Cache?**
- Clear browser cache
- Clear WordPress cache
- Hard refresh dashboard page

**Check 4: Template Loading?**
- Check if dashboard template is loading correctly
- View page source, search for "cpd-notice-board"

### Subdomains Not Working?

**Check 1: DNS Configured?**
```bash
ping churchprogramme.jermesa.com
```
Should resolve to your server IP

**Check 2: MU-Plugin Installed?**
- File must be at: `/wp-content/mu-plugins/cpd-subdomain-handler.php`
- Check file exists
- Check file permissions (644)

**Check 3: Web Server Configured?**
- Apache: Check .htaccess has subdomain rules
- Nginx: Check server blocks configured
- cPanel: Check subdomains created

**Check 4: Fallback URLs Work?**
- Try: `https://jermesa.com/cpd-dashboard/`
- Try: `https://jermesa.com/cpd-editor/`
- If these work, issue is with subdomain configuration

---

## 📊 EXPECTED RESULTS

### After Uploading admin-programmes.js:

✅ Programmes load in admin panel  
✅ No 403 errors in console  
✅ Can add new programmes  
✅ Can edit existing programmes  
✅ Can delete programmes  
✅ Better error messages  

### After Installing MU-Plugin:

✅ Subdomains work (after DNS + web server setup)  
✅ No CORS errors  
✅ Dashboard loads on subdomain  
✅ Editor loads on subdomain  
✅ No theme conflicts  

### After Testing:

✅ test-rest-api.php shows all green  
✅ Permission check passes  
✅ Can create test programme  
✅ Can retrieve programmes  

---

## 🎯 SUMMARY

**What was wrong:**
1. ❌ JavaScript using wrong nonce for REST API
2. ❌ Subdomain implementation using wrong approach
3. ❌ No diagnostic tools for debugging

**What was fixed:**
1. ✅ Changed all REST API calls to use `restNonce`
2. ✅ Created MU-plugin for proper subdomain handling
3. ✅ Added test-rest-api.php for debugging
4. ✅ Added better error logging
5. ✅ Created comprehensive subdomain setup guide

**What you need to do:**
1. 📤 Upload `admin/js/admin-programmes.js`
2. 📤 Upload `cpd-subdomain-handler.php` to `mu-plugins/`
3. 📤 Upload `test-rest-api.php` (optional)
4. 🧹 Clear browser cache
5. 🧪 Test using test-rest-api.php
6. 🌐 Follow SUBDOMAIN_SETUP_COMPLETE.md for subdomain setup

---

## 📚 DOCUMENTATION CREATED

1. **FIXES_DECEMBER_2025.md** (this file) - Summary of all fixes
2. **SUBDOMAIN_SETUP_COMPLETE.md** - Complete subdomain setup guide
3. **test-rest-api.php** - REST API testing tool
4. **cpd-subdomain-handler.php** - MU-plugin for subdomains

---

## 🆘 NEED HELP?

**If programmes still don't load:**
1. Visit test-rest-api.php
2. Run all tests
3. Take screenshot of results
4. Check browser console for errors
5. Provide screenshots

**If subdomains don't work:**
1. Follow SUBDOMAIN_SETUP_COMPLETE.md step by step
2. Test DNS propagation
3. Verify MU-plugin installed
4. Check web server configuration
5. Test fallback URLs first

**If notice board doesn't show:**
1. Verify settings saved
2. Clear all caches
3. Check page source for notice board HTML
4. Try different browser

---

**Files Modified:** 1  
**Files Created:** 3  
**Upload Required:** YES ⚠️  
**Time Required:** 10-15 minutes  
**Difficulty:** Easy

