<?php
/**
 * Direct Carousel Settings Updater
 * 
 * Use this script to directly update ALL carousel settings in the database
 * when the admin panel save functionality is not working.
 * 
 * Instructions:
 * 1. Upload this file to your WordPress root directory (same level as wp-config.php)
 * 2. Access it via browser: https://yoursite.com/update-all-carousel-settings.php
 * 3. Select your desired settings and click "Update Settings"
 * 4. Delete this file after use for security
 */

// Security check
if (!defined('ABSPATH')) {
    // Load WordPress
    require_once('wp-load.php');
}

// Check if user is logged in and has admin permissions
if (!current_user_can('manage_options')) {
    die('You do not have permission to access this page.');
}

$message = '';
$current_settings = array(
    'animation_type' => get_option('cpd_carousel_animation_type', 'slide'),
    'animation_speed' => get_option('cpd_carousel_animation_speed', '500'),
    'auto_interval' => get_option('cpd_carousel_auto_interval', '5000'),
    'direction' => get_option('cpd_carousel_direction', 'right-to-left')
);

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_settings'])) {
    $new_settings = array(
        'animation_type' => sanitize_text_field($_POST['animation_type']),
        'animation_speed' => intval($_POST['animation_speed']),
        'auto_interval' => intval($_POST['auto_interval']),
        'direction' => sanitize_text_field($_POST['direction'])
    );
    
    // Validate settings
    $valid = true;
    $errors = array();
    
    if ($new_settings['animation_speed'] < 100 || $new_settings['animation_speed'] > 2000) {
        $valid = false;
        $errors[] = "Animation speed must be between 100 and 2000ms";
    }
    
    if ($new_settings['auto_interval'] < 1000 || $new_settings['auto_interval'] > 30000) {
        $valid = false;
        $errors[] = "Auto-interval must be between 1000 and 30000ms";
    }
    
    if ($valid) {
        // Update all settings
        update_option('cpd_carousel_animation_type', $new_settings['animation_type']);
        update_option('cpd_carousel_animation_speed', $new_settings['animation_speed']);
        update_option('cpd_carousel_auto_interval', $new_settings['auto_interval']);
        update_option('cpd_carousel_direction', $new_settings['direction']);
        
        $current_settings = $new_settings;
        $message = "✅ Success! All carousel settings have been updated.";
    } else {
        $message = "❌ Error: " . implode(", ", $errors);
    }
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Update Carousel Settings</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background: #f0f0f1;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #1d2327;
            margin-bottom: 20px;
        }
        .info-box {
            background: #f6f7f7;
            border-left: 4px solid #72aee6;
            padding: 20px;
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
        }
        select, input[type="number"] {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #8c8f94;
            border-radius: 4px;
            font-size: 16px;
        }
        button {
            background: #2271b1;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background: #135e96;
        }
        .message {
            padding: 10px 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .success {
            background: #d1e7dd;
            border: 1px solid #badbcc;
            color: #0f5132;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f1aeb5;
            color: #721c24;
        }
        .current-settings {
            background: #fff3cd;
            border-left: 4px solid #ffc107;
            padding: 15px;
            margin-bottom: 20px;
        }
        .setting-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 15px;
        }
        @media (max-width: 600px) {
            .setting-row {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Update Carousel Settings</h1>
        
        <div class="info-box">
            <h3>Current Settings:</h3>
            <div class="setting-row">
                <div><strong>Animation Type:</strong> <?php echo esc_html($current_settings['animation_type']); ?></div>
                <div><strong>Animation Speed:</strong> <?php echo esc_html($current_settings['animation_speed']); ?>ms</div>
                <div><strong>Auto-interval:</strong> <?php echo esc_html($current_settings['auto_interval']); ?>ms</div>
                <div><strong>Direction:</strong> <?php echo esc_html($current_settings['direction']); ?></div>
            </div>
        </div>

        <?php if ($message): ?>
            <div class="message <?php echo strpos($message, '✅') !== false ? 'success' : 'error'; ?>">
                <?php echo esc_html($message); ?>
            </div>
        <?php endif; ?>

        <form method="post">
            <div class="setting-row">
                <div class="form-group">
                    <label for="animation_type">Animation Type:</label>
                    <select id="animation_type" name="animation_type" required>
                        <option value="slide" <?php selected($current_settings['animation_type'], 'slide'); ?>>Slide (Default)</option>
                        <option value="fade" <?php selected($current_settings['animation_type'], 'fade'); ?>>Fade</option>
                        <option value="zoom" <?php selected($current_settings['animation_type'], 'zoom'); ?>>Zoom</option>
                        <option value="flip" <?php selected($current_settings['animation_type'], 'flip'); ?>>3D Flip</option>
                        <option value="continuous-scroll" <?php selected($current_settings['animation_type'], 'continuous-scroll'); ?>>Continuous Scroll</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="animation_speed">Animation Speed (ms):</label>
                    <input type="number" id="animation_speed" name="animation_speed" 
                           value="<?php echo esc_attr($current_settings['animation_speed']); ?>" 
                           min="100" max="2000" step="100" required>
                    <p style="margin-top: 5px; color: #666; font-size: 14px;">
                        100-2000ms (lower = faster)
                    </p>
                </div>
            </div>
            
            <div class="setting-row">
                <div class="form-group">
                    <label for="auto_interval">Auto-advance Interval (ms):</label>
                    <input type="number" id="auto_interval" name="auto_interval" 
                           value="<?php echo esc_attr($current_settings['auto_interval']); ?>" 
                           min="1000" max="30000" step="1000" required>
                    <p style="margin-top: 5px; color: #666; font-size: 14px;">
                        1000-30000ms (1-30 seconds)
                    </p>
                </div>
                
                <div class="form-group">
                    <label for="direction">Slide Direction:</label>
                    <select id="direction" name="direction" required>
                        <option value="right-to-left" <?php selected($current_settings['direction'], 'right-to-left'); ?>>Right to Left</option>
                        <option value="left-to-right" <?php selected($current_settings['direction'], 'left-to-right'); ?>>Left to Right</option>
                        <option value="top-to-bottom" <?php selected($current_settings['direction'], 'top-to-bottom'); ?>>Top to Bottom</option>
                        <option value="bottom-to-top" <?php selected($current_settings['direction'], 'bottom-to-top'); ?>>Bottom to Top</option>
                    </select>
                </div>
            </div>
            
            <div style="margin-top: 30px;">
                <h3>Recommended Settings:</h3>
                <div class="current-settings">
                    <p><strong>For comfortable viewing:</strong></p>
                    <ul>
                        <li><strong>Animation Type:</strong> Fade or Slide</li>
                        <li><strong>Animation Speed:</strong> 500ms (balanced)</li>
                        <li><strong>Auto-interval:</strong> 10000ms (10 seconds)</li>
                        <li><strong>Direction:</strong> Right to Left</li>
                    </ul>
                </div>
            </div>
            
            <button type="submit" name="update_settings">Update Settings</button>
        </form>

        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd;">
            <p><strong>Security Note:</strong> Please delete this file after updating the settings.</p>
            <p><strong>Test:</strong> After updating, visit your dashboard to see the new carousel behavior.</p>
        </div>
    </div>
</body>
</html>
