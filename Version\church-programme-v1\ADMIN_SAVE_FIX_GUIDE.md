# Admin Panel Save Settings - Debugging & Fix Guide

## Problem
The "Save All Settings" button in the Admin Panel is not responding and changes are not being saved.

## Diagnostic Steps

### Step 1: Run the Debug Test Page
1. Access the debug test page in your browser:
   ```
   http://yourdomain.com/wp-content/plugins/church-programme-dashboard/test-admin-save.php
   ```

2. This page will check:
   - ✓ AJAX handler registration
   - ✓ Class initialization
   - ✓ Current settings in database
   - ✓ JavaScript dependencies
   - ✓ Live AJAX save test

3. Click "Run Save Test" button and check the results

### Step 2: Check Browser Console
1. Open the Admin Settings page
2. Press F12 to open Developer Tools
3. Go to the "Console" tab
4. Click the "Save All Settings" button
5. Look for:
   - Red error messages
   - "FORM SUBMIT TRIGGERED" message (should appear)
   - AJAX request details
   - Response data

### Step 3: Check Network Tab
1. In Developer Tools, go to "Network" tab
2. Click "Save All Settings" button
3. Look for a request to `admin-ajax.php`
4. Check:
   - Request status (should be 200)
   - Request payload (should contain settings data)
   - Response (should contain success message)

## Common Issues & Solutions

### Issue 1: JavaScript Not Loading
**Symptoms:**
- No console messages when clicking save
- Form submits and page reloads

**Solution:**
```bash
# Clear WordPress cache
# In WordPress admin, go to any cache plugin and clear cache
# Or add this to wp-config.php temporarily:
define('WP_DEBUG', true);
define('SCRIPT_DEBUG', true);
```

**Fix:** Use the debug version of the script temporarily:
1. Edit `church-programme-dashboard/admin/class-cpd-admin.php`
2. Find line ~105 where admin-script.js is enqueued
3. Change to use admin-script-debug.js

### Issue 2: cpdAdmin Object Not Defined
**Symptoms:**
- Console error: "cpdAdmin is not defined"
- AJAX request fails

**Solution:**
Check if `wp_localize_script` is being called properly in `admin/class-cpd-admin.php`

### Issue 3: AJAX Handler Not Registered
**Symptoms:**
- AJAX request returns 0 or -1
- Response says "action not found"

**Solution:**
Verify `CPD_AJAX::init()` is being called in main plugin file

### Issue 4: Nonce Verification Failing
**Symptoms:**
- Response says "Permission denied" or "Nonce verification failed"

**Solution:**
1. Log out and log back in
2. Clear browser cache
3. Check if nonce is being generated correctly

### Issue 5: PHP Errors
**Symptoms:**
- AJAX returns HTML instead of JSON
- 500 Internal Server Error

**Solution:**
1. Enable WordPress debug mode
2. Check error logs
3. Look for PHP syntax errors or fatal errors

## Quick Fixes

### Fix 1: Force Script Reload
Add version parameter to force browser to reload JavaScript:

Edit `admin/class-cpd-admin.php` around line 105:
```php
wp_enqueue_script(
    'cpd-admin-script',
    CPD_PLUGIN_URL . 'admin/js/admin-script.js',
    array('jquery', 'wp-color-picker'),
    time(), // Force reload
    true
);
```

### Fix 2: Verify AJAX Handler
Add this test to verify AJAX is working:

Create file `test-ajax-direct.php` in plugin root:
```php
<?php
require_once('../../../wp-load.php');

// Test direct call
$_POST['action'] = 'cpd_save_settings';
$_POST['nonce'] = wp_create_nonce('cpd_ajax_nonce');
$_POST['settings'] = array('test' => 'value');

do_action('wp_ajax_cpd_save_settings');
```

### Fix 3: Add Error Logging to AJAX Handler
Edit `includes/class-cpd-ajax.php`, add logging to save_settings method:

```php
public static function save_settings() {
    error_log('CPD: save_settings called');
    error_log('CPD: POST data: ' . print_r($_POST, true));
    
    check_ajax_referer('cpd_ajax_nonce', 'nonce');
    
    if (!current_user_can('manage_options')) {
        error_log('CPD: Permission denied');
        wp_send_json_error(array('message' => __('Permission denied.', 'church-programme-dashboard')));
    }
    
    $settings = isset($_POST['settings']) ? $_POST['settings'] : array();
    error_log('CPD: Settings to save: ' . print_r($settings, true));
    
    foreach ($settings as $key => $value) {
        // Sanitize based on key
        if (strpos($key, 'color') !== false) {
            $value = sanitize_hex_color($value);
        } elseif (strpos($key, 'url') !== false || strpos($key, 'image') !== false) {
            $value = esc_url_raw($value);
        } elseif (strpos($key, 'content') !== false) {
            $value = wp_kses_post($value);
        } else {
            $value = sanitize_text_field($value);
        }
        
        $result = update_option('cpd_' . $key, $value);
        error_log("CPD: update_option('cpd_$key', '$value') = " . ($result ? 'true' : 'false'));
    }
    
    error_log('CPD: Settings saved successfully');
    wp_send_json_success(array('message' => __('Settings saved successfully.', 'church-programme-dashboard')));
}
```

Then check WordPress debug.log file for the error_log messages.

## Testing Checklist

After applying fixes, test the following:

- [ ] Open Admin Settings page
- [ ] Open browser console (F12)
- [ ] Change a setting (e.g., Dashboard Title)
- [ ] Click "Save All Settings"
- [ ] Check console for "FORM SUBMIT TRIGGERED" message
- [ ] Check for success message on page
- [ ] Refresh page and verify setting was saved
- [ ] Test each tab:
  - [ ] General Settings
  - [ ] Subdomains
  - [ ] Colors & Styling
  - [ ] Programme Labels
  - [ ] Notice Board
  - [ ] Carousel Animation
  - [ ] Cookie Consent
  - [ ] Quick Links (no fields, just links)

## Verification

To verify settings are being saved:

1. Change a setting
2. Click Save
3. Run this in browser console:
```javascript
// Check if AJAX is working
jQuery.post(cpdAdmin.ajaxUrl, {
    action: 'cpd_save_settings',
    nonce: cpdAdmin.nonce,
    settings: {test: 'value'}
}, function(response) {
    console.log('Test response:', response);
});
```

4. Or check database directly:
```sql
SELECT * FROM wp_options WHERE option_name LIKE 'cpd_%';
```

## Files to Check

1. **Main Plugin File:** `church-programme-dashboard.php`
   - Verify CPD_AJAX::init() is called

2. **AJAX Handler:** `includes/class-cpd-ajax.php`
   - Verify save_settings() method exists
   - Check for PHP errors

3. **Admin Class:** `admin/class-cpd-admin.php`
   - Verify scripts are enqueued
   - Check wp_localize_script is called

4. **Admin Script:** `admin/js/admin-script.js`
   - Verify form submit handler is attached
   - Check for JavaScript errors

5. **Settings Page:** `admin/views/settings-page.php`
   - Verify form has id="cpd-settings-form"
   - Check all inputs have name attributes

## Emergency Fallback

If nothing works, you can manually save settings via database:

```php
// Add to functions.php temporarily
add_action('admin_init', function() {
    if (isset($_GET['cpd_manual_save'])) {
        update_option('cpd_dashboard_title', 'Your Title');
        update_option('cpd_primary_color', '#4a5568');
        // Add more as needed
        echo 'Settings saved manually!';
        exit;
    }
});

// Then visit: yourdomain.com/wp-admin/?cpd_manual_save=1
```

## Support

If issues persist after trying all fixes:

1. Check WordPress version (requires 5.8+)
2. Check PHP version (requires 7.4+)
3. Disable other plugins temporarily
4. Switch to default WordPress theme temporarily
5. Check for JavaScript conflicts
6. Review server error logs

## Next Steps

1. Run the debug test page first
2. Check browser console for errors
3. Apply appropriate fix based on findings
4. Test thoroughly
5. Remove debug code once fixed

