/**
 * Dashboard JavaScript
 */

(function() {
    'use strict';
    
    // State
    let currentSlide = 0;
    let slides = [];
    let currentMonth = new Date().getMonth();
    let currentYear = new Date().getFullYear();
    let programmeDates = {};
    
    // Initialize
    document.addEventListener('DOMContentLoaded', function() {
        initCookieConsent();
        loadUpcomingProgrammes();
        loadCalendar();
        initModalHandlers();
    });
    
    // Cookie Consent
    function initCookieConsent() {
        const consent = localStorage.getItem('cpd_cookie_consent');
        
        if (!consent) {
            const cookieConsent = document.getElementById('cpd-cookie-consent');
            if (cookieConsent) {
                cookieConsent.classList.add('show');
            }
        }
        
        const acceptBtn = document.getElementById('cpd-cookie-accept');
        if (acceptBtn) {
            acceptBtn.addEventListener('click', function() {
                localStorage.setItem('cpd_cookie_consent', 'accepted');
                localStorage.setItem('cpd_cookie_consent_date', new Date().toISOString());
                
                const cookieConsent = document.getElementById('cpd-cookie-consent');
                if (cookieConsent) {
                    cookieConsent.classList.remove('show');
                }
            });
        }
    }
    
    // Load Upcoming Programmes for Carousel
    function loadUpcomingProgrammes() {
        fetch(window.cpdData.restUrl + 'programmes/upcoming')
            .then(response => response.json())
            .then(data => {
                slides = processUpcomingProgrammes(data);
                renderCarousel();
                initCarouselHandlers();
            })
            .catch(error => {
                console.error('Error loading programmes:', error);
                showCarouselError();
            });
    }
    
    // Process upcoming programmes into slides
    function processUpcomingProgrammes(data) {
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        
        const upcomingSlides = [];
        const processedDates = new Set();
        
        // Sort dates
        const sortedDates = Object.keys(data).sort();
        
        for (const dateStr of sortedDates) {
            const programmes = data[dateStr];
            const programmeDate = new Date(dateStr + 'T00:00:00');
            
            // Only include future programmes
            if (programmeDate >= today && !processedDates.has(dateStr)) {
                for (const programme of programmes) {
                    const slide = createSlideFromProgramme(programme, dateStr);
                    if (slide) {
                        upcomingSlides.push(slide);
                        processedDates.add(dateStr);
                        
                        // Limit to 3 slides
                        if (upcomingSlides.length >= 3) {
                            return upcomingSlides;
                        }
                    }
                }
            }
        }
        
        return upcomingSlides;
    }
    
    // Create slide from programme data
    function createSlideFromProgramme(programme, dateStr) {
        const date = new Date(dateStr + 'T00:00:00');
        const dayName = date.toLocaleDateString('en-US', { weekday: 'long' });
        const formattedDate = date.toLocaleDateString('en-US', { 
            year: 'numeric', 
            month: 'long', 
            day: 'numeric' 
        });
        
        const data = programme.data;
        const type = programme.type;
        
        let slide = {
            title: '',
            time: '',
            date: dayName + ', ' + formattedDate,
            details: '',
            bgText: dayName.substring(0, 3).toUpperCase()
        };
        
        // Format based on programme type
        switch (type) {
            case 'miet_balang':
                slide.title = window.cpdData.labels.miet_balang;
                slide.time = window.cpdData.labels.miet_balang_time;
                slide.details = formatMietBalangDetails(data);
                break;
                
            case 'jingiaseng_samla':
                slide.title = window.cpdData.labels.jingiaseng_samla;
                slide.time = window.cpdData.labels.jingiaseng_samla_time;
                slide.details = formatJingiasengSamlaDetails(data);
                break;
                
            case 'jingiaseng_1pm':
                slide.title = window.cpdData.labels.jingiaseng_1pm;
                slide.time = window.cpdData.labels.jingiaseng_1pm_time;
                slide.details = formatJingiaseng1pmDetails(data);
                break;
                
            case 'jingiaseng_khynnah':
                slide.title = window.cpdData.labels.jingiaseng_khynnah;
                slide.time = window.cpdData.labels.jingiaseng_khynnah_time;
                slide.details = formatJingiasengKhynnahDetails(data);
                break;
                
            case 'jingiaseng_iing':
                slide.title = window.cpdData.labels.jingiaseng_iing;
                slide.time = window.cpdData.labels.jingiaseng_iing_time;
                slide.details = formatJingiasengIingDetails(data);
                break;
                
            default:
                return null;
        }
        
        return slide;
    }
    
    // Format details for different programme types
    function formatMietBalangDetails(data) {
        let html = '';
        if (data.pule_sdang_duwai) {
            html += '<p><strong>PULE SDANG & DUWAI:</strong> ' + escapeHtml(data.pule_sdang_duwai) + '</p>';
        }
        if (data.nongkren) {
            html += '<p><strong>NONGKREN:</strong> ' + escapeHtml(data.nongkren) + '</p>';
        }
        if (data.khubor) {
            html += '<p><strong>KHUBOR:</strong> ' + escapeHtml(data.khubor) + '</p>';
        }
        return html;
    }
    
    function formatJingiasengSamlaDetails(data) {
        let html = '';
        if (data.special_item) {
            html += '<p><strong>SPECIAL ITEM:</strong> ' + escapeHtml(data.special_item) + '</p>';
        } else {
            if (data.pulesdang_duwai) {
                html += '<p><strong>PULESDANG & DUWAI:</strong> ' + escapeHtml(data.pulesdang_duwai) + '</p>';
            }
            if (data.jingainguh) {
                html += '<p><strong>JINGAINGUH:</strong> ' + escapeHtml(data.jingainguh) + '</p>';
            }
            if (data.special_no) {
                html += '<p><strong>SPECIAL NO.:</strong> ' + escapeHtml(data.special_no) + '</p>';
            }
            if (data.nongkren) {
                html += '<p><strong>NONGKREN:</strong> ' + escapeHtml(data.nongkren) + '</p>';
            }
        }
        return html;
    }
    
    function formatJingiaseng1pmDetails(data) {
        let html = '';
        if (data.nongiathuh_khana_por) {
            html += '<p><strong>NONGIATHUH KHANA POR 1:00PM:</strong> ' + escapeHtml(data.nongiathuh_khana_por) + '</p>';
        }
        return html;
    }
    
    function formatJingiasengKhynnahDetails(data) {
        let html = '';
        if (data.jingrwai_iaroh) {
            html += '<p><strong>JINGRWAI IAROH:</strong> ' + escapeHtml(data.jingrwai_iaroh) + '</p>';
        }
        if (data.nongpule_sdang_old_testament || data.nongpule_sdang_new_testament) {
            html += '<p><strong>NONG PULE SDANG:</strong><br>';
            if (data.nongpule_sdang_old_testament) {
                html += 'OLD TESTAMENT - ' + escapeHtml(data.nongpule_sdang_old_testament);
            }
            if (data.nongpule_sdang_new_testament) {
                if (data.nongpule_sdang_old_testament) html += ' | ';
                html += 'NEW TESTAMENT - ' + escapeHtml(data.nongpule_sdang_new_testament);
            }
            html += '</p>';
        }
        if (data.nong_duwai) {
            html += '<p><strong>NONG DUWAI:</strong> ' + escapeHtml(data.nong_duwai) + '</p>';
        }
        if (data.lum_jingainguh) {
            html += '<p><strong>LUM JINGAINGUH:</strong> ' + escapeHtml(data.lum_jingainguh) + '</p>';
        }
        if (data.jingrwai_kyrpang) {
            html += '<p><strong>JINGRWAI KYRPANG:</strong> ' + escapeHtml(data.jingrwai_kyrpang) + '</p>';
        }
        if (data.duwai_jingainguh) {
            html += '<p><strong>DUWAI JINGAINGUH:</strong> ' + escapeHtml(data.duwai_jingainguh) + '</p>';
        }
        if (data.nongkren_activities) {
            html += '<p><strong>NONGKREN/ACTIVITIES:</strong> ' + escapeHtml(data.nongkren_activities) + '</p>';
        }
        return html;
    }
    
    function formatJingiasengIingDetails(data) {
        let html = '';
        if (data.zone_1) {
            html += '<p><strong>ZONE-1</strong></p>';
            if (data.zone_1.ing) {
                html += '<p><strong>ING:</strong> ' + escapeHtml(data.zone_1.ing) + '</p>';
            }
            if (data.zone_1.nongpule_duwai) {
                html += '<p><strong>NONGPULE & DUWAI:</strong> ' + escapeHtml(data.zone_1.nongpule_duwai) + '</p>';
            }
            if (data.zone_1.nongkren) {
                html += '<p><strong>NONGKREN:</strong> ' + escapeHtml(data.zone_1.nongkren) + '</p>';
            }
        }
        if (data.zone_2) {
            html += '<p style="margin-top: 1rem;"><strong>ZONE-2</strong></p>';
            if (data.zone_2.ing) {
                html += '<p><strong>ING:</strong> ' + escapeHtml(data.zone_2.ing) + '</p>';
            }
            if (data.zone_2.nongpule_duwai) {
                html += '<p><strong>NONGPULE & DUWAI:</strong> ' + escapeHtml(data.zone_2.nongpule_duwai) + '</p>';
            }
            if (data.zone_2.nongkren) {
                html += '<p><strong>NONGKREN:</strong> ' + escapeHtml(data.zone_2.nongkren) + '</p>';
            }
        }
        return html;
    }
    
    // Render carousel
    function renderCarousel() {
        const container = document.getElementById('cpd-carousel-slides');
        
        if (slides.length === 0) {
            container.innerHTML = '<div class="cpd-carousel-loading"><p>No upcoming programmes found.</p></div>';
            return;
        }
        
        let html = '';
        slides.forEach((slide, index) => {
            html += `
                <div class="cpd-carousel-slide ${index === 0 ? 'active' : ''}" data-slide="${index}">
                    <div class="cpd-slide-bg-text">${slide.bgText}</div>
                    <div class="cpd-slide-content">
                        <h2 class="cpd-slide-title">${escapeHtml(slide.title)}</h2>
                        <div class="cpd-slide-time">TIME: ${escapeHtml(slide.time)}</div>
                        <div class="cpd-slide-date">${escapeHtml(slide.date)}</div>
                        <div class="cpd-slide-details">${slide.details}</div>
                    </div>
                </div>
            `;
        });
        
        container.innerHTML = html;
        
        // Render indicators
        renderCarouselIndicators();
    }
    
    // Render carousel indicators
    function renderCarouselIndicators() {
        const container = document.getElementById('cpd-carousel-indicators');
        
        if (slides.length <= 1) {
            container.innerHTML = '';
            return;
        }
        
        let html = '';
        slides.forEach((slide, index) => {
            html += `<div class="cpd-indicator ${index === 0 ? 'active' : ''}" data-slide="${index}"></div>`;
        });
        
        container.innerHTML = html;
        
        // Add click handlers
        container.querySelectorAll('.cpd-indicator').forEach(indicator => {
            indicator.addEventListener('click', function() {
                const slideIndex = parseInt(this.dataset.slide);
                goToSlide(slideIndex);
            });
        });
    }
    
    // Initialize carousel handlers
    function initCarouselHandlers() {
        const prevBtn = document.getElementById('cpd-carousel-prev');
        const nextBtn = document.getElementById('cpd-carousel-next');
        
        if (prevBtn) {
            prevBtn.addEventListener('click', () => goToSlide(currentSlide - 1));
        }
        
        if (nextBtn) {
            nextBtn.addEventListener('click', () => goToSlide(currentSlide + 1));
        }
        
        // Auto-advance carousel
        if (slides.length > 1) {
            setInterval(() => {
                goToSlide(currentSlide + 1);
            }, window.cpdData.carousel.autoInterval);
        }
    }
    
    // Apply animation styles based on settings
    function applyCarouselAnimationStyles() {
        const carousel = document.querySelector('.cpd-carousel-slides');
        if (!carousel) return;
        
        const animationType = window.cpdData.carousel.animationType;
        const animationSpeed = window.cpdData.carousel.animationSpeed;
        const direction = window.cpdData.carousel.direction;
        
        // Remove existing animation classes
        carousel.classList.remove(
            'cpd-animation-slide', 
            'cpd-animation-fade', 
            'cpd-animation-zoom', 
            'cpd-animation-flip',
            'cpd-animation-continuous-scroll'
        );
        
        // Add new animation class
        carousel.classList.add(`cpd-animation-${animationType}`);
        
        // Set animation speed
        carousel.style.setProperty('--animation-speed', `${animationSpeed}ms`);
        
        // Set direction for slide animations
        if (animationType === 'slide' || animationType === 'continuous-scroll') {
            carousel.style.setProperty('--slide-direction', direction);
        }
    }
    
    // Go to specific slide with animation
    function goToSlide(index) {
        if (slides.length === 0) return;
        
        // Wrap around
        if (index < 0) {
            index = slides.length - 1;
        } else if (index >= slides.length) {
            index = 0;
        }
        
        // Skip if already on this slide
        if (index === currentSlide) return;
        
        const animationType = window.cpdData.carousel.animationType;
        const animationSpeed = window.cpdData.carousel.animationSpeed;
        
        // Get current and next slides
        const currentSlideEl = document.querySelector(`.cpd-carousel-slide[data-slide="${currentSlide}"]`);
        const nextSlideEl = document.querySelector(`.cpd-carousel-slide[data-slide="${index}"]`);
        
        if (!currentSlideEl || !nextSlideEl) return;
        
        // Handle different animation types
        switch (animationType) {
            case 'fade':
                applyFadeAnimation(currentSlideEl, nextSlideEl, animationSpeed);
                break;
            case 'zoom':
                applyZoomAnimation(currentSlideEl, nextSlideEl, animationSpeed);
                break;
            case 'flip':
                applyFlipAnimation(currentSlideEl, nextSlideEl, animationSpeed);
                break;
            case 'continuous-scroll':
                applyContinuousScrollAnimation(index);
                break;
            default: // slide
                applySlideAnimation(currentSlideEl, nextSlideEl, animationSpeed);
                break;
        }
        
        currentSlide = index;
        
        // Update indicators
        document.querySelectorAll('.cpd-indicator').forEach((indicator, i) => {
            indicator.classList.toggle('active', i === index);
        });
    }
    
    // Fade animation
    function applyFadeAnimation(currentSlideEl, nextSlideEl, speed) {
        currentSlideEl.style.transition = `opacity ${speed}ms ease`;
        nextSlideEl.style.transition = `opacity ${speed}ms ease`;
        
        currentSlideEl.style.opacity = '0';
        nextSlideEl.style.opacity = '1';
        
        setTimeout(() => {
            currentSlideEl.classList.remove('active');
            nextSlideEl.classList.add('active');
            currentSlideEl.style.opacity = '';
            nextSlideEl.style.opacity = '';
            currentSlideEl.style.transition = '';
            nextSlideEl.style.transition = '';
        }, speed);
    }
    
    // Zoom animation
    function applyZoomAnimation(currentSlideEl, nextSlideEl, speed) {
        currentSlideEl.style.transition = `transform ${speed}ms ease, opacity ${speed}ms ease`;
        nextSlideEl.style.transition = `transform ${speed}ms ease, opacity ${speed}ms ease`;
        
        currentSlideEl.style.transform = 'scale(1.2)';
        currentSlideEl.style.opacity = '0';
        nextSlideEl.style.transform = 'scale(1)';
        nextSlideEl.style.opacity = '1';
        
        setTimeout(() => {
            currentSlideEl.classList.remove('active');
            nextSlideEl.classList.add('active');
            currentSlideEl.style.transform = '';
            currentSlideEl.style.opacity = '';
            nextSlideEl.style.transform = '';
            nextSlideEl.style.opacity = '';
            currentSlideEl.style.transition = '';
            nextSlideEl.style.transition = '';
        }, speed);
    }
    
    // 3D Flip animation
    function applyFlipAnimation(currentSlideEl, nextSlideEl, speed) {
        const container = document.querySelector('.cpd-carousel-slides');
        container.style.perspective = '1000px';
        
        currentSlideEl.style.transition = `transform ${speed}ms ease`;
        nextSlideEl.style.transition = `transform ${speed}ms ease`;
        
        currentSlideEl.style.transform = 'rotateY(-180deg)';
        nextSlideEl.style.transform = 'rotateY(0deg)';
        
        setTimeout(() => {
            currentSlideEl.classList.remove('active');
            nextSlideEl.classList.add('active');
            currentSlideEl.style.transform = '';
            nextSlideEl.style.transform = '';
            currentSlideEl.style.transition = '';
            nextSlideEl.style.transition = '';
        }, speed);
    }
    
    // Slide animation
    function applySlideAnimation(currentSlideEl, nextSlideEl, speed) {
        const direction = window.cpdData.carousel.direction;
        let currentTransform, nextTransform;
        
        switch (direction) {
            case 'left-to-right':
                currentTransform = 'translateX(-100%)';
                nextTransform = 'translateX(0)';
                break;
            case 'top-to-bottom':
                currentTransform = 'translateY(-100%)';
                nextTransform = 'translateY(0)';
                break;
            case 'bottom-to-top':
                currentTransform = 'translateY(100%)';
                nextTransform = 'translateY(0)';
                break;
            default: // right-to-left
                currentTransform = 'translateX(100%)';
                nextTransform = 'translateX(0)';
                break;
        }
        
        currentSlideEl.style.transition = `transform ${speed}ms ease`;
        nextSlideEl.style.transition = `transform ${speed}ms ease`;
        
        currentSlideEl.style.transform = currentTransform;
        nextSlideEl.style.transform = nextTransform;
        
        setTimeout(() => {
            currentSlideEl.classList.remove('active');
            nextSlideEl.classList.add('active');
            currentSlideEl.style.transform = '';
            nextSlideEl.style.transform = '';
            currentSlideEl.style.transition = '';
            nextSlideEl.style.transition = '';
        }, speed);
    }
    
    // Continuous scroll animation
    function applyContinuousScrollAnimation(index) {
        const slidesContainer = document.querySelector('.cpd-carousel-slides');
        const slideWidth = 100; // 100% per slide
        
        // Calculate the transform value based on direction
        const direction = window.cpdData.carousel.direction;
        let transformValue;
        
        switch (direction) {
            case 'left-to-right':
                transformValue = `translateX(${index * slideWidth}%)`;
                break;
            case 'top-to-bottom':
                transformValue = `translateY(${index * slideWidth}%)`;
                break;
            case 'bottom-to-top':
                transformValue = `translateY(-${index * slideWidth}%)`;
                break;
            default: // right-to-left
                transformValue = `translateX(-${index * slideWidth}%)`;
                break;
        }
        
        // Apply smooth transition
        slidesContainer.style.transition = `transform ${window.cpdData.carousel.animationSpeed}ms ease`;
        slidesContainer.style.transform = transformValue;
        
        // Update active classes
        document.querySelectorAll('.cpd-carousel-slide').forEach((slide, i) => {
            slide.classList.toggle('active', i === index);
        });
    }
    
    // Show carousel error
    function showCarouselError() {
        const container = document.getElementById('cpd-carousel-slides');
        container.innerHTML = '<div class="cpd-carousel-loading"><p>Error loading programmes. Please try again later.</p></div>';
    }
    
    // Load Calendar
    function loadCalendar() {
        const url = `${window.cpdData.restUrl}programmes/${currentYear}/${currentMonth + 1}`;

        fetch(url)
            .then(response => response.json())
            .then(data => {
                programmeDates = {};

                // Group programmes by date
                data.forEach(programme => {
                    const date = programme.date;
                    if (!programmeDates[date]) {
                        programmeDates[date] = [];
                    }
                    programmeDates[date].push(programme);
                });

                renderCalendar();
            })
            .catch(error => {
                console.error('Error loading calendar:', error);
            });
    }

    // Render Calendar
    function renderCalendar() {
        const title = document.getElementById('cpd-calendar-title');
        const grid = document.getElementById('cpd-calendar-grid');

        const monthNames = ['January', 'February', 'March', 'April', 'May', 'June',
                           'July', 'August', 'September', 'October', 'November', 'December'];

        title.textContent = `${monthNames[currentMonth]} ${currentYear}`;

        // Get first day of month and number of days
        const firstDay = new Date(currentYear, currentMonth, 1).getDay();
        const daysInMonth = new Date(currentYear, currentMonth + 1, 0).getDate();
        const daysInPrevMonth = new Date(currentYear, currentMonth, 0).getDate();

        let html = '';

        // Day headers
        const dayHeaders = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
        dayHeaders.forEach(day => {
            html += `<div class="cpd-calendar-day-header">${day}</div>`;
        });

        // Previous month days
        for (let i = firstDay - 1; i >= 0; i--) {
            const day = daysInPrevMonth - i;
            html += `<div class="cpd-calendar-day empty other-month">${day}</div>`;
        }

        // Current month days
        const today = new Date();
        const todayStr = today.toISOString().split('T')[0];

        for (let day = 1; day <= daysInMonth; day++) {
            const dateStr = `${currentYear}-${String(currentMonth + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
            const hasProgramme = programmeDates[dateStr] && programmeDates[dateStr].length > 0;
            const isToday = dateStr === todayStr;

            let classes = 'cpd-calendar-day current-month';
            if (isToday) classes += ' today';
            if (hasProgramme) classes += ' has-programme';

            html += `<div class="${classes}" data-date="${dateStr}">${day}</div>`;
        }

        // Next month days
        const totalCells = Math.ceil((firstDay + daysInMonth) / 7) * 7;
        const remainingCells = totalCells - (firstDay + daysInMonth);

        for (let day = 1; day <= remainingCells; day++) {
            html += `<div class="cpd-calendar-day empty other-month">${day}</div>`;
        }

        grid.innerHTML = html;

        // Add click handlers
        grid.querySelectorAll('.cpd-calendar-day.has-programme').forEach(day => {
            day.addEventListener('click', function() {
                const date = this.dataset.date;
                showProgrammeModal(date);
            });
        });

        // Initialize calendar navigation
        initCalendarNavigation();
    }

    // Initialize calendar navigation
    function initCalendarNavigation() {
        const prevBtn = document.getElementById('cpd-calendar-prev');
        const nextBtn = document.getElementById('cpd-calendar-next');

        if (prevBtn) {
            prevBtn.addEventListener('click', () => {
                // Create a new date object to handle month transitions correctly
                const newDate = new Date(currentYear, currentMonth - 1, 1);
                currentMonth = newDate.getMonth();
                currentYear = newDate.getFullYear();
                loadCalendar();
            });
        }

        if (nextBtn) {
            nextBtn.addEventListener('click', () => {
                // Create a new date object to handle month transitions correctly
                const newDate = new Date(currentYear, currentMonth + 1, 1);
                currentMonth = newDate.getMonth();
                currentYear = newDate.getFullYear();
                loadCalendar();
            });
        }
    }

    // Show programme modal
    function showProgrammeModal(date) {
        const programmes = programmeDates[date];

        if (!programmes || programmes.length === 0) {
            return;
        }

        const modal = document.getElementById('cpd-programme-modal');
        const modalBody = document.getElementById('cpd-modal-body');

        const dateObj = new Date(date + 'T00:00:00');
        const formattedDate = dateObj.toLocaleDateString('en-US', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });

        let html = `<h2 style="font-family: 'Poppins', sans-serif; color: var(--primary-color); margin-bottom: 1rem;">${formattedDate}</h2>`;

        programmes.forEach((programme, index) => {
            if (index > 0) {
                html += '<hr style="margin: 1.5rem 0; border: none; border-top: 1px solid #e2e8f0;">';
            }

            html += '<div style="margin-bottom: 1rem;">';

            // Get programme title and time
            let title = '';
            let time = '';

            switch (programme.type) {
                case 'miet_balang':
                    title = window.cpdData.labels.miet_balang;
                    time = window.cpdData.labels.miet_balang_time;
                    html += formatMietBalangDetails(programme.data);
                    break;
                case 'jingiaseng_samla':
                    title = window.cpdData.labels.jingiaseng_samla;
                    time = window.cpdData.labels.jingiaseng_samla_time;
                    html += formatJingiasengSamlaDetails(programme.data);
                    break;
                case 'jingiaseng_1pm':
                    title = window.cpdData.labels.jingiaseng_1pm;
                    time = window.cpdData.labels.jingiaseng_1pm_time;
                    html += formatJingiaseng1pmDetails(programme.data);
                    break;
                case 'jingiaseng_khynnah':
                    title = window.cpdData.labels.jingiaseng_khynnah;
                    time = window.cpdData.labels.jingiaseng_khynnah_time;
                    html += formatJingiasengKhynnahDetails(programme.data);
                    break;
                case 'jingiaseng_iing':
                    title = window.cpdData.labels.jingiaseng_iing;
                    time = window.cpdData.labels.jingiaseng_iing_time;
                    html += formatJingiasengIingDetails(programme.data);
                    break;
            }

            // Prepend title and time
            html = html.replace('<div style="margin-bottom: 1rem;">',
                `<div style="margin-bottom: 1rem;">
                    <h3 style="font-family: 'Poppins', sans-serif; color: var(--accent-color); margin-bottom: 0.5rem;">${escapeHtml(title)}</h3>
                    <p style="color: var(--secondary-color); margin-bottom: 1rem;"><strong>TIME:</strong> ${escapeHtml(time)}</p>
                `);

            html += '</div>';
        });

        modalBody.innerHTML = html;
        modal.classList.add('active');
    }

    // Initialize modal handlers
    function initModalHandlers() {
        const modal = document.getElementById('cpd-programme-modal');
        const closeBtn = document.getElementById('cpd-modal-close');
        const overlay = document.getElementById('cpd-modal-overlay');

        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                modal.classList.remove('active');
            });
        }

        if (overlay) {
            overlay.addEventListener('click', () => {
                modal.classList.remove('active');
            });
        }

        // Close on Escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && modal.classList.contains('active')) {
                modal.classList.remove('active');
            }
        });
    }

    // Escape HTML
    function escapeHtml(text) {
        if (!text) return '';
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

})();
