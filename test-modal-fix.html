<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Programme Modal Fix Test</title>
    <style>
        :root {
            --primary-color: #2563eb;
            --accent-color: #1d4ed8;
            --secondary-color: #64748b;
        }
        
        body {
            font-family: 'Poppins', Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f8fafc;
        }
        
        .test-info {
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        .test-button {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        
        .test-button:hover {
            background: var(--accent-color);
        }
        
        /* Modal Styles */
        .cpd-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1000;
        }
        
        .cpd-modal.active {
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .cpd-modal-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
        }
        
        .cpd-modal-content {
            position: relative;
            background: white;
            border-radius: 12px;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
            margin: 20px;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
        }
        
        .cpd-modal-close {
            position: absolute;
            top: 15px;
            right: 15px;
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #64748b;
            z-index: 1001;
        }
        
        .cpd-modal-body {
            padding: 30px;
        }
        
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        
        .status.success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #10b981;
        }
        
        .status.before {
            background: #fef3c7;
            color: #92400e;
            border: 1px solid #f59e0b;
        }
    </style>
</head>
<body>
    <h1>Programme Modal Fix Test</h1>
    
    <div class="test-info">
        <h3>Modal Display Fix Test</h3>
        <p>This test demonstrates the fix for the programme modal where titles and times were appearing in wrong positions.</p>
        
        <div class="status before">
            <strong>BEFORE FIX:</strong> Titles and times appeared at wrong positions, not grouped with their respective programme details.
        </div>
        
        <div class="status success">
            <strong>AFTER FIX:</strong> Each programme title and time now appears correctly above its corresponding details.
        </div>
        
        <button class="test-button" onclick="showTestModal()">Show Test Modal (Multiple Programmes)</button>
    </div>
    
    <!-- Programme Detail Modal -->
    <div class="cpd-modal" id="cpd-programme-modal">
        <div class="cpd-modal-overlay" onclick="closeModal()"></div>
        <div class="cpd-modal-content">
            <button class="cpd-modal-close" onclick="closeModal()">×</button>
            <div class="cpd-modal-body" id="cpd-modal-body">
                <!-- Programme details will be loaded here -->
            </div>
        </div>
    </div>

    <script>
        // Mock data similar to what the actual application uses
        const mockProgrammes = [
            {
                type: 'jingiaseng_iing',
                data: {
                    zone_1: {
                        ing: 'Mrs.Itreadcy Chyne',
                        nongpule_duwai: 'Mr.Balui lui Pyrngap',
                        nongkren: 'Tpn.L.S.Thubru'
                    },
                    zone_2: {
                        ing: 'Mr.G.Syndai',
                        nongpule_duwai: 'Mrs.Rosesabel Sutnga',
                        nongkren: 'TBn.B.Pajuh'
                    }
                }
            },
            {
                type: 'jingiaseng_khynnah',
                data: {
                    jingrwai_iaroh: 'NONGIALAM JINGRWAI',
                    nongpule_sdang_old_testament: 'Mr.Embok Shylla',
                    nongpule_sdang_new_testament: 'Miss Daimon Shadong',
                    nong_duwai: 'Miss Bethchwamidaka Sutnga',
                    lum_jingainguh: '-',
                    jingrwai_kyrpang: 'Miss Larissa Lakhon',
                    duwai_jingainguh: 'Miss Fedliza Rupon',
                    nongkren_activities: 'Mr K. Marsharing, Topic: Ka Jingduwai.'
                }
            },
            {
                type: 'jingiaseng_1pm',
                data: {
                    nongiathuh_khana_por: 'Bible Sunday Tbn.K.Rymbai'
                }
            }
        ];

        // Mock labels
        window.cpdData = {
            labels: {
                jingiaseng_iing: 'JINGIASENG IING',
                jingiaseng_iing_time: '6:30 PM',
                jingiaseng_khynnah: 'JINGIASENG KHYNNAH',
                jingiaseng_khynnah_time: '3:00 PM',
                jingiaseng_1pm: 'JINGIASENG 1:00 BAJE',
                jingiaseng_1pm_time: '1:00 PM'
            }
        };

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // Format functions (simplified versions)
        function formatJingiasengIingDetails(data) {
            let html = '';
            if (data.zone_1) {
                html += '<p><strong>ZONE-1</strong></p>';
                if (data.zone_1.ing) {
                    html += '<p><strong>ING:</strong> ' + escapeHtml(data.zone_1.ing) + '</p>';
                }
                if (data.zone_1.nongpule_duwai) {
                    html += '<p><strong>NONGPULE & DUWAI:</strong> ' + escapeHtml(data.zone_1.nongpule_duwai) + '</p>';
                }
                if (data.zone_1.nongkren) {
                    html += '<p><strong>NONGKREN:</strong> ' + escapeHtml(data.zone_1.nongkren) + '</p>';
                }
            }
            if (data.zone_2) {
                html += '<p><strong>ZONE-2</strong></p>';
                if (data.zone_2.ing) {
                    html += '<p><strong>ING:</strong> ' + escapeHtml(data.zone_2.ing) + '</p>';
                }
                if (data.zone_2.nongpule_duwai) {
                    html += '<p><strong>NONGPULE & DUWAI:</strong> ' + escapeHtml(data.zone_2.nongpule_duwai) + '</p>';
                }
                if (data.zone_2.nongkren) {
                    html += '<p><strong>NONGKREN:</strong> ' + escapeHtml(data.zone_2.nongkren) + '</p>';
                }
            }
            return html;
        }

        function formatJingiasengKhynnahDetails(data) {
            let html = '';
            if (data.jingrwai_iaroh) {
                html += '<p><strong>JINGRWAI IAROH:</strong> ' + escapeHtml(data.jingrwai_iaroh) + '</p>';
            }
            if (data.nongpule_sdang_old_testament || data.nongpule_sdang_new_testament) {
                html += '<p><strong>NONG PULE SDANG:</strong></p>';
                if (data.nongpule_sdang_old_testament) {
                    html += '<p>OLD TESTAMENT - ' + escapeHtml(data.nongpule_sdang_old_testament) + '</p>';
                }
                if (data.nongpule_sdang_new_testament) {
                    html += '<p>NEW TESTAMENT - ' + escapeHtml(data.nongpule_sdang_new_testament) + '</p>';
                }
            }
            if (data.nong_duwai) {
                html += '<p><strong>NONG DUWAI:</strong> ' + escapeHtml(data.nong_duwai) + '</p>';
            }
            if (data.lum_jingainguh) {
                html += '<p><strong>LUM JINGAINGUH:</strong> ' + escapeHtml(data.lum_jingainguh) + '</p>';
            }
            if (data.jingrwai_kyrpang) {
                html += '<p><strong>JINGRWAI KYRPANG:</strong> ' + escapeHtml(data.jingrwai_kyrpang) + '</p>';
            }
            if (data.duwai_jingainguh) {
                html += '<p><strong>DUWAI JINGAINGUH:</strong> ' + escapeHtml(data.duwai_jingainguh) + '</p>';
            }
            if (data.nongkren_activities) {
                html += '<p><strong>NONGKREN/ACTIVITIES:</strong> ' + escapeHtml(data.nongkren_activities) + '</p>';
            }
            return html;
        }

        function formatJingiaseng1pmDetails(data) {
            let html = '';
            if (data.nongiathuh_khana_por) {
                html += '<p><strong>NONGIATHUH KHANA POR 1:00PM:</strong> ' + escapeHtml(data.nongiathuh_khana_por) + '</p>';
            }
            return html;
        }

        // FIXED Modal function
        function showTestModal() {
            const modal = document.getElementById('cpd-programme-modal');
            const modalBody = document.getElementById('cpd-modal-body');

            let html = `<h2 style="font-family: 'Poppins', sans-serif; color: var(--primary-color); margin-bottom: 1rem;">Sunday, December 7, 2025</h2>`;

            mockProgrammes.forEach((programme, index) => {
                if (index > 0) {
                    html += '<hr style="margin: 1.5rem 0; border: none; border-top: 1px solid #e2e8f0;">';
                }

                // Get programme title and time
                let title = '';
                let time = '';
                let programmeDetails = '';

                switch (programme.type) {
                    case 'jingiaseng_1pm':
                        title = window.cpdData.labels.jingiaseng_1pm;
                        time = window.cpdData.labels.jingiaseng_1pm_time;
                        programmeDetails = formatJingiaseng1pmDetails(programme.data);
                        break;
                    case 'jingiaseng_khynnah':
                        title = window.cpdData.labels.jingiaseng_khynnah;
                        time = window.cpdData.labels.jingiaseng_khynnah_time;
                        programmeDetails = formatJingiasengKhynnahDetails(programme.data);
                        break;
                    case 'jingiaseng_iing':
                        title = window.cpdData.labels.jingiaseng_iing;
                        time = window.cpdData.labels.jingiaseng_iing_time;
                        programmeDetails = formatJingiasengIingDetails(programme.data);
                        break;
                }

                // Build the programme section with proper structure (FIXED VERSION)
                html += `
                    <div style="margin-bottom: 1.5rem;">
                        <h3 style="font-family: 'Poppins', sans-serif; color: var(--accent-color); margin-bottom: 0.5rem;">${escapeHtml(title)}</h3>
                        <p style="color: var(--secondary-color); margin-bottom: 1rem;"><strong>TIME:</strong> ${escapeHtml(time)}</p>
                        ${programmeDetails}
                    </div>
                `;
            });

            modalBody.innerHTML = html;
            modal.classList.add('active');
        }

        function closeModal() {
            const modal = document.getElementById('cpd-programme-modal');
            modal.classList.remove('active');
        }

        // Close modal on Escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                closeModal();
            }
        });
    </script>
</body>
</html>
