<!DOCTYPE html>
<html <?php language_attributes(); ?>>
<head>
    <meta charset="<?php bloginfo('charset'); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <title><?php echo esc_html(get_option('cpd_dashboard_title', 'Church Programme')); ?></title>
    
    <!-- Google Fonts (Open SIL License) -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <link rel="stylesheet" href="<?php echo CPD_PLUGIN_URL; ?>public/css/dashboard-style.css?v=<?php echo CPD_VERSION; ?>">
    
    <style>
        :root {
            --primary-color: <?php echo esc_attr(get_option('cpd_primary_color', '#4a5568')); ?>;
            --secondary-color: <?php echo esc_attr(get_option('cpd_secondary_color', '#718096')); ?>;
            --accent-color: <?php echo esc_attr(get_option('cpd_accent_color', '#3182ce')); ?>;
            --text-color: <?php echo esc_attr(get_option('cpd_text_color', '#2d3748')); ?>;
            --header-bg-color: <?php echo esc_attr(get_option('cpd_header_bg_color', '#ffffff')); ?>;
        }
        
        <?php if (get_option('cpd_header_bg_image')): ?>
        .cpd-header {
            background-image: url('<?php echo esc_url(get_option('cpd_header_bg_image')); ?>');
            background-size: cover;
            background-position: center;
        }
        <?php endif; ?>
    </style>
</head>
<body class="cpd-dashboard-body">
    
    <div class="cpd-dashboard-container">
        
        <!-- Header -->
        <header class="cpd-header">
            <div class="cpd-header-content">
                <h1 class="cpd-header-title"><?php echo esc_html(get_option('cpd_dashboard_title', 'Church Programme')); ?></h1>
            </div>
        </header>
        
        <!-- Carousel Slider -->
        <section class="cpd-carousel-section">
            <div class="cpd-carousel-container">
                <div class="cpd-carousel-slides" id="cpd-carousel-slides">
                    <!-- Slides will be dynamically loaded -->
                    <div class="cpd-carousel-loading">
                        <div class="cpd-spinner"></div>
                        <p>Loading programmes...</p>
                    </div>
                </div>
                
                <!-- Carousel Navigation -->
                <div class="cpd-carousel-nav">
                    <button class="cpd-carousel-btn cpd-carousel-prev" id="cpd-carousel-prev" aria-label="Previous slide">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <polyline points="15 18 9 12 15 6"></polyline>
                        </svg>
                    </button>
                    <button class="cpd-carousel-btn cpd-carousel-next" id="cpd-carousel-next" aria-label="Next slide">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <polyline points="9 18 15 12 9 6"></polyline>
                        </svg>
                    </button>
                </div>
                
                <!-- Carousel Indicators -->
                <div class="cpd-carousel-indicators" id="cpd-carousel-indicators"></div>
            </div>
        </section>
        
        <!-- Notice Board -->
        <?php if (get_option('cpd_notice_board_enabled')): ?>
        <section class="cpd-notice-board">
            <div class="cpd-notice-board-container">
                <div class="cpd-notice-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <circle cx="12" cy="12" r="10"></circle>
                        <line x1="12" y1="16" x2="12" y2="12"></line>
                        <line x1="12" y1="8" x2="12.01" y2="8"></line>
                    </svg>
                </div>
                <div class="cpd-notice-content">
                    <h2 class="cpd-notice-title"><?php echo esc_html(get_option('cpd_notice_board_title', 'Notice Board')); ?></h2>
                    <div class="cpd-notice-text">
                        <?php echo wp_kses_post(get_option('cpd_notice_board_content')); ?>
                    </div>
                </div>
            </div>
        </section>
        <?php endif; ?>
        
        <!-- Calendar Section -->
        <section class="cpd-calendar-section">
            <div class="cpd-calendar-container">
                <div class="cpd-calendar-header">
                    <button class="cpd-calendar-nav-btn" id="cpd-calendar-prev" aria-label="Previous month">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <polyline points="15 18 9 12 15 6"></polyline>
                        </svg>
                    </button>
                    <h2 class="cpd-calendar-title" id="cpd-calendar-title">Loading...</h2>
                    <button class="cpd-calendar-nav-btn" id="cpd-calendar-next" aria-label="Next month">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <polyline points="9 18 15 12 9 6"></polyline>
                        </svg>
                    </button>
                </div>
                
                <div class="cpd-calendar-grid" id="cpd-calendar-grid">
                    <!-- Calendar will be dynamically loaded -->
                </div>
            </div>
        </section>
        
        <!-- Footer -->
        <footer class="cpd-footer">
            <div class="cpd-footer-content">
                <div class="cpd-footer-attribution">
                    <p>
                        Fonts: <a href="https://fonts.google.com/specimen/Inter" target="_blank" rel="noopener">Inter</a> & 
                        <a href="https://fonts.google.com/specimen/Poppins" target="_blank" rel="noopener">Poppins</a> 
                        (Open Font License)
                    </p>
                    <p>
                        Powered by <a href="https://www.jermesa.com" target="_blank" rel="noopener">Jermesa Studio</a> | 
                        <a href="https://jermesa.com/privacy-policy/" target="_blank" rel="noopener">Privacy Policy</a>
                    </p>
                </div>
            </div>
        </footer>
        
    </div>
    
    <!-- Programme Detail Modal -->
    <div class="cpd-modal" id="cpd-programme-modal">
        <div class="cpd-modal-overlay" id="cpd-modal-overlay"></div>
        <div class="cpd-modal-content">
            <button class="cpd-modal-close" id="cpd-modal-close" aria-label="Close modal">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <line x1="18" y1="6" x2="6" y2="18"></line>
                    <line x1="6" y1="6" x2="18" y2="18"></line>
                </svg>
            </button>
            <div class="cpd-modal-body" id="cpd-modal-body">
                <!-- Programme details will be loaded here -->
            </div>
        </div>
    </div>
    
    <!-- Cookie Consent -->
    <div class="cpd-cookie-consent" id="cpd-cookie-consent">
        <div class="cpd-cookie-content">
            <p><?php echo esc_html(get_option('cpd_cookie_consent_text', 'We use cookies to enhance your experience.')); ?></p>
            <button class="cpd-cookie-accept" id="cpd-cookie-accept">
                <?php echo esc_html(get_option('cpd_cookie_consent_button', 'Accept')); ?>
            </button>
        </div>
    </div>
    
    <script>
        // Pass PHP data to JavaScript
        window.cpdData = {
            restUrl: '<?php echo rest_url('cpd/v1/'); ?>',
            labels: <?php echo json_encode(array(
                'miet_balang' => get_option('cpd_label_miet_balang'),
                'miet_balang_time' => get_option('cpd_label_miet_balang_time'),
                'jingiaseng_samla' => get_option('cpd_label_jingiaseng_samla'),
                'jingiaseng_samla_time' => get_option('cpd_label_jingiaseng_samla_time'),
                'jingiaseng_1pm' => get_option('cpd_label_jingiaseng_1pm'),
                'jingiaseng_1pm_time' => get_option('cpd_label_jingiaseng_1pm_time'),
                'jingiaseng_khynnah' => get_option('cpd_label_jingiaseng_khynnah'),
                'jingiaseng_khynnah_time' => get_option('cpd_label_jingiaseng_khynnah_time'),
                'jingiaseng_iing' => get_option('cpd_label_jingiaseng_iing'),
                'jingiaseng_iing_time' => get_option('cpd_label_jingiaseng_iing_time'),
            )); ?>,
            carousel: {
                animationType: '<?php echo esc_js(get_option('cpd_carousel_animation_type', 'slide')); ?>',
                animationSpeed: <?php echo esc_js(get_option('cpd_carousel_animation_speed', '500')); ?>,
                autoInterval: <?php echo esc_js(get_option('cpd_carousel_auto_interval', '10000')); ?>,
                direction: '<?php echo esc_js(get_option('cpd_carousel_direction', 'right-to-left')); ?>',
                gradientType: '<?php echo esc_js(get_option('cpd_carousel_gradient_type', 'preset')); ?>',
                gradientPreset: '<?php echo esc_js(get_option('cpd_carousel_gradient_preset', 'blue-purple')); ?>',
                gradientCustomStart: '<?php echo esc_js(get_option('cpd_carousel_gradient_custom_start', '#667eea')); ?>',
                gradientCustomEnd: '<?php echo esc_js(get_option('cpd_carousel_gradient_custom_end', '#764ba2')); ?>',
                gradientCustomAngle: <?php echo esc_js(get_option('cpd_carousel_gradient_custom_angle', '135')); ?>
            }
        };
    </script>
    
    <script src="<?php echo CPD_PLUGIN_URL; ?>public/js/dashboard-script.js?v=<?php echo CPD_VERSION; ?>"></script>
    
</body>
</html>
