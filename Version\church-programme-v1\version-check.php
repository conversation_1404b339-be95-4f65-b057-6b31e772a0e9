<?php
/**
 * Version Check File
 * 
 * Access this file to verify plugin files are updated
 * URL: https://jermesa.com/wp-content/plugins/church-programme-dashboard/version-check.php
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    // Allow direct access for version check
    define('VERSION_CHECK', true);
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>Church Programme Dashboard - Version Check</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }
        .status {
            padding: 15px;
            margin: 15px 0;
            border-radius: 4px;
        }
        .success {
            background: #d4edda;
            border-left: 4px solid #28a745;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border-left: 4px solid #dc3545;
            color: #721c24;
        }
        .info {
            background: #d1ecf1;
            border-left: 4px solid #17a2b8;
            color: #0c5460;
        }
        .file-list {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
        }
        .file-item {
            padding: 8px;
            margin: 5px 0;
            background: white;
            border-left: 3px solid #28a745;
        }
        .file-item.missing {
            border-left-color: #dc3545;
        }
        code {
            background: #f4f4f4;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }
        .timestamp {
            color: #666;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Church Programme Dashboard - Version Check</h1>
        
        <div class="status info">
            <strong>Check Time:</strong> <?php echo date('Y-m-d H:i:s'); ?>
        </div>
        
        <?php
        $plugin_dir = dirname(__FILE__);
        $files_to_check = array(
            'admin/js/admin-programmes.js' => 'Programme management JavaScript',
            'includes/class-cpd-rest-api.php' => 'REST API handler',
            'includes/class-cpd-subdomain.php' => 'Subdomain handler',
            'admin/views/settings-page.php' => 'Settings page',
            'admin/class-cpd-admin.php' => 'Admin class',
        );
        
        $all_exist = true;
        $file_info = array();
        
        foreach ($files_to_check as $file => $description) {
            $full_path = $plugin_dir . '/' . $file;
            $exists = file_exists($full_path);
            
            if (!$exists) {
                $all_exist = false;
            }
            
            $file_info[] = array(
                'file' => $file,
                'description' => $description,
                'exists' => $exists,
                'modified' => $exists ? filemtime($full_path) : null,
                'size' => $exists ? filesize($full_path) : null,
            );
        }
        ?>
        
        <?php if ($all_exist): ?>
            <div class="status success">
                <strong>✅ All Required Files Found</strong><br>
                All plugin files are present on the server.
            </div>
        <?php else: ?>
            <div class="status error">
                <strong>❌ Missing Files Detected</strong><br>
                Some plugin files are missing. Please re-upload the plugin.
            </div>
        <?php endif; ?>
        
        <h2>📁 File Status</h2>
        <div class="file-list">
            <?php foreach ($file_info as $info): ?>
                <div class="file-item <?php echo $info['exists'] ? '' : 'missing'; ?>">
                    <strong><?php echo $info['exists'] ? '✅' : '❌'; ?> <?php echo htmlspecialchars($info['file']); ?></strong><br>
                    <small><?php echo htmlspecialchars($info['description']); ?></small><br>
                    <?php if ($info['exists']): ?>
                        <span class="timestamp">
                            Modified: <?php echo date('Y-m-d H:i:s', $info['modified']); ?> | 
                            Size: <?php echo number_format($info['size']); ?> bytes
                        </span>
                    <?php else: ?>
                        <span style="color: #dc3545;">File not found</span>
                    <?php endif; ?>
                </div>
            <?php endforeach; ?>
        </div>
        
        <h2>🔧 Fix Verification</h2>
        
        <?php
        // Check if REST API fix is applied
        $rest_api_file = $plugin_dir . '/includes/class-cpd-rest-api.php';
        $rest_api_fixed = false;
        if (file_exists($rest_api_file)) {
            $content = file_get_contents($rest_api_file);
            $rest_api_fixed = strpos($content, "current_user_can('manage_options')") !== false;
        }
        ?>
        
        <div class="status <?php echo $rest_api_fixed ? 'success' : 'error'; ?>">
            <strong>REST API Fix:</strong> 
            <?php if ($rest_api_fixed): ?>
                ✅ Applied - WordPress admin permission check is present
            <?php else: ?>
                ❌ Not Applied - File needs to be updated
            <?php endif; ?>
        </div>
        
        <?php
        // Check if admin-programmes.js fix is applied
        $admin_js_file = $plugin_dir . '/admin/js/admin-programmes.js';
        $admin_js_fixed = false;
        if (file_exists($admin_js_file)) {
            $content = file_get_contents($admin_js_file);
            $admin_js_fixed = strpos($content, "if (form.length && form[0])") !== false;
        }
        ?>
        
        <div class="status <?php echo $admin_js_fixed ? 'success' : 'error'; ?>">
            <strong>Admin JavaScript Fix:</strong> 
            <?php if ($admin_js_fixed): ?>
                ✅ Applied - Form existence check is present
            <?php else: ?>
                ❌ Not Applied - File needs to be updated
            <?php endif; ?>
        </div>
        
        <?php
        // Check if subdomain fallback is applied
        $subdomain_file = $plugin_dir . '/includes/class-cpd-subdomain.php';
        $subdomain_fixed = false;
        if (file_exists($subdomain_file)) {
            $content = file_get_contents($subdomain_file);
            $subdomain_fixed = strpos($content, "handle_path_fallback") !== false;
        }
        ?>

        <div class="status <?php echo $subdomain_fixed ? 'success' : 'error'; ?>">
            <strong>Subdomain Fallback Fix:</strong>
            <?php if ($subdomain_fixed): ?>
                ✅ Applied - Path fallback handler is present
            <?php else: ?>
                ❌ Not Applied - File needs to be updated
            <?php endif; ?>
        </div>

        <?php
        // Check if AI model detection fix is applied
        $ai_file = $plugin_dir . '/includes/class-cpd-ai.php';
        $ai_fixed = false;
        if (file_exists($ai_file)) {
            $content = file_get_contents($ai_file);
            $ai_fixed = (strpos($content, "gpt-4o") !== false && strpos($content, "claude-3") !== false);
        }
        ?>

        <div class="status <?php echo $ai_fixed ? 'success' : 'error'; ?>">
            <strong>AI Model Detection Fix:</strong>
            <?php if ($ai_fixed): ?>
                ✅ Applied - Enhanced vision model detection is present
            <?php else: ?>
                ❌ Not Applied - File needs to be updated
            <?php endif; ?>
        </div>

        <?php
        // Check if cache busting fix is applied
        $admin_class_file = $plugin_dir . '/admin/class-cpd-admin.php';
        $cache_busting_fixed = false;
        if (file_exists($admin_class_file)) {
            $content = file_get_contents($admin_class_file);
            $cache_busting_fixed = strpos($content, "filemtime") !== false;
        }
        ?>

        <div class="status <?php echo $cache_busting_fixed ? 'success' : 'error'; ?>">
            <strong>Cache Busting Fix:</strong>
            <?php if ($cache_busting_fixed): ?>
                ✅ Applied - File modification time versioning is present
            <?php else: ?>
                ❌ Not Applied - File needs to be updated
            <?php endif; ?>
        </div>

        <h2>📋 Next Steps</h2>

        <?php if ($all_exist && $rest_api_fixed && $admin_js_fixed && $subdomain_fixed && $ai_fixed && $cache_busting_fixed): ?>
            <div class="status success">
                <strong>✅ All Fixes Applied Successfully!</strong><br><br>
                Your plugin is up to date. If you're still experiencing issues:
                <ol>
                    <li>Clear your browser cache (Ctrl+Shift+Delete)</li>
                    <li>Hard refresh the admin page (Ctrl+F5 or Cmd+Shift+R)</li>
                    <li>Go to Settings > Permalinks and click "Save Changes"</li>
                    <li>Try accessing the programme management again</li>
                </ol>
            </div>
        <?php else: ?>
            <div class="status error">
                <strong>⚠️ Action Required</strong><br><br>
                <ol>
                    <li>Re-upload the entire <code>church-programme-dashboard</code> folder to your server</li>
                    <li>Overwrite all existing files</li>
                    <li>Refresh this page to verify</li>
                    <li>Clear browser cache and try again</li>
                </ol>
            </div>
        <?php endif; ?>
        
        <h2>🌐 Test URLs</h2>
        <div class="file-list">
            <div class="file-item">
                <strong>Dashboard (Temporary):</strong><br>
                <a href="<?php echo home_url('/cpd-dashboard/'); ?>" target="_blank">
                    <?php echo home_url('/cpd-dashboard/'); ?>
                </a>
            </div>
            <div class="file-item">
                <strong>Editor (Temporary):</strong><br>
                <a href="<?php echo home_url('/cpd-editor/'); ?>" target="_blank">
                    <?php echo home_url('/cpd-editor/'); ?>
                </a>
            </div>
            <div class="file-item">
                <strong>Admin Settings:</strong><br>
                <a href="<?php echo admin_url('admin.php?page=church-programme-dashboard'); ?>" target="_blank">
                    <?php echo admin_url('admin.php?page=church-programme-dashboard'); ?>
                </a>
            </div>
        </div>
        
        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; color: #666; font-size: 0.9em;">
            <strong>Plugin Version:</strong> 1.0.0<br>
            <strong>Fixes Applied:</strong> 2025-09-30<br>
            <strong>Plugin Directory:</strong> <code><?php echo htmlspecialchars($plugin_dir); ?></code>
        </div>
    </div>
</body>
</html>

