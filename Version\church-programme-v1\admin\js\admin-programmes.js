/**
 * Admin Programme Management JavaScript
 */

(function($) {
    'use strict';
    
    $(document).ready(function() {
        // Only run on settings page with programmes tab
        if ($('#programmes').length === 0) {
            return;
        }
        
        // Load programmes when tab is clicked
        $('.nav-tab[href="#programmes"]').on('click', function() {
            loadAdminProgrammes();
        });
        
        // Filter buttons
        $('#admin-apply-filter').on('click', function() {
            loadAdminProgrammes();
        });
        
        $('#admin-clear-filter').on('click', function() {
            $('#admin-filter-type').val('');
            $('#admin-filter-start-date').val('');
            $('#admin-filter-end-date').val('');
            loadAdminProgrammes();
        });
        
        // Add programme button
        $('#admin-add-programme').on('click', function() {
            openProgrammeModal();
        });
        
        // Close modal
        $('#admin-modal-close').on('click', function() {
            closeProgrammeModal();
        });
        
        // Programme type change
        $('#admin-programme-type').on('change', function() {
            generateProgrammeFields($(this).val());
        });
        
        // Form submission
        $('#admin-programme-form').on('submit', function(e) {
            e.preventDefault();
            saveProgramme();
        });
        
        // Load programmes on page load if tab is active
        if ($('#programmes').hasClass('cpd-tab-active')) {
            loadAdminProgrammes();
        }
        
        // Load Programmes
        function loadAdminProgrammes() {
            var listContainer = $('#admin-programmes-list');
            listContainer.html('<p style="text-align: center; padding: 2rem; color: #666;">Loading programmes...</p>');
            
            var filterType = $('#admin-filter-type').val();
            var filterStartDate = $('#admin-filter-start-date').val();
            var filterEndDate = $('#admin-filter-end-date').val();
            
            var queryParams = [];
            if (filterType) queryParams.push('type=' + encodeURIComponent(filterType));
            if (filterStartDate) queryParams.push('start_date=' + encodeURIComponent(filterStartDate));
            if (filterEndDate) queryParams.push('end_date=' + encodeURIComponent(filterEndDate));
            
            var queryString = queryParams.length > 0 ? '?' + queryParams.join('&') : '';
            
            $.ajax({
                url: cpdAdmin.restUrl + 'editor/programmes' + queryString,
                method: 'GET',
                beforeSend: function(xhr) {
                    xhr.setRequestHeader('X-WP-Nonce', cpdAdmin.restNonce || cpdAdmin.nonce);
                },
                success: function(response) {
                    if (response.success && response.programmes && response.programmes.length > 0) {
                        renderProgrammesList(response.programmes);
                    } else {
                        listContainer.html('<p style="text-align: center; padding: 2rem; color: #666;">No programmes found. Click "Add New Programme" to create one.</p>');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error loading programmes:', xhr.status, xhr.responseText);
                    listContainer.html('<p style="text-align: center; padding: 2rem; color: #c00;">Error loading programmes (Status: ' + xhr.status + '). Please try again.</p>');
                }
            });
        }
        
        // Render Programmes List
        function renderProgrammesList(programmes) {
            var listContainer = $('#admin-programmes-list');
            var html = '<table class="wp-list-table widefat fixed striped">';
            html += '<thead><tr>';
            html += '<th>Type</th>';
            html += '<th>Date</th>';
            html += '<th>Time</th>';
            html += '<th>Details</th>';
            html += '<th>Actions</th>';
            html += '</tr></thead>';
            html += '<tbody>';
            
            programmes.forEach(function(programme) {
                var typeLabel = getProgrammeTypeLabel(programme.programme_type);
                var formattedDate = formatDate(programme.programme_date);
                var formattedTime = formatTime(programme.programme_time);
                var preview = generatePreview(programme);
                
                html += '<tr>';
                html += '<td><strong>' + typeLabel + '</strong></td>';
                html += '<td>' + formattedDate + '</td>';
                html += '<td>' + formattedTime + '</td>';
                html += '<td style="font-size: 0.9em; color: #666;">' + preview + '</td>';
                html += '<td>';
                html += '<button type="button" class="button button-small admin-edit-programme" data-id="' + programme.id + '">Edit</button> ';
                html += '<button type="button" class="button button-small button-link-delete admin-delete-programme" data-id="' + programme.id + '">Delete</button>';
                html += '</td>';
                html += '</tr>';
            });
            
            html += '</tbody></table>';
            listContainer.html(html);
            
            // Attach event listeners
            $('.admin-edit-programme').on('click', function() {
                editProgramme($(this).data('id'));
            });
            
            $('.admin-delete-programme').on('click', function() {
                deleteProgramme($(this).data('id'));
            });
        }
        
        // Open Programme Modal
        function openProgrammeModal(programmeId) {
            $('#admin-programme-id').val(programmeId || '');
            $('#admin-modal-title').text(programmeId ? 'Edit Programme' : 'Add Programme');

            // Reset form if it exists
            var form = $('#admin-programme-form');
            if (form.length && form[0]) {
                form[0].reset();
            }

            $('#admin-programme-fields').html('');
            $('#admin-programme-modal').css('display', 'flex');
        }
        
        // Close Programme Modal
        function closeProgrammeModal() {
            $('#admin-programme-modal').hide();
        }
        
        // Generate Programme Fields
        function generateProgrammeFields(type) {
            var container = $('#admin-programme-fields');
            var html = '<table class="form-table">';
            
            switch (type) {
                case 'jingiaseng_1pm':
                    html += '<tr><th scope="row"><label>NONGIATHUH KHANA POR 1:00PM</label></th>';
                    html += '<td><input type="text" id="field-nongiathuh" class="regular-text"></td></tr>';
                    break;
                    
                case 'miet_balang':
                    html += '<tr><th scope="row"><label>PULE SDANG & DUWAI</label></th>';
                    html += '<td><input type="text" id="field-pule-sdang" class="regular-text"></td></tr>';
                    html += '<tr><th scope="row"><label>NONGKREN</label></th>';
                    html += '<td><input type="text" id="field-nongkren" class="regular-text"></td></tr>';
                    html += '<tr><th scope="row"><label>KHUBOR</label></th>';
                    html += '<td><input type="text" id="field-khubor" class="regular-text"></td></tr>';
                    break;
                    
                case 'jingiaseng_samla':
                    html += '<tr><th scope="row"><label><input type="checkbox" id="field-special-item-check"> Special Item</label></th><td></td></tr>';
                    html += '<tr id="special-item-row" style="display:none;"><th scope="row"><label>Special Item Name</label></th>';
                    html += '<td><input type="text" id="field-special-item" class="regular-text"></td></tr>';
                    html += '<tr class="regular-samla-row"><th scope="row"><label>PULESDANG & DUWAI</label></th>';
                    html += '<td><input type="text" id="field-pulesdang" class="regular-text"></td></tr>';
                    html += '<tr class="regular-samla-row"><th scope="row"><label>JINGAINGUH</label></th>';
                    html += '<td><input type="text" id="field-jingainguh" class="regular-text"></td></tr>';
                    html += '<tr class="regular-samla-row"><th scope="row"><label>SPECIAL NO.</label></th>';
                    html += '<td><input type="text" id="field-special-no" class="regular-text"></td></tr>';
                    html += '<tr class="regular-samla-row"><th scope="row"><label>NONGKREN</label></th>';
                    html += '<td><input type="text" id="field-nongkren-samla" class="regular-text"></td></tr>';
                    break;
                    
                case 'jingiaseng_khynnah':
                    html += '<tr><th scope="row"><label>JINGRWAI IAROH</label></th>';
                    html += '<td><input type="text" id="field-jingrwai-iaroh" class="regular-text"></td></tr>';
                    html += '<tr><th scope="row"><label>NONGPULE SDANG - OLD TESTAMENT</label></th>';
                    html += '<td><input type="text" id="field-nongpule-old" class="regular-text"></td></tr>';
                    html += '<tr><th scope="row"><label>NONGPULE SDANG - NEW TESTAMENT</label></th>';
                    html += '<td><input type="text" id="field-nongpule-new" class="regular-text"></td></tr>';
                    html += '<tr><th scope="row"><label>NONG DUWAI</label></th>';
                    html += '<td><input type="text" id="field-nong-duwai" class="regular-text"></td></tr>';
                    html += '<tr><th scope="row"><label>LUM JINGAINGUH</label></th>';
                    html += '<td><input type="text" id="field-lum-jingainguh" class="regular-text"></td></tr>';
                    html += '<tr><th scope="row"><label>JINGRWAI KYRPANG</label></th>';
                    html += '<td><input type="text" id="field-jingrwai-kyrpang" class="regular-text"></td></tr>';
                    html += '<tr><th scope="row"><label>DUWAI JINGAINGUH</label></th>';
                    html += '<td><input type="text" id="field-duwai-jingainguh" class="regular-text"></td></tr>';
                    html += '<tr><th scope="row"><label>NONGKREN/ACTIVITIES</label></th>';
                    html += '<td><input type="text" id="field-nongkren-activities" class="regular-text"></td></tr>';
                    break;
                    
                case 'jingiaseng_iing':
                    html += '<tr><th colspan="2"><h3>ZONE-1</h3></th></tr>';
                    html += '<tr><th scope="row"><label>ING</label></th>';
                    html += '<td><input type="text" id="field-zone1-ing" class="regular-text"></td></tr>';
                    html += '<tr><th scope="row"><label>NONGPULE & DUWAI</label></th>';
                    html += '<td><input type="text" id="field-zone1-nongpule" class="regular-text"></td></tr>';
                    html += '<tr><th scope="row"><label>NONGKREN</label></th>';
                    html += '<td><input type="text" id="field-zone1-nongkren" class="regular-text"></td></tr>';
                    html += '<tr><th colspan="2"><h3>ZONE-2</h3></th></tr>';
                    html += '<tr><th scope="row"><label>ING</label></th>';
                    html += '<td><input type="text" id="field-zone2-ing" class="regular-text"></td></tr>';
                    html += '<tr><th scope="row"><label>NONGPULE & DUWAI</label></th>';
                    html += '<td><input type="text" id="field-zone2-nongpule" class="regular-text"></td></tr>';
                    html += '<tr><th scope="row"><label>NONGKREN</label></th>';
                    html += '<td><input type="text" id="field-zone2-nongkren" class="regular-text"></td></tr>';
                    break;
            }
            
            html += '</table>';
            container.html(html);
            
            // Special item checkbox handler
            $('#field-special-item-check').on('change', function() {
                if ($(this).is(':checked')) {
                    $('#special-item-row').show();
                    $('.regular-samla-row').hide();
                } else {
                    $('#special-item-row').hide();
                    $('.regular-samla-row').show();
                }
            });
        }
        
        // Save Programme
        function saveProgramme() {
            var programmeId = $('#admin-programme-id').val();
            var type = $('#admin-programme-type').val();
            var date = $('#admin-programme-date').val();
            var time = $('#admin-programme-time').val();
            
            if (!type || !date || !time) {
                alert('Please fill in all required fields.');
                return;
            }
            
            var data = collectProgrammeData(type);
            
            var url = cpdAdmin.restUrl + 'editor/programmes';
            var method = 'POST';
            
            if (programmeId) {
                url += '/' + programmeId;
                method = 'PUT';
            }
            
            $.ajax({
                url: url,
                method: method,
                data: JSON.stringify({
                    type: type,
                    date: date,
                    time: time,
                    data: data
                }),
                contentType: 'application/json',
                beforeSend: function(xhr) {
                    xhr.setRequestHeader('X-WP-Nonce', cpdAdmin.restNonce || cpdAdmin.nonce);
                },
                success: function(response) {
                    if (response.success) {
                        alert(programmeId ? 'Programme updated successfully!' : 'Programme added successfully!');
                        closeProgrammeModal();
                        loadAdminProgrammes();
                    } else {
                        alert('Error: ' + (response.message || 'Failed to save programme'));
                    }
                },
                error: function() {
                    alert('An error occurred while saving the programme.');
                }
            });
        }
        
        // Collect Programme Data
        function collectProgrammeData(type) {
            var data = {};
            
            switch (type) {
                case 'jingiaseng_1pm':
                    data.nongiathuh_khana_por = $('#field-nongiathuh').val() || '';
                    break;
                    
                case 'miet_balang':
                    data.pule_sdang_duwai = $('#field-pule-sdang').val() || '';
                    data.nongkren = $('#field-nongkren').val() || '';
                    data.khubor = $('#field-khubor').val() || '';
                    break;
                    
                case 'jingiaseng_samla':
                    if ($('#field-special-item-check').is(':checked')) {
                        data.special_item = $('#field-special-item').val() || '';
                    } else {
                        data.pulesdang_duwai = $('#field-pulesdang').val() || '';
                        data.jingainguh = $('#field-jingainguh').val() || '';
                        data.special_no = $('#field-special-no').val() || '';
                        data.nongkren = $('#field-nongkren-samla').val() || '';
                    }
                    break;
                    
                case 'jingiaseng_khynnah':
                    data.jingrwai_iaroh = $('#field-jingrwai-iaroh').val() || '';
                    data.nongpule_sdang_old_testament = $('#field-nongpule-old').val() || '';
                    data.nongpule_sdang_new_testament = $('#field-nongpule-new').val() || '';
                    data.nong_duwai = $('#field-nong-duwai').val() || '';
                    data.lum_jingainguh = $('#field-lum-jingainguh').val() || '';
                    data.jingrwai_kyrpang = $('#field-jingrwai-kyrpang').val() || '';
                    data.duwai_jingainguh = $('#field-duwai-jingainguh').val() || '';
                    data.nongkren_activities = $('#field-nongkren-activities').val() || '';
                    break;
                    
                case 'jingiaseng_iing':
                    data.zone_1 = {
                        ing: $('#field-zone1-ing').val() || '',
                        nongpule_duwai: $('#field-zone1-nongpule').val() || '',
                        nongkren: $('#field-zone1-nongkren').val() || ''
                    };
                    data.zone_2 = {
                        ing: $('#field-zone2-ing').val() || '',
                        nongpule_duwai: $('#field-zone2-nongpule').val() || '',
                        nongkren: $('#field-zone2-nongkren').val() || ''
                    };
                    break;
            }
            
            return data;
        }
        
        // Edit Programme
        function editProgramme(programmeId) {
            $.ajax({
                url: cpdAdmin.restUrl + 'editor/programmes/' + programmeId,
                method: 'GET',
                beforeSend: function(xhr) {
                    xhr.setRequestHeader('X-WP-Nonce', cpdAdmin.restNonce || cpdAdmin.nonce);
                },
                success: function(response) {
                    if (response.success && response.programme) {
                        var programme = response.programme;
                        openProgrammeModal(programme.id);
                        
                        $('#admin-programme-type').val(programme.programme_type);
                        $('#admin-programme-date').val(programme.programme_date);
                        $('#admin-programme-time').val(programme.programme_time);
                        
                        generateProgrammeFields(programme.programme_type);
                        
                        setTimeout(function() {
                            populateFields(programme.programme_type, JSON.parse(programme.programme_data));
                        }, 100);
                    } else {
                        alert('Failed to load programme details.');
                    }
                },
                error: function() {
                    alert('Error loading programme details.');
                }
            });
        }
        
        // Populate Fields
        function populateFields(type, data) {
            switch (type) {
                case 'jingiaseng_1pm':
                    $('#field-nongiathuh').val(data.nongiathuh_khana_por || '');
                    break;
                    
                case 'miet_balang':
                    $('#field-pule-sdang').val(data.pule_sdang_duwai || '');
                    $('#field-nongkren').val(data.nongkren || '');
                    $('#field-khubor').val(data.khubor || '');
                    break;
                    
                case 'jingiaseng_samla':
                    if (data.special_item) {
                        $('#field-special-item-check').prop('checked', true).trigger('change');
                        $('#field-special-item').val(data.special_item);
                    } else {
                        $('#field-pulesdang').val(data.pulesdang_duwai || '');
                        $('#field-jingainguh').val(data.jingainguh || '');
                        $('#field-special-no').val(data.special_no || '');
                        $('#field-nongkren-samla').val(data.nongkren || '');
                    }
                    break;
                    
                case 'jingiaseng_khynnah':
                    $('#field-jingrwai-iaroh').val(data.jingrwai_iaroh || '');
                    $('#field-nongpule-old').val(data.nongpule_sdang_old_testament || '');
                    $('#field-nongpule-new').val(data.nongpule_sdang_new_testament || '');
                    $('#field-nong-duwai').val(data.nong_duwai || '');
                    $('#field-lum-jingainguh').val(data.lum_jingainguh || '');
                    $('#field-jingrwai-kyrpang').val(data.jingrwai_kyrpang || '');
                    $('#field-duwai-jingainguh').val(data.duwai_jingainguh || '');
                    $('#field-nongkren-activities').val(data.nongkren_activities || '');
                    break;
                    
                case 'jingiaseng_iing':
                    $('#field-zone1-ing').val(data.zone_1?.ing || '');
                    $('#field-zone1-nongpule').val(data.zone_1?.nongpule_duwai || '');
                    $('#field-zone1-nongkren').val(data.zone_1?.nongkren || '');
                    $('#field-zone2-ing').val(data.zone_2?.ing || '');
                    $('#field-zone2-nongpule').val(data.zone_2?.nongpule_duwai || '');
                    $('#field-zone2-nongkren').val(data.zone_2?.nongkren || '');
                    break;
            }
        }
        
        // Delete Programme
        function deleteProgramme(programmeId) {
            if (!confirm('Are you sure you want to delete this programme? This action cannot be undone.')) {
                return;
            }
            
            $.ajax({
                url: cpdAdmin.restUrl + 'editor/programmes/' + programmeId,
                method: 'DELETE',
                beforeSend: function(xhr) {
                    xhr.setRequestHeader('X-WP-Nonce', cpdAdmin.restNonce || cpdAdmin.nonce);
                },
                success: function(response) {
                    if (response.success) {
                        alert('Programme deleted successfully.');
                        loadAdminProgrammes();
                    } else {
                        alert('Error: ' + (response.message || 'Failed to delete programme'));
                    }
                },
                error: function() {
                    alert('An error occurred while deleting the programme.');
                }
            });
        }
        
        // Helper Functions
        function getProgrammeTypeLabel(type) {
            var labels = {
                'jingiaseng_1pm': 'JINGIASENG 1:00 Baje',
                'miet_balang': 'MIET BALANG',
                'jingiaseng_samla': 'JINGIASENG SAMLA',
                'jingiaseng_khynnah': 'JINGIASENG KHYNNAH',
                'jingiaseng_iing': 'JINGIASENG IING'
            };
            return labels[type] || type;
        }
        
        function formatDate(dateString) {
            var date = new Date(dateString + 'T00:00:00');
            var options = { year: 'numeric', month: 'long', day: 'numeric' };
            return date.toLocaleDateString('en-US', options);
        }
        
        function formatTime(timeString) {
            if (!timeString) {
                return 'N/A';
            }

            // If already formatted (contains AM/PM), return as is
            if (timeString.includes('AM') || timeString.includes('PM') || timeString.includes('am') || timeString.includes('pm')) {
                return timeString;
            }

            var parts = timeString.split(':');
            if (parts.length < 2) {
                return timeString; // Return as is if not in expected format
            }

            var hour = parseInt(parts[0]);
            var minute = parts[1];
            var ampm = hour >= 12 ? 'PM' : 'AM';
            var displayHour = hour > 12 ? hour - 12 : (hour === 0 ? 12 : hour);
            return displayHour + ':' + minute + ' ' + ampm;
        }
        
        function generatePreview(programme) {
            try {
                var data = JSON.parse(programme.programme_data);
                var preview = '';
                
                switch (programme.programme_type) {
                    case 'jingiaseng_1pm':
                        preview = data.nongiathuh_khana_por || 'N/A';
                        break;
                    case 'miet_balang':
                        preview = 'PULE SDANG: ' + (data.pule_sdang_duwai || 'N/A');
                        break;
                    case 'jingiaseng_samla':
                        preview = data.special_item ? 'SPECIAL: ' + data.special_item : 'PULESDANG: ' + (data.pulesdang_duwai || 'N/A');
                        break;
                    case 'jingiaseng_khynnah':
                        preview = 'JINGRWAI IAROH: ' + (data.jingrwai_iaroh || 'N/A');
                        break;
                    case 'jingiaseng_iing':
                        preview = 'Zone 1: ' + (data.zone_1?.ing || 'N/A') + ' | Zone 2: ' + (data.zone_2?.ing || 'N/A');
                        break;
                }
                
                return preview;
            } catch (e) {
                return 'Unable to preview';
            }
        }
    });
    
})(jQuery);

