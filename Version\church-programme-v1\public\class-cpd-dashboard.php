<?php
/**
 * Dashboard Class
 * 
 * Handles dashboard page functionality
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

class CPD_Dashboard {
    
    /**
     * Initialize
     */
    public static function init() {
        // Dashboard-specific initialization
    }
    
    /**
     * Get dashboard data
     */
    public static function get_dashboard_data() {
        return array(
            'settings' => self::get_settings(),
            'upcoming_programmes' => self::get_upcoming_programmes(),
        );
    }
    
    /**
     * Get settings
     */
    private static function get_settings() {
        return array(
            'title' => get_option('cpd_dashboard_title'),
            'header_bg_color' => get_option('cpd_header_bg_color'),
            'header_bg_image' => get_option('cpd_header_bg_image'),
            'primary_color' => get_option('cpd_primary_color'),
            'secondary_color' => get_option('cpd_secondary_color'),
            'accent_color' => get_option('cpd_accent_color'),
            'text_color' => get_option('cpd_text_color'),
            'notice_board_enabled' => get_option('cpd_notice_board_enabled'),
            'notice_board_title' => get_option('cpd_notice_board_title'),
            'notice_board_content' => get_option('cpd_notice_board_content'),
        );
    }
    
    /**
     * Get upcoming programmes
     */
    private static function get_upcoming_programmes() {
        $today = current_time('Y-m-d');
        $end_of_week = date('Y-m-d', strtotime($today . ' +7 days'));
        
        return CPD_Database::get_programmes_by_date_range($today, $end_of_week);
    }
}

