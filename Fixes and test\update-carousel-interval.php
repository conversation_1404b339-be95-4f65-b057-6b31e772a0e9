<?php
/**
 * Direct Carousel Interval Updater
 * 
 * Use this script to directly update the carousel auto-interval setting
 * when the admin panel save functionality is not working.
 * 
 * Instructions:
 * 1. Upload this file to your WordPress root directory (same level as wp-config.php)
 * 2. Access it via browser: https://yoursite.com/update-carousel-interval.php
 * 3. Enter the desired interval in milliseconds (e.g., 10000 for 10 seconds)
 * 4. Click "Update Interval"
 * 5. Delete this file after use for security
 */

// Security check
if (!defined('ABSPATH')) {
    // Load WordPress
    require_once('wp-load.php');
}

// Check if user is logged in and has admin permissions
if (!current_user_can('manage_options')) {
    die('You do not have permission to access this page.');
}

$message = '';
$current_interval = get_option('cpd_carousel_auto_interval', '5000');

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['interval'])) {
    $new_interval = intval($_POST['interval']);
    
    // Validate the interval (between 1000ms and 30000ms)
    if ($new_interval >= 1000 && $new_interval <= 30000) {
        $result = update_option('cpd_carousel_auto_interval', $new_interval);
        
        if ($result) {
            $message = "✅ Success! Carousel interval updated to {$new_interval}ms.";
            $current_interval = $new_interval;
        } else {
            $message = "❌ Failed to update the interval. The value might be the same as current.";
        }
    } else {
        $message = "❌ Invalid interval. Please enter a value between 1000 and 30000 milliseconds.";
    }
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Update Carousel Interval</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: #f0f0f1;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #1d2327;
            margin-bottom: 20px;
        }
        .info-box {
            background: #f6f7f7;
            border-left: 4px solid #72aee6;
            padding: 15px;
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
        }
        input[type="number"] {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #8c8f94;
            border-radius: 4px;
            font-size: 16px;
        }
        button {
            background: #2271b1;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background: #135e96;
        }
        .message {
            padding: 10px 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .success {
            background: #d1e7dd;
            border: 1px solid #badbcc;
            color: #0f5132;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f1aeb5;
            color: #721c24;
        }
        .current-value {
            font-size: 18px;
            font-weight: bold;
            color: #2271b1;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Update Carousel Auto-Interval</h1>
        
        <div class="info-box">
            <p><strong>Current Interval:</strong> <span class="current-value"><?php echo esc_html($current_interval); ?>ms</span> (<?php echo esc_html($current_interval / 1000); ?> seconds)</p>
            <p><strong>Recommended Values:</strong></p>
            <ul>
                <li>5000ms = 5 seconds (current default - fast)</li>
                <li>8000ms = 8 seconds (moderate)</li>
                <li>10000ms = 10 seconds (recommended)</li>
                <li>15000ms = 15 seconds (slow)</li>
                <li>20000ms = 20 seconds (very slow)</li>
            </ul>
        </div>

        <?php if ($message): ?>
            <div class="message <?php echo strpos($message, '✅') !== false ? 'success' : 'error'; ?>">
                <?php echo esc_html($message); ?>
            </div>
        <?php endif; ?>

        <form method="post">
            <div class="form-group">
                <label for="interval">New Interval (milliseconds):</label>
                <input type="number" 
                       id="interval" 
                       name="interval" 
                       value="<?php echo esc_attr($current_interval); ?>" 
                       min="1000" 
                       max="30000" 
                       step="1000"
                       required>
                <p style="margin-top: 5px; color: #666; font-size: 14px;">
                    Enter a value between 1000 and 30000 milliseconds (1-30 seconds)
                </p>
            </div>
            
            <button type="submit">Update Interval</button>
        </form>

        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd;">
            <p><strong>Security Note:</strong> Please delete this file after updating the interval.</p>
        </div>
    </div>
</body>
</html>
