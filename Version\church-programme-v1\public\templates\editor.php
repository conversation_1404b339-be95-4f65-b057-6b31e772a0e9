<!DOCTYPE html>
<html <?php language_attributes(); ?>>
<head>
    <meta charset="<?php bloginfo('charset'); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Programme Editor - <?php echo esc_html(get_option('cpd_dashboard_title', 'Church Programme')); ?></title>
    
    <!-- Google Fonts (Open SIL License) -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <link rel="stylesheet" href="<?php echo CPD_PLUGIN_URL; ?>public/css/editor-style.css?v=<?php echo CPD_VERSION; ?>">
</head>
<body class="cpd-editor-body">
    
    <!-- Login Screen -->
    <div class="cpd-login-screen" id="cpd-login-screen">
        <div class="cpd-login-container">
            <div class="cpd-login-header">
                <h1>Programme Editor</h1>
                <p>Please login to continue</p>
            </div>
            
            <form id="cpd-login-form" class="cpd-login-form">
                <div class="cpd-form-group">
                    <label for="login-username">Username</label>
                    <input type="text" id="login-username" name="username" required autocomplete="username">
                </div>
                
                <div class="cpd-form-group">
                    <label for="login-password">Password</label>
                    <input type="password" id="login-password" name="password" required autocomplete="current-password">
                </div>
                
                <div class="cpd-form-error" id="login-error" style="display: none;"></div>
                
                <button type="submit" class="cpd-btn cpd-btn-primary cpd-btn-block">
                    <span class="cpd-btn-text">Login</span>
                    <span class="cpd-btn-loading" style="display: none;">
                        <span class="cpd-spinner-small"></span> Logging in...
                    </span>
                </button>
            </form>
        </div>
    </div>
    
    <!-- Editor Screen -->
    <div class="cpd-editor-screen" id="cpd-editor-screen" style="display: none;">
        
        <!-- Header -->
        <header class="cpd-editor-header">
            <div class="cpd-editor-header-content">
                <h1>Programme Editor</h1>
                <div class="cpd-editor-header-actions">
                    <span class="cpd-user-info" id="cpd-user-info"></span>
                    <button class="cpd-btn cpd-btn-secondary" id="cpd-logout-btn">Logout</button>
                </div>
            </div>
        </header>
        
        <!-- Main Content -->
        <main class="cpd-editor-main">
            
            <!-- Tabs -->
            <div class="cpd-editor-tabs">
                <button class="cpd-tab-btn active" data-tab="ai-extraction">AI Extraction</button>
                <button class="cpd-tab-btn" data-tab="manual-entry">Manual Entry</button>
                <button class="cpd-tab-btn" data-tab="manage-programmes">Manage Programmes</button>
            </div>
            
            <!-- AI Extraction Tab -->
            <div class="cpd-tab-content active" id="tab-ai-extraction">
                <div class="cpd-section">
                    <h2>AI Configuration</h2>
                    <p class="cpd-section-description">
                        Configure your AI provider and API key. Your API key is stored securely in your browser's local storage and never sent to our servers.
                    </p>
                    
                    <div class="cpd-ai-config">
                        <div class="cpd-form-group">
                            <label for="ai-provider">AI Provider</label>
                            <select id="ai-provider" class="cpd-select">
                                <option value="">Select Provider</option>
                                <option value="openrouter">OpenRouter</option>
                                <option value="gemini">Google Gemini</option>
                                <option value="deepseek">DeepSeek</option>
                            </select>
                        </div>
                        
                        <div class="cpd-form-group">
                            <label for="ai-api-key">API Key</label>
                            <input type="password" id="ai-api-key" class="cpd-input" placeholder="Enter your API key">
                            <p class="cpd-help-text">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <circle cx="12" cy="12" r="10"></circle>
                                    <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path>
                                    <line x1="12" y1="17" x2="12.01" y2="17"></line>
                                </svg>
                                Your API key is stored locally in your browser and is never sent to our servers.
                            </p>
                        </div>
                        
                        <button class="cpd-btn cpd-btn-primary" id="fetch-models-btn">
                            <span class="cpd-btn-text">Fetch Available Models</span>
                            <span class="cpd-btn-loading" style="display: none;">
                                <span class="cpd-spinner-small"></span> Fetching...
                            </span>
                        </button>
                        
                        <div class="cpd-form-group" id="model-select-group" style="display: none;">
                            <label for="ai-model">Select Model</label>
                            <select id="ai-model" class="cpd-select">
                                <option value="">Select a model</option>
                            </select>
                            <p class="cpd-help-text">
                                <strong>Recommended Vision Models:</strong> GPT-4 Vision, Claude 3 Opus/Sonnet, Gemini Pro Vision, DeepSeek VL
                            </p>
                        </div>
                    </div>
                </div>
                
                <div class="cpd-section">
                    <h2>Extract Programme Data from Images</h2>
                    <p class="cpd-section-description">
                        Upload images of your church programmes and let AI extract the data automatically.
                    </p>
                    
                    <!-- Programme Type 1: JINGIASENG RANGBAH -->
                    <div class="cpd-extraction-card">
                        <h3>JINGIASENG RANGBAH</h3>
                        <p class="cpd-card-description">
                            Contains two programmes: JINGIASENG 1:00 Baje (1:00 PM) and MIET BALANG (6:30 PM)
                        </p>
                        
                        <div class="cpd-image-upload">
                            <input type="file" id="image-rangbah" accept="image/*" class="cpd-file-input">
                            <label for="image-rangbah" class="cpd-file-label">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                                    <polyline points="17 8 12 3 7 8"></polyline>
                                    <line x1="12" y1="3" x2="12" y2="15"></line>
                                </svg>
                                <span>Choose Image or Drag & Drop</span>
                            </label>
                            <div class="cpd-image-preview" id="preview-rangbah" style="display: none;"></div>
                        </div>
                        
                        <button class="cpd-btn cpd-btn-accent" data-type="jingiaseng_rangbah" data-image="image-rangbah">
                            <span class="cpd-btn-text">Extract Data</span>
                            <span class="cpd-btn-loading" style="display: none;">
                                <span class="cpd-spinner-small"></span> Extracting...
                            </span>
                        </button>
                        
                        <div class="cpd-extraction-result" id="result-rangbah" style="display: none;"></div>
                    </div>
                    
                    <!-- Programme Type 2: JINGIASENG IING -->
                    <div class="cpd-extraction-card">
                        <h3>JINGIASENG IING</h3>
                        <p class="cpd-card-description">
                            Contains two zones with participants (6:30 PM)
                        </p>
                        
                        <div class="cpd-image-upload">
                            <input type="file" id="image-iing" accept="image/*" class="cpd-file-input">
                            <label for="image-iing" class="cpd-file-label">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                                    <polyline points="17 8 12 3 7 8"></polyline>
                                    <line x1="12" y1="3" x2="12" y2="15"></line>
                                </svg>
                                <span>Choose Image or Drag & Drop</span>
                            </label>
                            <div class="cpd-image-preview" id="preview-iing" style="display: none;"></div>
                        </div>
                        
                        <button class="cpd-btn cpd-btn-accent cpd-extract-btn" data-type="jingiaseng_iing" data-image="image-iing">
                            <span class="cpd-btn-text">Extract Data</span>
                            <span class="cpd-btn-loading" style="display: none;">
                                <span class="cpd-spinner-small"></span> Extracting...
                            </span>
                        </button>
                        
                        <div class="cpd-extraction-result" id="result-iing" style="display: none;"></div>
                    </div>
                    
                    <!-- Programme Type 3: JINGIASENG SAMLA -->
                    <div class="cpd-extraction-card">
                        <h3>JINGIASENG SAMLA</h3>
                        <p class="cpd-card-description">
                            Contains programme with 5 columns including special items (6:30 PM)
                        </p>
                        
                        <div class="cpd-image-upload">
                            <input type="file" id="image-samla" accept="image/*" class="cpd-file-input">
                            <label for="image-samla" class="cpd-file-label">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                                    <polyline points="17 8 12 3 7 8"></polyline>
                                    <line x1="12" y1="3" x2="12" y2="15"></line>
                                </svg>
                                <span>Choose Image or Drag & Drop</span>
                            </label>
                            <div class="cpd-image-preview" id="preview-samla" style="display: none;"></div>
                        </div>
                        
                        <button class="cpd-btn cpd-btn-accent cpd-extract-btn" data-type="jingiaseng_samla" data-image="image-samla">
                            <span class="cpd-btn-text">Extract Data</span>
                            <span class="cpd-btn-loading" style="display: none;">
                                <span class="cpd-spinner-small"></span> Extracting...
                            </span>
                        </button>
                        
                        <div class="cpd-extraction-result" id="result-samla" style="display: none;"></div>
                    </div>
                    
                    <!-- Programme Type 4: JINGIASENG KHYNNAH -->
                    <div class="cpd-extraction-card">
                        <h3>JINGIASENG KHYNNAH</h3>
                        <p class="cpd-card-description">
                            Contains programme with 8 columns (3:00 PM)
                        </p>
                        
                        <div class="cpd-image-upload">
                            <input type="file" id="image-khynnah" accept="image/*" class="cpd-file-input">
                            <label for="image-khynnah" class="cpd-file-label">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                                    <polyline points="17 8 12 3 7 8"></polyline>
                                    <line x1="12" y1="3" x2="12" y2="15"></line>
                                </svg>
                                <span>Choose Image or Drag & Drop</span>
                            </label>
                            <div class="cpd-image-preview" id="preview-khynnah" style="display: none;"></div>
                        </div>
                        
                        <button class="cpd-btn cpd-btn-accent cpd-extract-btn" data-type="jingiaseng_khynnah" data-image="image-khynnah">
                            <span class="cpd-btn-text">Extract Data</span>
                            <span class="cpd-btn-loading" style="display: none;">
                                <span class="cpd-spinner-small"></span> Extracting...
                            </span>
                        </button>
                        
                        <div class="cpd-extraction-result" id="result-khynnah" style="display: none;"></div>
                    </div>
                </div>
            </div>
            
            <!-- Manual Entry Tab -->
            <div class="cpd-tab-content" id="tab-manual-entry">
                <div class="cpd-section">
                    <h2>Add Programme Manually</h2>
                    <p class="cpd-section-description">Manually add or edit programme data.</p>
                    
                    <form id="manual-entry-form" class="cpd-manual-form">
                        <div class="cpd-form-row">
                            <div class="cpd-form-group">
                                <label for="manual-type">Programme Type</label>
                                <select id="manual-type" class="cpd-select" required>
                                    <option value="">Select Type</option>
                                    <option value="jingiaseng_1pm">JINGIASENG 1:00 Baje</option>
                                    <option value="miet_balang">MIET BALANG</option>
                                    <option value="jingiaseng_iing">JINGIASENG IING</option>
                                    <option value="jingiaseng_samla">JINGIASENG SAMLA</option>
                                    <option value="jingiaseng_khynnah">JINGIASENG KHYNNAH</option>
                                </select>
                            </div>
                            
                            <div class="cpd-form-group">
                                <label for="manual-date">Date</label>
                                <input type="date" id="manual-date" class="cpd-input" required>
                            </div>
                            
                            <div class="cpd-form-group">
                                <label for="manual-time">Time</label>
                                <input type="time" id="manual-time" class="cpd-input" required>
                            </div>
                        </div>
                        
                        <div id="manual-fields-container">
                            <!-- Dynamic fields will be loaded here based on programme type -->
                        </div>
                        
                        <button type="submit" class="cpd-btn cpd-btn-primary">
                            <span class="cpd-btn-text">Save Programme</span>
                            <span class="cpd-btn-loading" style="display: none;">
                                <span class="cpd-spinner-small"></span> Saving...
                            </span>
                        </button>
                    </form>
                </div>
            </div>
            
            <!-- Manage Programmes Tab -->
            <div class="cpd-tab-content" id="tab-manage-programmes">
                <div class="cpd-section">
                    <h2>Manage Programmes</h2>
                    <p class="cpd-section-description">View, edit, and delete existing programmes.</p>
                    
                    <div class="cpd-filter-bar">
                        <div class="cpd-form-group">
                            <label for="filter-type">Filter by Type</label>
                            <select id="filter-type" class="cpd-select">
                                <option value="">All Types</option>
                                <option value="jingiaseng_1pm">JINGIASENG 1:00 Baje</option>
                                <option value="miet_balang">MIET BALANG</option>
                                <option value="jingiaseng_iing">JINGIASENG IING</option>
                                <option value="jingiaseng_samla">JINGIASENG SAMLA</option>
                                <option value="jingiaseng_khynnah">JINGIASENG KHYNNAH</option>
                            </select>
                        </div>
                        
                        <div class="cpd-form-group">
                            <label for="filter-start-date">Start Date</label>
                            <input type="date" id="filter-start-date" class="cpd-input">
                        </div>
                        
                        <div class="cpd-form-group">
                            <label for="filter-end-date">End Date</label>
                            <input type="date" id="filter-end-date" class="cpd-input">
                        </div>
                        
                        <button class="cpd-btn cpd-btn-primary" id="apply-filter-btn">Apply Filter</button>
                    </div>
                    
                    <div id="programmes-list" class="cpd-programmes-list">
                        <!-- Programmes will be loaded here -->
                    </div>
                </div>
            </div>
            
        </main>
        
    </div>
    
    <script>
        window.cpdEditor = {
            restUrl: '<?php echo rest_url('cpd/v1/'); ?>',
            nonce: '<?php echo wp_create_nonce('wp_rest'); ?>'
        };
    </script>
    
    <script src="<?php echo CPD_PLUGIN_URL; ?>public/js/editor-script.js?v=<?php echo CPD_VERSION; ?>"></script>
    
</body>
</html>

