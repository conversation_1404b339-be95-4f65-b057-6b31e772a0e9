# 🚨 CRITICAL FIXES APPLIED - MUST UPLOAD TO SERVER

## Date: 2025-09-30

---

## ⚠️ IMPORTANT: YOU MUST UPLOAD THESE FILES

The fixes have been applied to your LOCAL files, but **your server still has the old versions**. That's why you're still seeing errors!

---

## 🔧 FILES THAT MUST BE UPLOADED (7 Files)

### 1. **admin/class-cpd-admin.php**
**Fix:** Cache busting + REST nonce
- Added file modification time for cache busting
- Added REST API nonce to localized scripts
- This fixes the JavaScript caching issue

### 2. **admin/js/admin-programmes.js**
**Fix:** Form reset error
- Added existence check before calling `reset()`
- Prevents "Cannot read properties of undefined" error

### 3. **includes/class-cpd-rest-api.php**
**Fix:** 403 errors
- Added WordPress admin permission check
- Added CORS headers for subdomain access
- Allows admins to access REST API

### 4. **includes/class-cpd-subdomain.php**
**Fix:** CORS and theme loading
- Added CORS headers
- Prevented theme from loading on subdomains
- Removed all theme actions

### 5. **includes/class-cpd-ai.php**
**Fix:** OpenRouter "No vision models found"
- Improved model detection (3 methods)
- Added fallback checks for vision models
- Checks model ID, name, and architecture

### 6. **admin/views/settings-page.php**
**Fix:** Added test buttons
- Test buttons for temporary URLs
- Warning box with fallback URLs

### 7. **database-check.php** (NEW FILE)
**Purpose:** Database verification and repair tool
- Checks if tables exist
- Shows programme count
- Can repair/create missing tables

---

## 🎯 ROOT CAUSES IDENTIFIED

### Issue 1: Browser Cache
**Problem:** Browser is loading old JavaScript files  
**Evidence:** Error at line 140 in old file, but new file has different line numbers  
**Solution:** Cache busting with file modification time

### Issue 2: Database Tables Not Created
**Problem:** Plugin activation didn't create tables  
**Evidence:** "No programmes found" even after adding data  
**Solution:** Use database-check.php to verify and repair

### Issue 3: OpenRouter Vision Models
**Problem:** Too strict filtering for vision models  
**Evidence:** "No vision models found" error  
**Solution:** Added 3 detection methods (architecture, ID, name)

### Issue 4: REST API 403 Errors
**Problem:** Permission check failing for WordPress admins  
**Evidence:** 403 errors in console  
**Solution:** Added `current_user_can('manage_options')` check

---

## 📤 HOW TO UPLOAD (Choose One Method)

### Method 1: FTP/SFTP (RECOMMENDED)

1. **Connect to server** using FTP client
2. **Navigate to:** `/public_html/wp-content/plugins/church-programme-dashboard/`
3. **Upload these 7 files** (overwrite existing):
   ```
   admin/class-cpd-admin.php
   admin/js/admin-programmes.js
   admin/views/settings-page.php
   includes/class-cpd-rest-api.php
   includes/class-cpd-subdomain.php
   includes/class-cpd-ai.php
   database-check.php (new file in root)
   ```

### Method 2: cPanel File Manager

1. **Log in to cPanel**
2. **Open File Manager**
3. **Navigate to:** `public_html/wp-content/plugins/church-programme-dashboard/`
4. **Upload each file** (confirm overwrite)

### Method 3: Full Plugin Re-upload

1. **Zip the entire plugin folder**
2. **Go to:** Plugins > Add New > Upload Plugin
3. **Upload ZIP** and replace current version

---

## ✅ VERIFICATION STEPS (CRITICAL!)

### Step 1: Upload Verification
**URL:** `https://jermesa.com/wp-content/plugins/church-programme-dashboard/version-check.php`

**Expected Result:**
```
✅ All Required Files Found
✅ REST API Fix: Applied
✅ Admin JavaScript Fix: Applied
✅ Subdomain Fallback Fix: Applied
✅ All Fixes Applied Successfully!
```

### Step 2: Database Verification
**URL:** `https://jermesa.com/wp-content/plugins/church-programme-dashboard/database-check.php`

**Expected Result:**
```
✅ All Required Tables Exist
✅ Programmes Found: X total programmes
```

**If tables are missing:**
1. Click "🔧 Repair Database" button
2. Wait for completion
3. Refresh page

### Step 3: Clear ALL Caches

**Browser Cache:**
- Press `Ctrl+Shift+Delete` (Windows) or `Cmd+Shift+Delete` (Mac)
- Select "Cached images and files"
- Click "Clear data"

**Or use Incognito/Private mode:**
- Windows: `Ctrl+Shift+N`
- Mac: `Cmd+Shift+N`

**WordPress Cache:**
- If using cache plugin, clear it
- Go to Settings > Permalinks > Save Changes

### Step 4: Test Programme Management

1. **Go to:** Church Programme > Settings > Manage Programmes
2. **Click:** "+ Add New Programme"
3. **Expected:** Modal opens, no console errors
4. **Check Console:** Should show no 403 errors

### Step 5: Test AI Extraction

1. **Go to:** `https://jermesa.com/cpd-editor/`
2. **Login** as editor or WordPress admin
3. **Select:** OpenRouter as provider
4. **Enter:** Your API key
5. **Click:** "Fetch Models"
6. **Expected:** List of vision models appears

---

## 🐛 TROUBLESHOOTING

### Still Seeing JavaScript Error?

**Error:**
```
admin-programmes.js?ver=1.0.0:140 Uncaught TypeError: Cannot read properties of undefined (reading 'reset')
```

**This means:** Old file still cached

**Solutions:**
1. Check version-check.php shows recent modification date
2. Hard refresh: `Ctrl+F5` (Windows) or `Cmd+Shift+R` (Mac)
3. Try incognito mode
4. Check browser console for file version number

**How to check file version:**
1. Open browser console (F12)
2. Go to Network tab
3. Refresh page
4. Find `admin-programmes.js`
5. Check the `?ver=` parameter - should be a timestamp, not "1.0.0"

### Still Seeing 403 Errors?

**Error:**
```
/wp-json/cpd/v1/editor/programmes:1 Failed to load resource: the server responded with a status of 403 ()
```

**This means:** REST API file not updated

**Solutions:**
1. Verify `includes/class-cpd-rest-api.php` is uploaded
2. Check version-check.php shows REST API fix ✅
3. Log out and log back in to WordPress admin
4. Clear all caches
5. Check if you're logged in as admin

### Still Seeing "No Vision Models Found"?

**Error:**
```
No vision models found
```

**This means:** AI file not updated OR invalid API key

**Solutions:**
1. Verify `includes/class-cpd-ai.php` is uploaded
2. Check version-check.php shows file is recent
3. Verify your OpenRouter API key is correct
4. Check if you have credits in OpenRouter account
5. Try a different provider (Gemini or DeepSeek)

### Still Seeing "No Programmes Found"?

**Error:**
```
No programmes found
```

**This means:** Database tables don't exist OR no data added

**Solutions:**
1. Visit database-check.php
2. Check if tables exist
3. If missing, click "Repair Database"
4. Try adding a programme manually
5. Check if data appears in database-check.php

---

## 🧪 TESTING CHECKLIST

After uploading files, test in this order:

- [ ] Visit version-check.php - all green ✅
- [ ] Visit database-check.php - tables exist ✅
- [ ] Clear browser cache completely
- [ ] Clear WordPress cache (if applicable)
- [ ] Flush permalinks (Settings > Permalinks > Save)
- [ ] Test in incognito mode
- [ ] Go to Church Programme > Settings
- [ ] Click "Manage Programmes" tab
- [ ] Click "+ Add New Programme"
- [ ] Modal opens without errors ✅
- [ ] Console shows no 403 errors ✅
- [ ] Go to cpd-editor page
- [ ] Select OpenRouter provider
- [ ] Enter API key
- [ ] Click "Fetch Models"
- [ ] Models list appears ✅
- [ ] Try extracting from an image
- [ ] Data appears in form ✅
- [ ] Save programme
- [ ] Programme appears in list ✅

---

## 📊 EXPECTED RESULTS AFTER UPLOAD

### Version Check Page:
```
✅ All Required Files Found
✅ REST API Fix: Applied (WordPress admin permission check present)
✅ Admin JavaScript Fix: Applied (Form existence check present)
✅ Subdomain Fallback Fix: Applied (Path fallback handler present)
✅ AI Model Detection Fix: Applied (Enhanced vision model detection)
✅ Cache Busting Fix: Applied (File modification time versioning)
✅ All Fixes Applied Successfully!

File Modification Dates:
- admin/class-cpd-admin.php: 2025-09-30 21:xx:xx
- admin/js/admin-programmes.js: 2025-09-30 21:xx:xx
- includes/class-cpd-rest-api.php: 2025-09-30 21:xx:xx
- includes/class-cpd-subdomain.php: 2025-09-30 21:xx:xx
- includes/class-cpd-ai.php: 2025-09-30 21:xx:xx
```

### Database Check Page:
```
✅ All Required Tables Exist
✅ Programmes Found: X total programmes
✅ Editor Users Found: X users
```

### Browser Console:
```
(No errors)
```

### Programme Management:
- Modal opens smoothly
- No JavaScript errors
- No 403 errors
- Can add/edit/delete programmes

### AI Extraction:
- OpenRouter shows vision models
- Can fetch models successfully
- Can extract data from images
- Data populates form correctly

---

## 🆘 IF STILL NOT WORKING

### Take These Screenshots:

1. **version-check.php page** (full page)
2. **database-check.php page** (full page)
3. **Browser console** (F12 > Console tab)
4. **Network tab** (F12 > Network tab, filter: XHR)
5. **Programme management page** (with modal open)

### Provide This Information:

- Which step failed?
- What error messages do you see?
- Are you logged in as WordPress admin?
- Which browser and version?
- Did you clear cache?
- Did you try incognito mode?
- What does version-check.php show?
- What does database-check.php show?

---

## 📞 SUPPORT CHECKLIST

Before asking for help, ensure:

- [ ] All 7 files uploaded to server
- [ ] version-check.php shows all green ✅
- [ ] database-check.php shows tables exist
- [ ] Browser cache cleared (tried incognito)
- [ ] WordPress cache cleared
- [ ] Permalinks flushed
- [ ] Logged in as WordPress admin
- [ ] Tested in different browser
- [ ] Screenshots taken
- [ ] Error messages noted

---

## 🎯 SUMMARY

**What was wrong:**
1. ❌ Browser cached old JavaScript files
2. ❌ Database tables not created
3. ❌ OpenRouter model detection too strict
4. ❌ REST API permission check failing

**What was fixed:**
1. ✅ Added cache busting with file modification time
2. ✅ Created database repair tool
3. ✅ Enhanced vision model detection (3 methods)
4. ✅ Added WordPress admin permission check
5. ✅ Added CORS headers
6. ✅ Added form existence check
7. ✅ Added REST API nonce

**What you need to do:**
1. 📤 Upload 7 files to server
2. ✅ Visit version-check.php to verify
3. ✅ Visit database-check.php to verify/repair
4. 🧹 Clear ALL caches
5. 🧪 Test everything

---

**Remember:** The fixes are in your LOCAL files. You MUST upload them to your server!

**Quick Verification:** Visit version-check.php first - it will tell you exactly what's uploaded and what's not.

---

**Plugin Version:** 1.0.0  
**Fixes Date:** 2025-09-30  
**Files Modified:** 6  
**Files Added:** 1  
**Upload Required:** YES ⚠️

