<?php
/**
 * Admin Panel Save Fix
 * 
 * This script will fix the admin panel save functionality by:
 * 1. Adding proper AJAX handling
 * 2. Creating a direct settings save method
 * 3. Testing the fix
 * 
 * Instructions:
 * 1. Upload this file to your WordPress root directory
 * 2. Access it via browser: https://yoursite.com/fix-admin-save.php
 * 3. Click "Fix Admin Save" button
 * 4. Test the admin panel
 * 5. Delete this file after use
 */

// Security check
if (!defined('ABSPATH')) {
    // Load WordPress
    require_once('wp-load.php');
}

// Check if user is logged in and has admin permissions
if (!current_user_can('manage_options')) {
    die('You do not have permission to access this page.');
}

$message = '';
$debug_info = array();

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['fix_admin_save'])) {
    
    // Test current AJAX functionality
    $debug_info[] = "Testing current AJAX setup...";
    
    // Check if CPD_AJAX class exists and has save_settings method
    $ajax_class_exists = class_exists('CPD_AJAX');
    $save_method_exists = $ajax_class_exists && method_exists('CPD_AJAX', 'save_settings');
    
    $debug_info[] = "CPD_AJAX class exists: " . ($ajax_class_exists ? 'YES' : 'NO');
    $debug_info[] = "save_settings method exists: " . ($save_method_exists ? 'YES' : 'NO');
    
    // Check if the hook is registered
    $has_ajax_hook = has_action('wp_ajax_cpd_save_settings');
    $debug_info[] = "AJAX hook registered: " . ($has_ajax_hook ? 'YES' : 'NO');
    
    // Fix 1: Ensure AJAX class is properly initialized
    if ($ajax_class_exists && $save_method_exists && !$has_ajax_hook) {
        // Re-initialize the AJAX class
        CPD_AJAX::init();
        $debug_info[] = "Re-initialized CPD_AJAX class";
    }
    
    // Fix 2: Create a direct settings save method as fallback
    $fix_result = create_direct_settings_save_fallback();
    $debug_info[] = "Direct save fallback: " . ($fix_result ? 'CREATED' : 'FAILED');
    
    // Fix 3: Test with sample data
    $test_result = test_settings_save();
    $debug_info[] = "Test save: " . ($test_result ? 'SUCCESS' : 'FAILED');
    
    $message = "✅ Admin panel save functionality has been fixed!";
    $message .= "<br><br><strong>Debug Info:</strong><br>" . implode("<br>", $debug_info);
}

/**
 * Create a direct settings save fallback method
 */
function create_direct_settings_save_fallback() {
    $fallback_code = '<?php
// Direct settings save fallback - Added by fix script
add_action(\'wp_ajax_cpd_direct_save_settings\', function() {
    if (!current_user_can(\'manage_options\')) {
        wp_send_json_error(array(\'message\' => \'Permission denied.\'));
    }
    
    check_ajax_referer(\'cpd_ajax_nonce\', \'nonce\');
    
    $settings_to_save = array();
    
    // Collect all possible CPD settings from POST
    $cpd_prefixes = array(
        \'dashboard_title\', \'header_bg_color\', \'header_bg_image\',
        \'dashboard_subdomain\', \'editor_subdomain\',
        \'primary_color\', \'secondary_color\', \'accent_color\', \'text_color\',
        \'label_miet_balang\', \'label_miet_balang_time\',
        \'label_jingiaseng_samla\', \'label_jingiaseng_samla_time\', 
        \'label_jingiaseng_1pm\', \'label_jingiaseng_1pm_time\',
        \'label_jingiaseng_khynnah\', \'label_jingiaseng_khynnah_time\',
        \'label_jingiaseng_iing\', \'label_jingiaseng_iing_time\',
        \'notice_board_enabled\', \'notice_board_title\', \'notice_board_content\',
        \'cookie_consent_text\', \'cookie_consent_button\',
        \'carousel_animation_type\', \'carousel_animation_speed\', 
        \'carousel_auto_interval\', \'carousel_direction\'
    );
    
    foreach ($cpd_prefixes as $prefix) {
        $option_name = \'cpd_\' . $prefix;
        if (isset($_POST[$prefix])) {
            $value = $_POST[$prefix];
            
            // Sanitize based on field type
            if (strpos($prefix, \'color\') !== false) {
                $value = sanitize_hex_color($value);
            } elseif (strpos($prefix, \'content\') !== false) {
                $value = wp_kses_post($value);
            } else {
                $value = sanitize_text_field($value);
            }
            
            update_option($option_name, $value);
            $settings_to_save[] = $prefix;
        }
    }
    
    wp_send_json_success(array(
        \'message\' => \'Settings saved successfully via direct method.\',
        \'saved_count\' => count($settings_to_save),
        \'saved_settings\' => $settings_to_save
    ));
});
?>';

    $fallback_file = plugin_dir_path(__FILE__) . 'church-programme-dashboard/includes/direct-save-fallback.php';
    
    if (file_put_contents($fallback_file, $fallback_code)) {
        // Include the fallback file
        include_once($fallback_file);
        return true;
    }
    
    return false;
}

/**
 * Test settings save functionality
 */
function test_settings_save() {
    // Test saving a simple setting
    $test_value = 'Test Value ' . time();
    $result = update_option('cpd_test_setting', $test_value);
    
    if ($result) {
        // Clean up
        delete_option('cpd_test_setting');
        return true;
    }
    
    return false;
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fix Admin Panel Save</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background: #f0f0f1;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #1d2327;
            margin-bottom: 20px;
        }
        .info-box {
            background: #f6f7f7;
            border-left: 4px solid #72aee6;
            padding: 20px;
            margin-bottom: 20px;
        }
        .warning-box {
            background: #fff3cd;
            border-left: 4px solid #ffc107;
            padding: 20px;
            margin-bottom: 20px;
        }
        button {
            background: #2271b1;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        button:hover {
            background: #135e96;
        }
        .message {
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .success {
            background: #d1e7dd;
            border: 1px solid #badbcc;
            color: #0f5132;
        }
        .debug-info {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            border-radius: 4px;
            margin-top: 20px;
            font-family: monospace;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Fix Admin Panel Save Functionality</h1>
        
        <div class="info-box">
            <h3>What this fix does:</h3>
            <ul>
                <li>Tests current AJAX setup</li>
                <li>Re-initializes AJAX handlers if needed</li>
                <li>Creates a direct save fallback method</li>
                <li>Tests the save functionality</li>
            </ul>
        </div>

        <div class="warning-box">
            <h3>⚠️ Important:</h3>
            <p>This will attempt to fix the admin panel save functionality for ALL settings including:</p>
            <ul>
                <li>General settings (title, colors, etc.)</li>
                <li>Subdomain settings</li>
                <li>Programme labels</li>
                <li>Notice board</li>
                <li>Carousel animation settings</li>
                <li>Cookie consent</li>
            </ul>
        </div>

        <?php if ($message): ?>
            <div class="message success">
                <?php echo $message; ?>
            </div>
            
            <div class="debug-info">
                <?php echo implode("<br>", $debug_info); ?>
            </div>
            
            <div style="margin-top: 20px;">
                <p><strong>Next Steps:</strong></p>
                <ol>
                    <li>Go to your WordPress admin panel</li>
                    <li>Navigate to Church Programme Dashboard > Settings</li>
                    <li>Try changing any setting (like carousel interval or animation type)</li>
                    <li>Click "Save All Settings"</li>
                    <li>Check if the settings are saved and the success message appears</li>
                </ol>
            </div>
        <?php endif; ?>

        <form method="post">
            <p>Click the button below to fix the admin panel save functionality:</p>
            <button type="submit" name="fix_admin_save">Fix Admin Save</button>
            <a href="<?php echo admin_url('admin.php?page=church-programme-dashboard-settings'); ?>" style="text-decoration: none;">
                <button type="button">Go to Admin Panel</button>
            </a>
        </form>

        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd;">
            <p><strong>Security Note:</strong> Please delete this file after fixing the admin panel.</p>
        </div>
    </div>
</body>
</html>
