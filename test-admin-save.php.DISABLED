<?php
/**
 * Admin Save Settings Debug Test
 * 
 * This file helps debug the admin settings save functionality
 * 
 * Usage: Access this file directly in your browser:
 * http://yourdomain.com/wp-content/plugins/church-programme-dashboard/test-admin-save.php
 */

// Load WordPress
require_once('../../../wp-load.php');

// Check if user is admin
if (!current_user_can('manage_options')) {
    die('You must be an administrator to access this page.');
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>Admin Save Settings Debug Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
            max-width: 1200px;
            margin: 40px auto;
            padding: 20px;
            background: #f0f0f1;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin-bottom: 20px;
            border-radius: 5px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        h1 {
            color: #1d2327;
            border-bottom: 2px solid #2271b1;
            padding-bottom: 10px;
        }
        h2 {
            color: #2271b1;
            margin-top: 0;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        pre {
            background: #f6f7f7;
            padding: 15px;
            border-radius: 3px;
            overflow-x: auto;
            border: 1px solid #ddd;
        }
        button {
            background: #2271b1;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
        }
        button:hover {
            background: #135e96;
        }
        button.secondary {
            background: #6c757d;
        }
        button.secondary:hover {
            background: #5a6268;
        }
        #console-log {
            max-height: 400px;
            overflow-y: auto;
        }
        .log-entry {
            padding: 5px;
            margin: 2px 0;
            border-left: 3px solid #2271b1;
            background: #f9f9f9;
        }
        .log-entry.error {
            border-left-color: #dc3545;
            background: #fff5f5;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        table th, table td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        table th {
            background: #f6f7f7;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <h1>🔍 Admin Save Settings Debug Test</h1>

    <!-- Test 1: Check AJAX Handler Registration -->
    <div class="test-section">
        <h2>Test 1: AJAX Handler Registration</h2>
        <?php
        $ajax_actions = array(
            'cpd_save_settings',
            'cpd_upload_image',
            'cpd_create_editor_user',
            'cpd_delete_editor_user',
            'cpd_get_editor_users'
        );
        
        echo '<table>';
        echo '<tr><th>Action</th><th>Status</th></tr>';
        foreach ($ajax_actions as $action) {
            $has_action = has_action('wp_ajax_' . $action);
            $status = $has_action ? 'success' : 'error';
            $text = $has_action ? '✓ Registered' : '✗ Not Registered';
            echo '<tr><td>' . esc_html($action) . '</td><td class="status ' . $status . '">' . $text . '</td></tr>';
        }
        echo '</table>';
        ?>
    </div>

    <!-- Test 2: Check Class Initialization -->
    <div class="test-section">
        <h2>Test 2: Class Initialization</h2>
        <?php
        $classes = array(
            'CPD_AJAX' => 'AJAX Handler',
            'CPD_Admin' => 'Admin Panel',
            'CPD_Admin_Settings' => 'Admin Settings',
            'CPD_Database' => 'Database',
            'CPD_REST_API' => 'REST API',
            'CPD_Auth' => 'Authentication',
            'CPD_Subdomain' => 'Subdomain Handler'
        );
        
        echo '<table>';
        echo '<tr><th>Class</th><th>Description</th><th>Status</th></tr>';
        foreach ($classes as $class => $desc) {
            $exists = class_exists($class);
            $status = $exists ? 'success' : 'error';
            $text = $exists ? '✓ Loaded' : '✗ Not Loaded';
            echo '<tr><td>' . esc_html($class) . '</td><td>' . esc_html($desc) . '</td><td class="status ' . $status . '">' . $text . '</td></tr>';
        }
        echo '</table>';
        ?>
    </div>

    <!-- Test 3: Check Current Settings -->
    <div class="test-section">
        <h2>Test 3: Current Settings in Database</h2>
        <?php
        $settings_keys = array(
            'cpd_dashboard_title',
            'cpd_primary_color',
            'cpd_secondary_color',
            'cpd_notice_board_enabled',
            'cpd_notice_board_title',
            'cpd_cookie_consent_text',
            'cpd_carousel_animation_type'
        );
        
        echo '<table>';
        echo '<tr><th>Setting Key</th><th>Current Value</th></tr>';
        foreach ($settings_keys as $key) {
            $value = get_option($key);
            $display_value = $value === false ? '<em>Not Set</em>' : esc_html($value);
            echo '<tr><td>' . esc_html($key) . '</td><td>' . $display_value . '</td></tr>';
        }
        echo '</table>';
        ?>
    </div>

    <!-- Test 4: Test AJAX Endpoint -->
    <div class="test-section">
        <h2>Test 4: Live AJAX Save Test</h2>
        <p>This test will attempt to save a test setting via AJAX:</p>
        
        <button id="test-save-btn">Run Save Test</button>
        <button id="clear-log-btn" class="secondary">Clear Log</button>
        
        <div id="test-result" style="margin-top: 20px;"></div>
        <div id="console-log"></div>
    </div>

    <!-- Test 5: Check JavaScript Loading -->
    <div class="test-section">
        <h2>Test 5: JavaScript & Dependencies</h2>
        <div id="js-check"></div>
    </div>

    <!-- Test 6: Network Request Monitor -->
    <div class="test-section">
        <h2>Test 6: Network Request Details</h2>
        <p>Check browser console (F12) for detailed network information.</p>
        <div id="network-info"></div>
    </div>

    <script src="<?php echo includes_url('js/jquery/jquery.min.js'); ?>"></script>
    <script>
    jQuery(document).ready(function($) {
        
        // Console logging
        function addLog(message, type = 'info') {
            const logEntry = $('<div class="log-entry ' + type + '"></div>');
            logEntry.text(new Date().toLocaleTimeString() + ': ' + message);
            $('#console-log').prepend(logEntry);
        }

        // Clear log
        $('#clear-log-btn').on('click', function() {
            $('#console-log').empty();
            addLog('Log cleared');
        });

        // Check JavaScript dependencies
        function checkJavaScript() {
            const checks = {
                'jQuery': typeof jQuery !== 'undefined',
                'jQuery.ajax': typeof jQuery.ajax !== 'undefined',
                'cpdAdmin object': typeof cpdAdmin !== 'undefined',
                'cpdAdmin.ajaxUrl': typeof cpdAdmin !== 'undefined' && cpdAdmin.ajaxUrl,
                'cpdAdmin.nonce': typeof cpdAdmin !== 'undefined' && cpdAdmin.nonce
            };

            let html = '<table><tr><th>Check</th><th>Status</th></tr>';
            for (let check in checks) {
                const status = checks[check] ? 'success' : 'error';
                const text = checks[check] ? '✓ Available' : '✗ Missing';
                html += '<tr><td>' + check + '</td><td class="status ' + status + '">' + text + '</td></tr>';
            }
            html += '</table>';

            if (typeof cpdAdmin !== 'undefined') {
                html += '<pre>cpdAdmin object:\n' + JSON.stringify(cpdAdmin, null, 2) + '</pre>';
            }

            $('#js-check').html(html);
        }

        checkJavaScript();

        // Test AJAX save
        $('#test-save-btn').on('click', function() {
            const button = $(this);
            button.prop('disabled', true).text('Testing...');
            
            $('#test-result').html('<div class="status info">Running test...</div>');
            addLog('Starting AJAX save test');

            // Prepare test data
            const testData = {
                action: 'cpd_save_settings',
                nonce: '<?php echo wp_create_nonce('cpd_ajax_nonce'); ?>',
                settings: {
                    'test_setting': 'test_value_' + Date.now(),
                    'dashboard_title': 'Test Dashboard Title',
                    'primary_color': '#ff0000'
                }
            };

            addLog('Sending request to: <?php echo admin_url('admin-ajax.php'); ?>');
            addLog('Request data: ' + JSON.stringify(testData, null, 2));

            $.ajax({
                url: '<?php echo admin_url('admin-ajax.php'); ?>',
                type: 'POST',
                data: testData,
                beforeSend: function(xhr) {
                    addLog('Request headers being sent...');
                },
                success: function(response) {
                    addLog('Response received: ' + JSON.stringify(response, null, 2));
                    
                    let resultHtml = '<div class="status ';
                    if (response.success) {
                        resultHtml += 'success">✓ SUCCESS: ' + (response.data.message || 'Settings saved');
                        addLog('Save successful!', 'success');
                    } else {
                        resultHtml += 'error">✗ FAILED: ' + (response.data.message || 'Unknown error');
                        addLog('Save failed: ' + (response.data.message || 'Unknown error'), 'error');
                    }
                    resultHtml += '</div>';
                    resultHtml += '<pre>Full Response:\n' + JSON.stringify(response, null, 2) + '</pre>';
                    
                    $('#test-result').html(resultHtml);
                },
                error: function(xhr, status, error) {
                    addLog('AJAX Error: ' + error, 'error');
                    addLog('Status: ' + status, 'error');
                    addLog('Response Text: ' + xhr.responseText, 'error');
                    
                    let resultHtml = '<div class="status error">✗ AJAX ERROR</div>';
                    resultHtml += '<pre>Status: ' + status + '\n';
                    resultHtml += 'Error: ' + error + '\n';
                    resultHtml += 'HTTP Status: ' + xhr.status + '\n';
                    resultHtml += 'Response: ' + xhr.responseText + '</pre>';
                    
                    $('#test-result').html(resultHtml);
                },
                complete: function() {
                    button.prop('disabled', false).text('Run Save Test');
                    addLog('Test complete');
                }
            });
        });

        // Monitor console errors
        window.onerror = function(msg, url, lineNo, columnNo, error) {
            addLog('JavaScript Error: ' + msg + ' at ' + url + ':' + lineNo, 'error');
            return false;
        };

        addLog('Debug page loaded and ready');
    });
    </script>
</body>
</html>

