<?php
/**
 * Direct Notice Board Settings Updater
 * 
 * Use this script to directly update Notice Board settings in the database
 * with a rich text editor similar to WordPress.
 * 
 * Instructions:
 * 1. Upload this file to your WordPress root directory (same level as wp-config.php)
 * 2. Access it via browser: https://yoursite.com/update-notice-board-settings.php
 * 3. Configure your notice board settings and click "Update Settings"
 * 4. Delete this file after use for security
 */

// Security check
if (!defined('ABSPATH')) {
    // Load WordPress
    require_once('wp-load.php');
}

// Check if user is logged in and has admin permissions
if (!current_user_can('manage_options')) {
    die('You do not have permission to access this page.');
}

// Load WordPress editor scripts
wp_enqueue_script('jquery');
wp_enqueue_script('editor');
wp_enqueue_script('quicktags');
wp_enqueue_script('media-upload');
wp_enqueue_style('buttons');
wp_enqueue_style('editor-buttons');

$message = '';
$current_settings = array(
    'enabled' => get_option('cpd_notice_board_enabled', '0'),
    'title' => get_option('cpd_notice_board_title', 'Notice Board'),
    'content' => get_option('cpd_notice_board_content', '')
);

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_settings'])) {
    $new_settings = array(
        'enabled' => isset($_POST['enabled']) ? '1' : '0',
        'title' => sanitize_text_field($_POST['title']),
        'content' => wp_kses_post($_POST['content'])
    );
    
    // Update all settings
    update_option('cpd_notice_board_enabled', $new_settings['enabled']);
    update_option('cpd_notice_board_title', $new_settings['title']);
    update_option('cpd_notice_board_content', $new_settings['content']);
    
    $current_settings = $new_settings;
    $message = "✅ Success! Notice Board settings have been updated.";
}

// Get WordPress editor settings
$editor_id = 'notice_board_content';
$editor_settings = array(
    'textarea_name' => 'content',
    'textarea_rows' => 15,
    'media_buttons' => true,
    'tinymce' => array(
        'toolbar1' => 'formatselect,bold,italic,bullist,numlist,blockquote,alignleft,aligncenter,alignright,link,unlink,wp_adv',
        'toolbar2' => 'strikethrough,hr,forecolor,pastetext,removeformat,charmap,outdent,indent,undo,redo,wp_help'
    ),
    'quicktags' => true
);

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Update Notice Board Settings</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f0f0f1;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #1d2327;
            margin-bottom: 20px;
        }
        .info-box {
            background: #f6f7f7;
            border-left: 4px solid #72aee6;
            padding: 20px;
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 25px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            font-size: 14px;
        }
        input[type="text"], select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #8c8f94;
            border-radius: 4px;
            font-size: 16px;
        }
        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .checkbox-group input[type="checkbox"] {
            width: auto;
        }
        button {
            background: #2271b1;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        button:hover {
            background: #135e96;
        }
        .message {
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .success {
            background: #d1e7dd;
            border: 1px solid #badbcc;
            color: #0f5132;
        }
        .current-settings {
            background: #fff3cd;
            border-left: 4px solid #ffc107;
            padding: 15px;
            margin-bottom: 20px;
        }
        .editor-container {
            border: 1px solid #c3c4c7;
            border-radius: 4px;
            overflow: hidden;
        }
        .wp-editor-tools {
            background: #f6f7f7;
            padding: 10px;
            border-bottom: 1px solid #c3c4c7;
        }
        .wp-editor-container {
            background: white;
        }
        .setting-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 15px;
        }
        @media (max-width: 768px) {
            .setting-row {
                grid-template-columns: 1fr;
            }
        }
        .preview-box {
            background: #f9f9f9;
            border: 1px solid #e2e8f0;
            border-radius: 4px;
            padding: 15px;
            margin-top: 10px;
        }
        .preview-box h4 {
            margin-top: 0;
            color: #4a5568;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Update Notice Board Settings</h1>
        
        <div class="info-box">
            <h3>Current Settings:</h3>
            <div class="setting-row">
                <div><strong>Enabled:</strong> <?php echo $current_settings['enabled'] === '1' ? 'Yes' : 'No'; ?></div>
                <div><strong>Title:</strong> <?php echo esc_html($current_settings['title']); ?></div>
            </div>
            <?php if ($current_settings['enabled'] === '1' && !empty($current_settings['content'])): ?>
                <div style="margin-top: 15px;">
                    <strong>Current Content Preview:</strong>
                    <div class="preview-box">
                        <?php echo wp_kses_post(wpautop($current_settings['content'])); ?>
                    </div>
                </div>
            <?php endif; ?>
        </div>

        <?php if ($message): ?>
            <div class="message success">
                <?php echo esc_html($message); ?>
            </div>
        <?php endif; ?>

        <form method="post">
            <div class="setting-row">
                <div class="form-group">
                    <div class="checkbox-group">
                        <input type="checkbox" id="enabled" name="enabled" value="1" <?php checked($current_settings['enabled'], '1'); ?>>
                        <label for="enabled" style="display: inline; margin-bottom: 0;">Enable Notice Board</label>
                    </div>
                    <p style="margin-top: 5px; color: #666; font-size: 14px;">
                        Show the notice board on the dashboard
                    </p>
                </div>
                
                <div class="form-group">
                    <label for="title">Notice Board Title:</label>
                    <input type="text" id="title" name="title" 
                           value="<?php echo esc_attr($current_settings['title']); ?>" 
                           placeholder="Enter notice board title">
                    <p style="margin-top: 5px; color: #666; font-size: 14px;">
                        The title displayed above the notice board
                    </p>
                </div>
            </div>
            
            <div class="form-group">
                <label for="notice_board_content">Notice Board Content:</label>
                <div class="editor-container">
                    <div class="wp-editor-tools">
                        <!-- WordPress will add the toolbar here -->
                    </div>
                    <div class="wp-editor-container">
                        <?php
                        // Output the WordPress editor
                        wp_editor(
                            $current_settings['content'],
                            $editor_id,
                            $editor_settings
                        );
                        ?>
                    </div>
                </div>
                <p style="margin-top: 5px; color: #666; font-size: 14px;">
                    Use the rich text editor to format your notice board content. You can add links, lists, images, and more.
                </p>
            </div>
            
            <div style="margin-top: 30px;">
                <h3>Notice Board Features:</h3>
                <div class="current-settings">
                    <ul>
                        <li><strong>Rich Text Editor:</strong> Full formatting capabilities including bold, italic, lists, links, and more</li>
                        <li><strong>Media Upload:</strong> Add images and other media to your notices</li>
                        <li><strong>HTML Support:</strong> Switch to code view for advanced HTML editing</li>
                        <li><strong>Live Preview:</strong> See how your notice will look on the dashboard</li>
                    </ul>
                </div>
            </div>
            
            <div style="margin-top: 30px;">
                <button type="submit" name="update_settings">Update Settings</button>
                <a href="<?php echo CPD_Subdomain::get_dashboard_url(); ?>" target="_blank" style="text-decoration: none;">
                    <button type="button">View Dashboard</button>
                </a>
            </div>
        </form>

        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd;">
            <p><strong>Security Note:</strong> Please delete this file after updating the settings.</p>
            <p><strong>Preview:</strong> After updating, visit your dashboard to see the notice board with your new content.</p>
        </div>
    </div>

    <script type="text/javascript">
    // Initialize quicktags for the textarea
    if (typeof quicktags !== 'undefined') {
        quicktags({
            id: 'notice_board_content',
            buttons: 'strong,em,link,block,del,ins,img,ul,ol,li,code,more,close'
        });
    }
    
    // Auto-resize the textarea
    jQuery(document).ready(function($) {
        var textarea = $('#notice_board_content');
        if (textarea.length) {
            textarea.css('min-height', '300px');
        }
        
        // Show/hide content preview based on enabled state
        $('#enabled').on('change', function() {
            var isEnabled = $(this).is(':checked');
            if (!isEnabled) {
                $('.preview-box').hide();
            }
        });
    });
    </script>
</body>
</html>
