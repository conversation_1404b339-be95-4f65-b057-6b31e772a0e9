<?php
/**
 * Database Handler Class
 * 
 * Handles all database operations for the Church Programme Dashboard plugin
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

class CPD_Database {
    
    /**
     * Create database tables
     */
    public static function create_tables() {
        global $wpdb;
        
        $charset_collate = $wpdb->get_charset_collate();
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        
        // Table for programme data
        $table_programmes = $wpdb->prefix . 'cpd_programmes';
        $sql_programmes = "CREATE TABLE $table_programmes (
            id bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            programme_type varchar(50) NOT NULL,
            programme_date date NOT NULL,
            programme_time varchar(20) DEFAULT NULL,
            programme_data longtext NOT NULL,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY  (id),
            <PERSON>EY programme_type (programme_type),
            KEY programme_date (programme_date),
            KE<PERSON> type_date (programme_type, programme_date)
        ) $charset_collate;";
        
        dbDelta($sql_programmes);
        
        // Table for editor users
        $table_users = $wpdb->prefix . 'cpd_editor_users';
        $sql_users = "CREATE TABLE $table_users (
            id bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            username varchar(60) NOT NULL,
            password varchar(255) NOT NULL,
            email varchar(100) DEFAULT NULL,
            status varchar(20) DEFAULT 'active',
            created_by bigint(20) UNSIGNED DEFAULT NULL,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            last_login datetime DEFAULT NULL,
            PRIMARY KEY  (id),
            UNIQUE KEY username (username),
            KEY status (status)
        ) $charset_collate;";
        
        dbDelta($sql_users);
        
        // Table for AI settings (stored per user session)
        $table_ai_settings = $wpdb->prefix . 'cpd_ai_settings';
        $sql_ai_settings = "CREATE TABLE $table_ai_settings (
            id bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            setting_key varchar(100) NOT NULL,
            setting_value longtext NOT NULL,
            user_id bigint(20) UNSIGNED DEFAULT NULL,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY  (id),
            KEY setting_key (setting_key),
            KEY user_id (user_id)
        ) $charset_collate;";
        
        dbDelta($sql_ai_settings);
        
        // Table for extraction history
        $table_extraction_history = $wpdb->prefix . 'cpd_extraction_history';
        $sql_extraction_history = "CREATE TABLE $table_extraction_history (
            id bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            programme_type varchar(50) NOT NULL,
            image_url varchar(500) NOT NULL,
            ai_provider varchar(50) NOT NULL,
            ai_model varchar(100) NOT NULL,
            extraction_status varchar(20) DEFAULT 'pending',
            extracted_data longtext DEFAULT NULL,
            error_message text DEFAULT NULL,
            user_id bigint(20) UNSIGNED DEFAULT NULL,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY  (id),
            KEY programme_type (programme_type),
            KEY extraction_status (extraction_status),
            KEY user_id (user_id)
        ) $charset_collate;";
        
        dbDelta($sql_extraction_history);
        
        // Update database version
        update_option('cpd_db_version', CPD_VERSION);
    }
    
    /**
     * Get all programmes (no date filter)
     */
    public static function get_all_programmes($programme_type = null) {
        global $wpdb;
        $table = $wpdb->prefix . 'cpd_programmes';

        if ($programme_type) {
            return $wpdb->get_results($wpdb->prepare(
                "SELECT * FROM $table WHERE programme_type = %s ORDER BY programme_date DESC, programme_time ASC",
                $programme_type
            ));
        }

        return $wpdb->get_results("SELECT * FROM $table ORDER BY programme_date DESC, programme_time ASC");
    }

    /**
     * Get programmes by date range
     */
    public static function get_programmes_by_date_range($start_date, $end_date, $programme_type = null) {
        global $wpdb;
        $table = $wpdb->prefix . 'cpd_programmes';

        $sql = "SELECT * FROM $table WHERE programme_date BETWEEN %s AND %s";
        $params = array($start_date, $end_date);

        if ($programme_type) {
            $sql .= " AND programme_type = %s";
            $params[] = $programme_type;
        }

        $sql .= " ORDER BY programme_date ASC, programme_time ASC";

        return $wpdb->get_results($wpdb->prepare($sql, $params));
    }

    /**
     * Get programmes by specific date
     */
    public static function get_programmes_by_date($date) {
        global $wpdb;
        $table = $wpdb->prefix . 'cpd_programmes';

        return $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM $table WHERE programme_date = %s ORDER BY programme_time ASC",
            $date
        ));
    }
    
    /**
     * Get programme by ID
     */
    public static function get_programme($id) {
        global $wpdb;
        $table = $wpdb->prefix . 'cpd_programmes';
        
        return $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $table WHERE id = %d",
            $id
        ));
    }
    
    /**
     * Insert programme
     */
    public static function insert_programme($data) {
        global $wpdb;
        $table = $wpdb->prefix . 'cpd_programmes';
        
        $wpdb->insert(
            $table,
            array(
                'programme_type' => $data['programme_type'],
                'programme_date' => $data['programme_date'],
                'programme_time' => isset($data['programme_time']) ? $data['programme_time'] : null,
                'programme_data' => is_array($data['programme_data']) ? json_encode($data['programme_data']) : $data['programme_data'],
            ),
            array('%s', '%s', '%s', '%s')
        );
        
        return $wpdb->insert_id;
    }
    
    /**
     * Update programme
     */
    public static function update_programme($id, $data) {
        global $wpdb;
        $table = $wpdb->prefix . 'cpd_programmes';
        
        $update_data = array();
        $format = array();
        
        if (isset($data['programme_type'])) {
            $update_data['programme_type'] = $data['programme_type'];
            $format[] = '%s';
        }
        
        if (isset($data['programme_date'])) {
            $update_data['programme_date'] = $data['programme_date'];
            $format[] = '%s';
        }
        
        if (isset($data['programme_time'])) {
            $update_data['programme_time'] = $data['programme_time'];
            $format[] = '%s';
        }
        
        if (isset($data['programme_data'])) {
            $update_data['programme_data'] = is_array($data['programme_data']) ? json_encode($data['programme_data']) : $data['programme_data'];
            $format[] = '%s';
        }
        
        return $wpdb->update(
            $table,
            $update_data,
            array('id' => $id),
            $format,
            array('%d')
        );
    }
    
    /**
     * Delete programme
     */
    public static function delete_programme($id) {
        global $wpdb;
        $table = $wpdb->prefix . 'cpd_programmes';
        
        return $wpdb->delete(
            $table,
            array('id' => $id),
            array('%d')
        );
    }
    
    /**
     * Delete programmes by type
     */
    public static function delete_programmes_by_type($programme_type) {
        global $wpdb;
        $table = $wpdb->prefix . 'cpd_programmes';
        
        return $wpdb->delete(
            $table,
            array('programme_type' => $programme_type),
            array('%s')
        );
    }
    
    /**
     * Get all dates with programmes
     */
    public static function get_programme_dates($year, $month) {
        global $wpdb;
        $table = $wpdb->prefix . 'cpd_programmes';
        
        $start_date = sprintf('%04d-%02d-01', $year, $month);
        $end_date = date('Y-m-t', strtotime($start_date));
        
        return $wpdb->get_col($wpdb->prepare(
            "SELECT DISTINCT programme_date FROM $table 
            WHERE programme_date BETWEEN %s AND %s 
            ORDER BY programme_date ASC",
            $start_date,
            $end_date
        ));
    }
    
    /**
     * Get editor user by username
     */
    public static function get_editor_user_by_username($username) {
        global $wpdb;
        $table = $wpdb->prefix . 'cpd_editor_users';
        
        return $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $table WHERE username = %s AND status = 'active'",
            $username
        ));
    }
    
    /**
     * Create editor user
     */
    public static function create_editor_user($username, $password, $email = null, $created_by = null) {
        global $wpdb;
        $table = $wpdb->prefix . 'cpd_editor_users';
        
        $wpdb->insert(
            $table,
            array(
                'username' => $username,
                'password' => password_hash($password, PASSWORD_DEFAULT),
                'email' => $email,
                'created_by' => $created_by,
            ),
            array('%s', '%s', '%s', '%d')
        );
        
        return $wpdb->insert_id;
    }
    
    /**
     * Update last login
     */
    public static function update_last_login($user_id) {
        global $wpdb;
        $table = $wpdb->prefix . 'cpd_editor_users';
        
        return $wpdb->update(
            $table,
            array('last_login' => current_time('mysql')),
            array('id' => $user_id),
            array('%s'),
            array('%d')
        );
    }
    
    /**
     * Get all editor users
     */
    public static function get_all_editor_users() {
        global $wpdb;
        $table = $wpdb->prefix . 'cpd_editor_users';
        
        return $wpdb->get_results("SELECT id, username, email, status, created_at, last_login FROM $table ORDER BY created_at DESC");
    }
    
    /**
     * Delete editor user
     */
    public static function delete_editor_user($user_id) {
        global $wpdb;
        $table = $wpdb->prefix . 'cpd_editor_users';
        
        return $wpdb->delete(
            $table,
            array('id' => $user_id),
            array('%d')
        );
    }
    
    /**
     * Save extraction history
     */
    public static function save_extraction_history($data) {
        global $wpdb;
        $table = $wpdb->prefix . 'cpd_extraction_history';
        
        $wpdb->insert(
            $table,
            array(
                'programme_type' => $data['programme_type'],
                'image_url' => $data['image_url'],
                'ai_provider' => $data['ai_provider'],
                'ai_model' => $data['ai_model'],
                'extraction_status' => $data['extraction_status'],
                'extracted_data' => isset($data['extracted_data']) ? json_encode($data['extracted_data']) : null,
                'error_message' => isset($data['error_message']) ? $data['error_message'] : null,
                'user_id' => isset($data['user_id']) ? $data['user_id'] : null,
            ),
            array('%s', '%s', '%s', '%s', '%s', '%s', '%s', '%d')
        );
        
        return $wpdb->insert_id;
    }
}

