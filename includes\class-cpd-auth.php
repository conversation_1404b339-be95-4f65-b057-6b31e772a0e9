<?php
/**
 * Authentication Handler Class
 * 
 * Handles authentication for the editor page
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

class CPD_Auth {
    
    /**
     * Initialize
     */
    public static function init() {
        add_action('init', array(__CLASS__, 'start_session'));
        add_action('wp_logout', array(__CLASS__, 'logout'));
    }
    
    /**
     * Start session
     */
    public static function start_session() {
        if (!session_id() && !headers_sent()) {
            session_start();
        }
    }
    
    /**
     * Check if user is logged in to editor
     */
    public static function is_editor_logged_in() {
        return isset($_SESSION['cpd_editor_user_id']) && !empty($_SESSION['cpd_editor_user_id']);
    }
    
    /**
     * Get current editor user
     */
    public static function get_current_editor_user() {
        if (!self::is_editor_logged_in()) {
            return null;
        }
        
        global $wpdb;
        $table = $wpdb->prefix . 'cpd_editor_users';
        
        return $wpdb->get_row($wpdb->prepare(
            "SELECT id, username, email FROM $table WHERE id = %d AND status = 'active'",
            $_SESSION['cpd_editor_user_id']
        ));
    }
    
    /**
     * Login editor user
     */
    public static function login($username, $password) {
        $user = CPD_Database::get_editor_user_by_username($username);
        
        if (!$user) {
            return new WP_Error('invalid_username', __('Invalid username or password.', 'church-programme-dashboard'));
        }
        
        if (!password_verify($password, $user->password)) {
            return new WP_Error('invalid_password', __('Invalid username or password.', 'church-programme-dashboard'));
        }
        
        // Set session
        $_SESSION['cpd_editor_user_id'] = $user->id;
        $_SESSION['cpd_editor_username'] = $user->username;
        
        // Update last login
        CPD_Database::update_last_login($user->id);
        
        return true;
    }
    
    /**
     * Logout editor user
     */
    public static function logout() {
        unset($_SESSION['cpd_editor_user_id']);
        unset($_SESSION['cpd_editor_username']);
        session_destroy();
    }
    
    /**
     * Check if current user is WordPress admin
     */
    public static function is_wp_admin() {
        return current_user_can('manage_options');
    }
    
    /**
     * Verify nonce for AJAX requests
     */
    public static function verify_ajax_nonce($nonce, $action = 'cpd_ajax_nonce') {
        return wp_verify_nonce($nonce, $action);
    }
}

