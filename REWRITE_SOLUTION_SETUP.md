# WordPress Rewrite Solution Setup

Since your hosting environment has a "Web App Manager" that blocks subdomain access, this solution uses WordPress rewrite rules to create clean URLs that work within your hosting restrictions.

## The Problem

Your hosting has a "Web App Manager" that intercepts subdomain requests and shows "No app found for subdomain: churchprogramme". This prevents any subdomain-based solutions from working.

## The Solution

Use WordPress rewrite rules to create clean URLs:
- **Dashboard:** `https://jermesa.com/programme/`
- **Editor:** `https://jermesa.com/editor/`

## Setup Instructions

### Option 1: Add to Theme's functions.php

Add this code to your theme's `functions.php` file:

```php
// Add this to the bottom of functions.php
require_once WP_PLUGIN_DIR . '/church-programme-dashboard/cpd-rewrite-solution.php';
```

### Option 2: Create as a Plugin

1. Create a new file: `/wp-content/plugins/cpd-rewrite-solution.php`
2. Copy the contents of `cpd-rewrite-solution.php` into it
3. Add this header at the top:
```php
<?php
/**
 * Plugin Name: CPD Rewrite Solution
 * Description: Clean URLs for Church Programme Dashboard
 * Version: 1.0
 */
```
4. Activate the plugin in WordPress admin

### Step 3: Flush Rewrite Rules

After adding the code, you MUST flush rewrite rules:

1. Go to **Settings > Permalinks** in WordPress admin
2. Click **Save Changes** (no need to change anything)

## Test the URLs

After setup, test these URLs:
- **Dashboard:** https://jermesa.com/programme/
- **Editor:** https://jermesa.com/editor/

## What This Does

1. **Creates Clean URLs**: Uses WordPress rewrite rules instead of subdomains
2. **Bypasses Hosting Restrictions**: Works within your hosting environment
3. **Full Functionality**: All features work including carousel and calendar
4. **Proper WordPress Integration**: All WordPress functions and REST API work correctly

## Benefits Over Subdomains

- ✅ **Works with Hosting**: Bypasses the Web App Manager restrictions
- ✅ **Clean URLs**: Still provides professional-looking URLs
- ✅ **Easy Setup**: Simple WordPress integration
- ✅ **Reliable**: Uses proven WordPress rewrite functionality
- ✅ **All Features**: Carousel, calendar, and all plugin features work

## Troubleshooting

If the URLs don't work:

1. **Flush Rewrite Rules**: Go to Settings > Permalinks and click Save Changes
2. **Check Plugin Activation**: Ensure Church Programme Dashboard plugin is active
3. **Test REST API**: Visit `https://jermesa.com/wp-json/cpd/v1/programmes/upcoming` to check if REST API works
4. **Check Error Logs**: Look for any PHP errors in WordPress debug logs

This solution provides clean, professional URLs that work within your hosting environment's restrictions while maintaining all the functionality of your dashboard and editor.
