<?php
/**
 * AI Integration Class
 * 
 * Handles AI provider integrations for image extraction
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

class CPD_AI {
    
    /**
     * AI Provider configurations
     */
    private static $providers = array(
        'openrouter' => array(
            'name' => 'OpenRouter',
            'api_url' => 'https://openrouter.ai/api/v1',
            'models_endpoint' => '/models',
            'chat_endpoint' => '/chat/completions',
            'supports_vision' => true,
        ),
        'gemini' => array(
            'name' => 'Google Gemini',
            'api_url' => 'https://generativelanguage.googleapis.com/v1beta',
            'models_endpoint' => '/models',
            'chat_endpoint' => '/models/{model}:generateContent',
            'supports_vision' => true,
        ),
        'deepseek' => array(
            'name' => 'DeepSeek',
            'api_url' => 'https://api.deepseek.com/v1',
            'models_endpoint' => '/models',
            'chat_endpoint' => '/chat/completions',
            'supports_vision' => true,
        ),
    );
    
    /**
     * Get available providers
     */
    public static function get_providers() {
        return self::$providers;
    }
    
    /**
     * Fetch models from provider
     */
    public static function fetch_models($provider, $api_key) {
        if (!isset(self::$providers[$provider])) {
            return new WP_Error('invalid_provider', __('Invalid AI provider.', 'church-programme-dashboard'));
        }
        
        $config = self::$providers[$provider];
        
        switch ($provider) {
            case 'openrouter':
                return self::fetch_openrouter_models($api_key);
            case 'gemini':
                return self::fetch_gemini_models($api_key);
            case 'deepseek':
                return self::fetch_deepseek_models($api_key);
            default:
                return new WP_Error('unsupported_provider', __('Provider not supported yet.', 'church-programme-dashboard'));
        }
    }
    
    /**
     * Fetch OpenRouter models
     */
    private static function fetch_openrouter_models($api_key) {
        $response = wp_remote_get('https://openrouter.ai/api/v1/models', array(
            'headers' => array(
                'Authorization' => 'Bearer ' . $api_key,
                'Content-Type' => 'application/json',
            ),
            'timeout' => 30,
        ));

        if (is_wp_error($response)) {
            return $response;
        }

        $body = json_decode(wp_remote_retrieve_body($response), true);

        if (!isset($body['data'])) {
            return new WP_Error('api_error', __('Failed to fetch models.', 'church-programme-dashboard'));
        }

        // Filter for vision-capable models
        $models = array();
        foreach ($body['data'] as $model) {
            $supports_vision = false;

            // Method 1: Check architecture modality
            if (isset($model['architecture']) && isset($model['architecture']['modality'])) {
                if (in_array('image', $model['architecture']['modality']) ||
                    in_array('vision', $model['architecture']['modality']) ||
                    in_array('multimodal', $model['architecture']['modality'])) {
                    $supports_vision = true;
                }
            }

            // Method 2: Check model ID for vision keywords
            if (!$supports_vision) {
                $model_id_lower = strtolower($model['id']);
                if (stripos($model_id_lower, 'vision') !== false ||
                    stripos($model_id_lower, 'gpt-4o') !== false ||
                    stripos($model_id_lower, 'gpt-4-turbo') !== false ||
                    stripos($model_id_lower, 'claude-3') !== false ||
                    stripos($model_id_lower, 'gemini') !== false ||
                    stripos($model_id_lower, 'llava') !== false ||
                    stripos($model_id_lower, 'pixtral') !== false ||
                    stripos($model_id_lower, 'qwen-vl') !== false) {
                    $supports_vision = true;
                }
            }

            // Method 3: Check name for vision keywords
            if (!$supports_vision && isset($model['name'])) {
                $model_name_lower = strtolower($model['name']);
                if (stripos($model_name_lower, 'vision') !== false ||
                    stripos($model_name_lower, 'multimodal') !== false) {
                    $supports_vision = true;
                }
            }

            if ($supports_vision) {
                $models[] = array(
                    'id' => $model['id'],
                    'name' => isset($model['name']) ? $model['name'] : $model['id'],
                    'description' => isset($model['description']) ? $model['description'] : '',
                    'supports_vision' => true,
                    'context_length' => isset($model['context_length']) ? $model['context_length'] : null,
                    'pricing' => isset($model['pricing']) ? $model['pricing'] : null,
                );
            }
        }

        // If no models found, return error with helpful message
        if (empty($models)) {
            return new WP_Error('no_vision_models', __('No vision-capable models found. Please check your API key or try again later.', 'church-programme-dashboard'));
        }

        return $models;
    }
    
    /**
     * Fetch Gemini models
     */
    private static function fetch_gemini_models($api_key) {
        $response = wp_remote_get('https://generativelanguage.googleapis.com/v1beta/models?key=' . $api_key, array(
            'timeout' => 30,
        ));
        
        if (is_wp_error($response)) {
            return $response;
        }
        
        $body = json_decode(wp_remote_retrieve_body($response), true);
        
        if (!isset($body['models'])) {
            return new WP_Error('api_error', __('Failed to fetch models.', 'church-programme-dashboard'));
        }
        
        // Filter for vision-capable models
        $models = array();
        foreach ($body['models'] as $model) {
            if (isset($model['supportedGenerationMethods']) && 
                in_array('generateContent', $model['supportedGenerationMethods'])) {
                // Check if it supports vision
                $supports_vision = false;
                if (isset($model['inputTokenLimit']) && $model['inputTokenLimit'] > 0) {
                    // Gemini Pro Vision and similar models
                    if (stripos($model['name'], 'vision') !== false || 
                        stripos($model['displayName'], 'vision') !== false ||
                        stripos($model['name'], 'gemini-pro') !== false ||
                        stripos($model['name'], 'gemini-1.5') !== false ||
                        stripos($model['name'], 'gemini-2') !== false) {
                        $supports_vision = true;
                    }
                }
                
                if ($supports_vision) {
                    $models[] = array(
                        'id' => str_replace('models/', '', $model['name']),
                        'name' => isset($model['displayName']) ? $model['displayName'] : $model['name'],
                        'description' => isset($model['description']) ? $model['description'] : '',
                        'supports_vision' => true,
                    );
                }
            }
        }
        
        return $models;
    }
    
    /**
     * Fetch DeepSeek models
     */
    private static function fetch_deepseek_models($api_key) {
        $response = wp_remote_get('https://api.deepseek.com/v1/models', array(
            'headers' => array(
                'Authorization' => 'Bearer ' . $api_key,
                'Content-Type' => 'application/json',
            ),
            'timeout' => 30,
        ));
        
        if (is_wp_error($response)) {
            return $response;
        }
        
        $body = json_decode(wp_remote_retrieve_body($response), true);
        
        if (!isset($body['data'])) {
            return new WP_Error('api_error', __('Failed to fetch models.', 'church-programme-dashboard'));
        }
        
        // Filter for vision-capable models
        $models = array();
        foreach ($body['data'] as $model) {
            // DeepSeek models that support vision
            if (stripos($model['id'], 'vision') !== false || 
                stripos($model['id'], 'vl') !== false) {
                $models[] = array(
                    'id' => $model['id'],
                    'name' => isset($model['name']) ? $model['name'] : $model['id'],
                    'description' => isset($model['description']) ? $model['description'] : '',
                    'supports_vision' => true,
                );
            }
        }
        
        return $models;
    }
    
    /**
     * Extract data from image
     */
    public static function extract_from_image($provider, $model, $api_key, $image_url, $programme_type) {
        if (!isset(self::$providers[$provider])) {
            return new WP_Error('invalid_provider', __('Invalid AI provider.', 'church-programme-dashboard'));
        }
        
        // Get the appropriate prompt for the programme type
        $prompt = self::get_extraction_prompt($programme_type);
        
        if (is_wp_error($prompt)) {
            return $prompt;
        }
        
        // Call the appropriate extraction method
        switch ($provider) {
            case 'openrouter':
                return self::extract_with_openrouter($model, $api_key, $image_url, $prompt);
            case 'gemini':
                return self::extract_with_gemini($model, $api_key, $image_url, $prompt);
            case 'deepseek':
                return self::extract_with_deepseek($model, $api_key, $image_url, $prompt);
            default:
                return new WP_Error('unsupported_provider', __('Provider not supported yet.', 'church-programme-dashboard'));
        }
    }
    
    /**
     * Get extraction prompt for programme type
     */
    private static function get_extraction_prompt($programme_type) {
        $prompts = array(
            'jingiaseng_rangbah' => self::get_rangbah_prompt(),
            'jingiaseng_iing' => self::get_iing_prompt(),
            'jingiaseng_samla' => self::get_samla_prompt(),
            'jingiaseng_khynnah' => self::get_khynnah_prompt(),
        );
        
        if (!isset($prompts[$programme_type])) {
            return new WP_Error('invalid_type', __('Invalid programme type.', 'church-programme-dashboard'));
        }
        
        return $prompts[$programme_type];
    }
    
    /**
     * Get JINGIASENG RANGBAH extraction prompt
     */
    private static function get_rangbah_prompt() {
        return "You are an expert at extracting structured data from church programme schedules. 

Analyze this image which contains JINGIASENG RANGBAH programme schedule. This schedule has TWO programmes:

1. JINGIASENG 1:00 Baje (starts at 1:00 PM):
   - Uses Column 1 and Column 2
   - Extract: Date, NONGIATHUH KHANA POR 1:00PM participants

2. MIET BALANG (starts at 6:30 PM):
   - Uses Column 3, 4, 5, and 6
   - Extract: Date, PULE SDANG & DUWAI, NONGKREN, KHUBOR

IMPORTANT: Sometimes at the bottom there are sub-programmes with specific dates and times. If present, extract these as well. If not present, don't include them.

Return the data in this EXACT JSON format:
{
  \"programmes\": [
    {
      \"type\": \"jingiaseng_1pm\",
      \"date\": \"YYYY-MM-DD\",
      \"time\": \"1:00 PM\",
      \"data\": {
        \"nongiathuh_khana_por\": \"participant names\"
      }
    },
    {
      \"type\": \"miet_balang\",
      \"date\": \"YYYY-MM-DD\",
      \"time\": \"6:30 PM\",
      \"data\": {
        \"pule_sdang_duwai\": \"participant names\",
        \"nongkren\": \"participant names\",
        \"khubor\": \"participant names\"
      }
    }
  ],
  \"sub_programmes\": [
    {
      \"title\": \"sub-programme title\",
      \"date\": \"YYYY-MM-DD\",
      \"time\": \"HH:MM AM/PM\",
      \"details\": \"any additional details\"
    }
  ]
}

Extract ALL dates and participants from the image. Return ONLY valid JSON, no additional text.";
    }
    
    /**
     * Get JINGIASENG IING extraction prompt
     */
    private static function get_iing_prompt() {
        return "You are an expert at extracting structured data from church programme schedules.

Analyze this image which contains JINGIASENG IING programme schedule. This programme has TWO ZONES, both starting at 6:30 PM:

ZONE-1:
- Extract: Date, ING, NONGPULE & DUWAI, NONGKREN

ZONE-2:
- Extract: Date, ING, NONGPULE & DUWAI, NONGKREN

Return the data in this EXACT JSON format:
{
  \"programmes\": [
    {
      \"type\": \"jingiaseng_iing\",
      \"date\": \"YYYY-MM-DD\",
      \"time\": \"6:30 PM\",
      \"data\": {
        \"zone_1\": {
          \"ing\": \"participant names\",
          \"nongpule_duwai\": \"participant names\",
          \"nongkren\": \"participant names\"
        },
        \"zone_2\": {
          \"ing\": \"participant names\",
          \"nongpule_duwai\": \"participant names\",
          \"nongkren\": \"participant names\"
        }
      }
    }
  ]
}

Extract ALL dates and participants from the image. Return ONLY valid JSON, no additional text.";
    }
    
    /**
     * Get JINGIASENG SAMLA extraction prompt
     */
    private static function get_samla_prompt() {
        return "You are an expert at extracting structured data from church programme schedules.

Analyze this image which contains JINGIASENG SAMLA programme schedule (starts at 6:30 PM). The schedule has 5 columns:

Column 1: TARIK (Date)
Column 2: PULESDANG & DUWAI
Column 3: JINGAINGUH
Column 4: SPECIAL NO.
Column 5: NONGKREN

IMPORTANT: Sometimes there are no regular participants, only special items like \"JINGIASENG IALAP\". Extract these special items as well.

Return the data in this EXACT JSON format:
{
  \"programmes\": [
    {
      \"type\": \"jingiaseng_samla\",
      \"date\": \"YYYY-MM-DD\",
      \"time\": \"6:30 PM\",
      \"data\": {
        \"pulesdang_duwai\": \"participant names or empty if special item\",
        \"jingainguh\": \"participant names or empty if special item\",
        \"special_no\": \"special number or empty\",
        \"nongkren\": \"participant names or empty if special item\",
        \"special_item\": \"special item name if applicable, otherwise empty\"
      }
    }
  ]
}

Extract ALL dates and participants from the image. Return ONLY valid JSON, no additional text.";
    }
    
    /**
     * Get JINGIASENG KHYNNAH extraction prompt
     */
    private static function get_khynnah_prompt() {
        return "You are an expert at extracting structured data from church programme schedules.

Analyze this image which contains JINGIASENG KHYNNAH programme schedule (starts at 3:00 PM). The schedule has 8 columns:

Column 1: TARIK (Date)
Column 2: JINGRWAI IAROH
Column 3: NONGPULE SDANG (has 2 sub-columns: OLD TESTAMENT and NEW TESTAMENT)
Column 4: NONG DUWAI
Column 5: LUM JINGAINGUH
Column 6: JINGRWAI KYRPANG
Column 7: DUWAI JINGAINGUH
Column 8: NONGKREN/ACTIVITIES

Return the data in this EXACT JSON format:
{
  \"programmes\": [
    {
      \"type\": \"jingiaseng_khynnah\",
      \"date\": \"YYYY-MM-DD\",
      \"time\": \"3:00 PM\",
      \"data\": {
        \"jingrwai_iaroh\": \"participant names\",
        \"nongpule_sdang_old_testament\": \"participant names\",
        \"nongpule_sdang_new_testament\": \"participant names\",
        \"nong_duwai\": \"participant names\",
        \"lum_jingainguh\": \"participant names\",
        \"jingrwai_kyrpang\": \"participant names\",
        \"duwai_jingainguh\": \"participant names\",
        \"nongkren_activities\": \"participant names or activities\"
      }
    }
  ]
}

Extract ALL dates and participants from the image. Return ONLY valid JSON, no additional text.";
    }

    /**
     * Extract with OpenRouter
     */
    private static function extract_with_openrouter($model, $api_key, $image_url, $prompt) {
        // Convert image to base64 if it's a local file
        $image_data = self::prepare_image_data($image_url);

        if (is_wp_error($image_data)) {
            return $image_data;
        }

        $request_body = array(
            'model' => $model,
            'messages' => array(
                array(
                    'role' => 'user',
                    'content' => array(
                        array(
                            'type' => 'text',
                            'text' => $prompt,
                        ),
                        array(
                            'type' => 'image_url',
                            'image_url' => array(
                                'url' => $image_data,
                            ),
                        ),
                    ),
                ),
            ),
            'max_tokens' => 4000,
        );

        $response = wp_remote_post('https://openrouter.ai/api/v1/chat/completions', array(
            'headers' => array(
                'Authorization' => 'Bearer ' . $api_key,
                'Content-Type' => 'application/json',
                'HTTP-Referer' => home_url(),
            ),
            'body' => json_encode($request_body),
            'timeout' => 60,
        ));

        if (is_wp_error($response)) {
            return $response;
        }

        $body = json_decode(wp_remote_retrieve_body($response), true);

        if (!isset($body['choices'][0]['message']['content'])) {
            return new WP_Error('api_error', __('Failed to extract data from image.', 'church-programme-dashboard'));
        }

        return self::parse_extraction_result($body['choices'][0]['message']['content']);
    }

    /**
     * Extract with Gemini
     */
    private static function extract_with_gemini($model, $api_key, $image_url, $prompt) {
        // Convert image to base64
        $image_data = self::prepare_image_data($image_url, true);

        if (is_wp_error($image_data)) {
            return $image_data;
        }

        $request_body = array(
            'contents' => array(
                array(
                    'parts' => array(
                        array(
                            'text' => $prompt,
                        ),
                        array(
                            'inline_data' => array(
                                'mime_type' => 'image/png',
                                'data' => $image_data,
                            ),
                        ),
                    ),
                ),
            ),
            'generationConfig' => array(
                'maxOutputTokens' => 4000,
            ),
        );

        $url = 'https://generativelanguage.googleapis.com/v1beta/models/' . $model . ':generateContent?key=' . $api_key;

        $response = wp_remote_post($url, array(
            'headers' => array(
                'Content-Type' => 'application/json',
            ),
            'body' => json_encode($request_body),
            'timeout' => 60,
        ));

        if (is_wp_error($response)) {
            return $response;
        }

        $body = json_decode(wp_remote_retrieve_body($response), true);

        if (!isset($body['candidates'][0]['content']['parts'][0]['text'])) {
            return new WP_Error('api_error', __('Failed to extract data from image.', 'church-programme-dashboard'));
        }

        return self::parse_extraction_result($body['candidates'][0]['content']['parts'][0]['text']);
    }

    /**
     * Extract with DeepSeek
     */
    private static function extract_with_deepseek($model, $api_key, $image_url, $prompt) {
        // Convert image to base64 if it's a local file
        $image_data = self::prepare_image_data($image_url);

        if (is_wp_error($image_data)) {
            return $image_data;
        }

        $request_body = array(
            'model' => $model,
            'messages' => array(
                array(
                    'role' => 'user',
                    'content' => array(
                        array(
                            'type' => 'text',
                            'text' => $prompt,
                        ),
                        array(
                            'type' => 'image_url',
                            'image_url' => array(
                                'url' => $image_data,
                            ),
                        ),
                    ),
                ),
            ),
            'max_tokens' => 4000,
        );

        $response = wp_remote_post('https://api.deepseek.com/v1/chat/completions', array(
            'headers' => array(
                'Authorization' => 'Bearer ' . $api_key,
                'Content-Type' => 'application/json',
            ),
            'body' => json_encode($request_body),
            'timeout' => 60,
        ));

        if (is_wp_error($response)) {
            return $response;
        }

        $body = json_decode(wp_remote_retrieve_body($response), true);

        if (!isset($body['choices'][0]['message']['content'])) {
            return new WP_Error('api_error', __('Failed to extract data from image.', 'church-programme-dashboard'));
        }

        return self::parse_extraction_result($body['choices'][0]['message']['content']);
    }

    /**
     * Prepare image data for API
     */
    private static function prepare_image_data($image_url, $base64_only = false) {
        // If it's already a data URL, return as is
        if (strpos($image_url, 'data:image') === 0) {
            if ($base64_only) {
                // Extract base64 part only
                $parts = explode(',', $image_url);
                return isset($parts[1]) ? $parts[1] : $image_url;
            }
            return $image_url;
        }

        // If it's a URL, download and convert to base64
        if (strpos($image_url, 'http') === 0) {
            $response = wp_remote_get($image_url, array('timeout' => 30));

            if (is_wp_error($response)) {
                return $response;
            }

            $image_content = wp_remote_retrieve_body($response);
            $base64 = base64_encode($image_content);

            if ($base64_only) {
                return $base64;
            }

            // Detect mime type
            $finfo = new finfo(FILEINFO_MIME_TYPE);
            $mime_type = $finfo->buffer($image_content);

            return 'data:' . $mime_type . ';base64,' . $base64;
        }

        // If it's a local file path
        if (file_exists($image_url)) {
            $image_content = file_get_contents($image_url);
            $base64 = base64_encode($image_content);

            if ($base64_only) {
                return $base64;
            }

            $mime_type = mime_content_type($image_url);
            return 'data:' . $mime_type . ';base64,' . $base64;
        }

        return new WP_Error('invalid_image', __('Invalid image URL or path.', 'church-programme-dashboard'));
    }

    /**
     * Parse extraction result
     */
    private static function parse_extraction_result($content) {
        // Remove markdown code blocks if present
        $content = preg_replace('/```json\s*/', '', $content);
        $content = preg_replace('/```\s*/', '', $content);
        $content = trim($content);

        // Try to decode JSON
        $data = json_decode($content, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            return new WP_Error('parse_error', __('Failed to parse extraction result: ', 'church-programme-dashboard') . json_last_error_msg());
        }

        return $data;
    }
}

