# 🚨 UPLOAD THESE FILES NOW!

## Your errors are because the server has OLD files!

---

## 📤 UPLOAD THESE 7 FILES:

1. ✅ `admin/class-cpd-admin.php`
2. ✅ `admin/js/admin-programmes.js`
3. ✅ `admin/views/settings-page.php`
4. ✅ `includes/class-cpd-rest-api.php`
5. ✅ `includes/class-cpd-subdomain.php`
6. ✅ `includes/class-cpd-ai.php`
7. ✅ `database-check.php` (NEW - in root folder)

---

## 🎯 WHAT EACH FILE FIXES:

### 1. admin/class-cpd-admin.php
**Fixes:** JavaScript caching issue  
**Problem:** <PERSON><PERSON><PERSON> loads old JavaScript  
**Solution:** Uses file modification time instead of version number

### 2. admin/js/admin-programmes.js
**Fixes:** `Cannot read properties of undefined (reading 'reset')`  
**Problem:** Trying to reset form that doesn't exist  
**Solution:** Checks if form exists before calling reset()

### 3. includes/class-cpd-rest-api.php
**Fixes:** 403 errors on `/wp-json/cpd/v1/editor/programmes`  
**Problem:** WordPress admins can't access REST API  
**Solution:** Added `current_user_can('manage_options')` check

### 4. includes/class-cpd-subdomain.php
**Fixes:** CORS errors  
**Problem:** Subdomain can't access main site resources  
**Solution:** Added CORS headers and prevented theme loading

### 5. includes/class-cpd-ai.php
**Fixes:** "No vision models found" for OpenRouter  
**Problem:** Model detection too strict  
**Solution:** 3 detection methods (architecture, ID, name)

### 6. admin/views/settings-page.php
**Fixes:** No test buttons  
**Problem:** Can't easily test dashboard/editor  
**Solution:** Added test buttons and warning box

### 7. database-check.php (NEW)
**Fixes:** "No programmes found"  
**Problem:** Database tables not created  
**Solution:** Verifies and repairs database tables

---

## ⚡ QUICK UPLOAD GUIDE:

### Using FTP (FileZilla, WinSCP, etc.):

1. **Connect to your server**
2. **Go to:** `/public_html/wp-content/plugins/church-programme-dashboard/`
3. **Drag and drop** these 7 files
4. **Confirm overwrite** when asked

### Using cPanel:

1. **Login to cPanel**
2. **Open File Manager**
3. **Navigate to:** `public_html/wp-content/plugins/church-programme-dashboard/`
4. **Click Upload** button
5. **Select files** and upload
6. **Confirm overwrite**

---

## ✅ AFTER UPLOAD:

### Step 1: Verify Upload
Visit: `https://jermesa.com/wp-content/plugins/church-programme-dashboard/version-check.php`

**Should show:**
```
✅ All Required Files Found
✅ REST API Fix: Applied
✅ Admin JavaScript Fix: Applied
✅ Subdomain Fallback Fix: Applied
✅ AI Model Detection Fix: Applied
✅ Cache Busting Fix: Applied
✅ All Fixes Applied Successfully!
```

### Step 2: Check Database
Visit: `https://jermesa.com/wp-content/plugins/church-programme-dashboard/database-check.php`

**If tables missing:**
- Click "🔧 Repair Database" button
- Wait for completion
- Refresh page

### Step 3: Clear Cache

**Browser:**
- Press `Ctrl+Shift+Delete`
- Clear "Cached images and files"
- Or use Incognito mode

**WordPress:**
- Go to Settings > Permalinks
- Click "Save Changes"

### Step 4: Test

1. **Go to:** Church Programme > Settings > Manage Programmes
2. **Click:** "+ Add New Programme"
3. **Expected:** Modal opens, no errors
4. **Console:** No 403 errors

---

## 🐛 IF STILL NOT WORKING:

### Check 1: Files Uploaded?
- Visit version-check.php
- All should show ✅
- Modification dates should be TODAY

### Check 2: Cache Cleared?
- Try incognito mode
- Hard refresh: `Ctrl+F5`

### Check 3: Database Tables?
- Visit database-check.php
- Tables should exist
- If not, click "Repair Database"

### Check 4: Still Errors?
Take screenshots of:
1. version-check.php page
2. database-check.php page
3. Browser console (F12)
4. The error you're seeing

---

## 📊 WHAT YOU'LL SEE AFTER UPLOAD:

### ✅ Programme Management:
- Modal opens smoothly
- No JavaScript errors
- No 403 errors
- Can add/edit/delete programmes

### ✅ AI Extraction:
- OpenRouter shows vision models
- Can fetch models successfully
- Can extract data from images
- Data populates form correctly

### ✅ Dashboard:
- Loads at `/cpd-dashboard/`
- No CORS errors
- Shows calendar and carousel

### ✅ Editor:
- Loads at `/cpd-editor/`
- Shows login form
- No CORS errors

---

## 🎯 SUMMARY:

**Current Status:**
- ❌ Server has OLD files
- ❌ Browser cached OLD JavaScript
- ❌ Database tables may not exist

**After Upload:**
- ✅ Server has NEW files
- ✅ Cache busting forces new JavaScript
- ✅ Database can be repaired

**Action Required:**
1. Upload 7 files
2. Visit version-check.php
3. Visit database-check.php
4. Clear cache
5. Test

---

**Time Required:** 5-10 minutes  
**Difficulty:** Easy  
**Risk:** None (can always re-upload)

---

## 🆘 NEED HELP?

If you're stuck:

1. **Which step are you on?**
2. **What error do you see?**
3. **Did you upload all 7 files?**
4. **What does version-check.php show?**
5. **What does database-check.php show?**

---

**Remember:** The fixes are DONE. You just need to UPLOAD them!

**Start here:** Upload the 7 files, then visit version-check.php

---

**Plugin Version:** 1.0.0  
**Fix Date:** 2025-09-30  
**Files to Upload:** 7  
**Estimated Time:** 5-10 minutes

