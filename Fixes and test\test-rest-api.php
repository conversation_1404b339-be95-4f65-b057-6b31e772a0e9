<?php
/**
 * REST API Test Tool
 * 
 * Access this file to test REST API endpoints and authentication
 * URL: https://jermesa.com/wp-content/plugins/church-programme-dashboard/test-rest-api.php
 */

// Load WordPress
$wp_load_path = dirname(dirname(dirname(dirname(__FILE__)))) . '/wp-load.php';
if (file_exists($wp_load_path)) {
    require_once($wp_load_path);
} else {
    die('WordPress not found. Please access this file through WordPress.');
}

// Security check - only admins can access
if (!current_user_can('manage_options')) {
    wp_die('You do not have permission to access this page.');
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>Church Programme Dashboard - REST API Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            max-width: 1200px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 4px;
        }
        .btn {
            display: inline-block;
            padding: 10px 20px;
            background: #3498db;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            border: none;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
        }
        .btn:hover {
            background: #2980b9;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            background: white;
            border-left: 4px solid #3498db;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        .error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        .info {
            padding: 15px;
            margin: 15px 0;
            background: #d1ecf1;
            border-left: 4px solid #17a2b8;
            border-radius: 4px;
        }
        code {
            background: #f4f4f4;
            padding: 2px 6px;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Church Programme Dashboard - REST API Test</h1>
        
        <div class="info">
            <strong>Current User:</strong> <?php echo wp_get_current_user()->user_login; ?><br>
            <strong>User Role:</strong> <?php echo implode(', ', wp_get_current_user()->roles); ?><br>
            <strong>Can Manage Options:</strong> <?php echo current_user_can('manage_options') ? '✅ Yes' : '❌ No'; ?><br>
            <strong>REST URL:</strong> <?php echo rest_url('cpd/v1/'); ?><br>
            <strong>REST Nonce:</strong> <code id="rest-nonce"><?php echo wp_create_nonce('wp_rest'); ?></code>
        </div>
        
        <h2>🔍 Test REST API Endpoints</h2>
        
        <!-- Test 1: Get All Programmes -->
        <div class="test-section">
            <h3>Test 1: GET /editor/programmes</h3>
            <p>This endpoint requires authentication and should return all programmes.</p>
            <button class="btn" onclick="testGetProgrammes()">Run Test</button>
            <div id="test1-result" class="result" style="display:none;"></div>
        </div>
        
        <!-- Test 2: Create Programme -->
        <div class="test-section">
            <h3>Test 2: POST /editor/programmes</h3>
            <p>This endpoint requires authentication and should create a new programme.</p>
            <button class="btn" onclick="testCreateProgramme()">Run Test</button>
            <div id="test2-result" class="result" style="display:none;"></div>
        </div>
        
        <!-- Test 3: Get Public Programmes -->
        <div class="test-section">
            <h3>Test 3: GET /programmes/upcoming</h3>
            <p>This is a public endpoint and should work without authentication.</p>
            <button class="btn" onclick="testGetUpcoming()">Run Test</button>
            <div id="test3-result" class="result" style="display:none;"></div>
        </div>
        
        <!-- Test 4: Permission Check -->
        <div class="test-section">
            <h3>Test 4: Permission Check</h3>
            <p>Test if the current user has permission to access editor endpoints.</p>
            <button class="btn" onclick="testPermission()">Run Test</button>
            <div id="test4-result" class="result" style="display:none;"></div>
        </div>
        
        <h2>📋 Diagnostic Information</h2>
        
        <div class="test-section">
            <h3>WordPress Configuration</h3>
            <table style="width: 100%; border-collapse: collapse;">
                <tr style="border-bottom: 1px solid #ddd;">
                    <td style="padding: 10px;"><strong>WordPress Version:</strong></td>
                    <td style="padding: 10px;"><?php echo get_bloginfo('version'); ?></td>
                </tr>
                <tr style="border-bottom: 1px solid #ddd;">
                    <td style="padding: 10px;"><strong>PHP Version:</strong></td>
                    <td style="padding: 10px;"><?php echo PHP_VERSION; ?></td>
                </tr>
                <tr style="border-bottom: 1px solid #ddd;">
                    <td style="padding: 10px;"><strong>Site URL:</strong></td>
                    <td style="padding: 10px;"><?php echo get_site_url(); ?></td>
                </tr>
                <tr style="border-bottom: 1px solid #ddd;">
                    <td style="padding: 10px;"><strong>Home URL:</strong></td>
                    <td style="padding: 10px;"><?php echo get_home_url(); ?></td>
                </tr>
                <tr style="border-bottom: 1px solid #ddd;">
                    <td style="padding: 10px;"><strong>REST API URL:</strong></td>
                    <td style="padding: 10px;"><?php echo rest_url(); ?></td>
                </tr>
                <tr style="border-bottom: 1px solid #ddd;">
                    <td style="padding: 10px;"><strong>Plugin Active:</strong></td>
                    <td style="padding: 10px;"><?php echo is_plugin_active('church-programme-dashboard/church-programme-dashboard.php') ? '✅ Yes' : '❌ No'; ?></td>
                </tr>
            </table>
        </div>
    </div>
    
    <script>
        const restUrl = '<?php echo rest_url('cpd/v1/'); ?>';
        const restNonce = '<?php echo wp_create_nonce('wp_rest'); ?>';
        
        function showResult(elementId, content, isSuccess) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = 'result ' + (isSuccess ? 'success' : 'error');
            element.innerHTML = '<pre>' + content + '</pre>';
        }
        
        async function testGetProgrammes() {
            try {
                const response = await fetch(restUrl + 'editor/programmes', {
                    method: 'GET',
                    headers: {
                        'X-WP-Nonce': restNonce,
                        'Content-Type': 'application/json'
                    },
                    credentials: 'same-origin'
                });
                
                const data = await response.json();
                const result = `Status: ${response.status} ${response.statusText}\n\n` +
                              `Response:\n${JSON.stringify(data, null, 2)}`;
                
                showResult('test1-result', result, response.ok);
            } catch (error) {
                showResult('test1-result', `Error: ${error.message}`, false);
            }
        }
        
        async function testCreateProgramme() {
            try {
                const testData = {
                    type: 'jingiaseng_1pm',
                    date: '2025-10-15',
                    time: '13:00',
                    data: {
                        nongiathuh_khana_por: 'Test Programme',
                        pule_sdang_duwai: 'Test Pastor',
                        pule_sdang_thaw: 'Test Church'
                    }
                };
                
                const response = await fetch(restUrl + 'editor/programmes', {
                    method: 'POST',
                    headers: {
                        'X-WP-Nonce': restNonce,
                        'Content-Type': 'application/json'
                    },
                    credentials: 'same-origin',
                    body: JSON.stringify(testData)
                });
                
                const data = await response.json();
                const result = `Status: ${response.status} ${response.statusText}\n\n` +
                              `Request:\n${JSON.stringify(testData, null, 2)}\n\n` +
                              `Response:\n${JSON.stringify(data, null, 2)}`;
                
                showResult('test2-result', result, response.ok);
            } catch (error) {
                showResult('test2-result', `Error: ${error.message}`, false);
            }
        }
        
        async function testGetUpcoming() {
            try {
                const response = await fetch(restUrl + 'programmes/upcoming', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                const result = `Status: ${response.status} ${response.statusText}\n\n` +
                              `Response:\n${JSON.stringify(data, null, 2)}`;
                
                showResult('test3-result', result, response.ok);
            } catch (error) {
                showResult('test3-result', `Error: ${error.message}`, false);
            }
        }
        
        async function testPermission() {
            const result = `Current User: <?php echo wp_get_current_user()->user_login; ?>\n` +
                          `User ID: <?php echo get_current_user_id(); ?>\n` +
                          `Can Manage Options: <?php echo current_user_can('manage_options') ? 'Yes' : 'No'; ?>\n` +
                          `Is Admin: <?php echo is_admin() ? 'Yes' : 'No'; ?>\n` +
                          `REST Nonce: ${restNonce}\n\n` +
                          `Permission Check Result:\n` +
                          `<?php 
                          if (current_user_can('manage_options')) {
                              echo '✅ User has manage_options capability\n';
                              echo '✅ Should be able to access editor endpoints';
                          } else {
                              echo '❌ User does NOT have manage_options capability\n';
                              echo '❌ Will NOT be able to access editor endpoints';
                          }
                          ?>`;
            
            showResult('test4-result', result, <?php echo current_user_can('manage_options') ? 'true' : 'false'; ?>);
        }
    </script>
</body>
</html>

