<?php
/**
 * Secure Admin Panel - Single Page with Tabs and User Authentication
 * 
 * This script provides a complete admin panel with all settings organized in tabs
 * and secure user authentication system.
 * 
 * Instructions:
 * 1. Upload this file to your WordPress root directory (same level as wp-config.php)
 * 2. Access it via browser: https://yoursite.com/secure-admin-panel.php
 * 3. First time: Use default admin credentials (admin/admin123)
 * 4. Configure all settings across different tabs
 * 5. Manage users in the Users tab
 * 6. Delete this file after use for security
 */

// Security check
if (!defined('ABSPATH')) {
    // Load WordPress
    require_once('wp-load.php');
}

// Initialize custom user management
function cpd_init_custom_users() {
    $users = get_option('cpd_custom_users', array());
    
    // Add default admin user if no users exist
    if (empty($users)) {
        $users = array(
            'admin' => array(
                'username' => 'admin',
                'password' => password_hash('admin123', PASSWORD_DEFAULT),
                'role' => 'administrator',
                'email' => '<EMAIL>',
                'created_at' => date('Y-m-d H:i:s')
            )
        );
        update_option('cpd_custom_users', $users);
    }
    
    return $users;
}

// Check if user is logged in
function cpd_is_logged_in() {
    return isset($_SESSION['cpd_logged_in']) && $_SESSION['cpd_logged_in'] === true;
}

// Authenticate user
function cpd_authenticate($username, $password) {
    $users = get_option('cpd_custom_users', array());
    
    if (isset($users[$username]) && password_verify($password, $users[$username]['password'])) {
        $_SESSION['cpd_logged_in'] = true;
        $_SESSION['cpd_username'] = $username;
        $_SESSION['cpd_role'] = $users[$username]['role'];
        return true;
    }
    
    return false;
}

// Logout user
function cpd_logout() {
    session_destroy();
    header('Location: ' . $_SERVER['PHP_SELF']);
    exit;
}

// Add new user
function cpd_add_user($username, $password, $role, $email) {
    $users = get_option('cpd_custom_users', array());
    
    if (isset($users[$username])) {
        return false; // User already exists
    }
    
    $users[$username] = array(
        'username' => $username,
        'password' => password_hash($password, PASSWORD_DEFAULT),
        'role' => $role,
        'email' => $email,
        'created_at' => date('Y-m-d H:i:s')
    );
    
    update_option('cpd_custom_users', $users);
    return true;
}

// Delete user
function cpd_delete_user($username) {
    $users = get_option('cpd_custom_users', array());
    
    if (isset($users[$username]) && $username !== 'admin') {
        unset($users[$username]);
        update_option('cpd_custom_users', $users);
        return true;
    }
    
    return false;
}

// Start session
session_start();

// Initialize users
cpd_init_custom_users();

// Handle authentication
if (isset($_POST['login'])) {
    $username = sanitize_text_field($_POST['username']);
    $password = $_POST['password'];
    
    if (cpd_authenticate($username, $password)) {
        header('Location: ' . $_SERVER['PHP_SELF']);
        exit;
    } else {
        $login_error = "Invalid username or password";
    }
}

// Handle logout
if (isset($_GET['logout'])) {
    cpd_logout();
}

// Check if user needs to login
if (!cpd_is_logged_in()) {
    // Show login form
    ?>
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Login - Church Programme Dashboard</title>
        <style>
            body {
                font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
                margin: 0;
                padding: 0;
                background: #f0f0f1;
                display: flex;
                justify-content: center;
                align-items: center;
                min-height: 100vh;
            }
            .login-container {
                background: white;
                padding: 40px;
                border-radius: 8px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                width: 100%;
                max-width: 400px;
            }
            .login-header {
                text-align: center;
                margin-bottom: 30px;
            }
            .login-header h1 {
                color: #1e73be;
                margin: 0 0 10px 0;
            }
            .login-header p {
                color: #666;
                margin: 0;
            }
            .form-group {
                margin-bottom: 20px;
            }
            label {
                display: block;
                margin-bottom: 8px;
                font-weight: 600;
                font-size: 14px;
                color: #2c3338;
            }
            input[type="text"], input[type="password"] {
                width: 100%;
                padding: 12px;
                border: 1px solid #8c8f94;
                border-radius: 4px;
                font-size: 16px;
                box-sizing: border-box;
            }
            button {
                width: 100%;
                background: #2271b1;
                color: white;
                border: none;
                padding: 12px;
                border-radius: 4px;
                cursor: pointer;
                font-size: 16px;
            }
            button:hover {
                background: #135e96;
            }
            .error {
                background: #f8d7da;
                color: #721c24;
                padding: 10px;
                border-radius: 4px;
                margin-bottom: 20px;
                border: 1px solid #f5c6cb;
            }
            .default-credentials {
                background: #d1ecf1;
                color: #0c5460;
                padding: 15px;
                border-radius: 4px;
                margin-top: 20px;
                border: 1px solid #bee5eb;
            }
        </style>
    </head>
    <body>
        <div class="login-container">
            <div class="login-header">
                <h1>Church Programme Dashboard</h1>
                <p>Secure Admin Panel Login</p>
            </div>
            
            <?php if (isset($login_error)): ?>
                <div class="error">
                    <?php echo esc_html($login_error); ?>
                </div>
            <?php endif; ?>
            
            <form method="post">
                <div class="form-group">
                    <label for="username">Username:</label>
                    <input type="text" id="username" name="username" required>
                </div>
                <div class="form-group">
                    <label for="password">Password:</label>
                    <input type="password" id="password" name="password" required>
                </div>
                <button type="submit" name="login">Login</button>
            </form>
            
            <div class="default-credentials">
                <strong>Default Credentials:</strong><br>
                Username: <code>admin</code><br>
                Password: <code>admin123</code><br>
                <small>Change these credentials after first login in the Users tab.</small>
            </div>
        </div>
    </body>
    </html>
    <?php
    exit;
}

// User is logged in, show admin panel
$current_settings = array(
    // General Settings
    'dashboard_title' => get_option('cpd_dashboard_title', 'Church Programme Dashboard'),
    'header_bg_color' => get_option('cpd_header_bg_color', '#1e73be'),
    'header_bg_image' => get_option('cpd_header_bg_image', ''),
    
    // Subdomain Settings
    'dashboard_subdomain' => get_option('cpd_dashboard_subdomain', 'dashboard'),
    'editor_subdomain' => get_option('cpd_editor_subdomain', 'editor'),
    
    // Color Settings
    'primary_color' => get_option('cpd_primary_color', '#1e73be'),
    'secondary_color' => get_option('cpd_secondary_color', '#2c3e50'),
    'accent_color' => get_option('cpd_accent_color', '#e74c3c'),
    'text_color' => get_option('cpd_text_color', '#333333'),
    
    // Programme Labels
    'label_miet_balang' => get_option('cpd_label_miet_balang', 'Miet Balang'),
    'label_miet_balang_time' => get_option('cpd_label_miet_balang_time', '6:00 AM'),
    'label_jingiaseng_samla' => get_option('cpd_label_jingiaseng_samla', 'Jingiaseng Samla'),
    'label_jingiaseng_samla_time' => get_option('cpd_label_jingiaseng_samla_time', '8:00 AM'),
    'label_jingiaseng_1pm' => get_option('cpd_label_jingiaseng_1pm', 'Jingiaseng 1PM'),
    'label_jingiaseng_1pm_time' => get_option('cpd_label_jingiaseng_1pm_time', '1:00 PM'),
    'label_jingiaseng_khynnah' => get_option('cpd_label_jingiaseng_khynnah', 'Jingiaseng Khynnah'),
    'label_jingiaseng_khynnah_time' => get_option('cpd_label_jingiaseng_khynnah_time', '4:00 PM'),
    'label_jingiaseng_iing' => get_option('cpd_label_jingiaseng_iing', 'Jingiaseng Iing'),
    'label_jingiaseng_iing_time' => get_option('cpd_label_jingiaseng_iing_time', '6:00 PM'),
    
    // Notice Board Settings
    'notice_board_enabled' => get_option('cpd_notice_board_enabled', '0'),
    'notice_board_title' => get_option('cpd_notice_board_title', 'Notice Board'),
    'notice_board_content' => get_option('cpd_notice_board_content', ''),
    
    // Carousel Settings
    'carousel_animation_type' => get_option('cpd_carousel_animation_type', 'slide'),
    'carousel_animation_speed' => get_option('cpd_carousel_animation_speed', '500'),
    'carousel_auto_interval' => get_option('cpd_carousel_auto_interval', '5000'),
    'carousel_direction' => get_option('cpd_carousel_direction', 'right-to-left'),
    
    // Cookie Consent Settings
    'cookie_consent_text' => get_option('cpd_cookie_consent_text', 'This website uses cookies to ensure you get the best experience.'),
    'cookie_consent_button' => get_option('cpd_cookie_consent_button', 'Accept'),
    
    // Quick Links
    'quick_links' => get_option('cpd_quick_links', '[]'),
);

$message = '';
$active_tab = isset($_GET['tab']) ? sanitize_text_field($_GET['tab']) : 'general';

// Handle user management
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $tab = isset($_POST['tab']) ? sanitize_text_field($_POST['tab']) : 'general';
    
    switch($tab) {
        case 'general':
            update_option('cpd_dashboard_title', sanitize_text_field($_POST['dashboard_title']));
            update_option('cpd_header_bg_color', sanitize_hex_color($_POST['header_bg_color']));
            update_option('cpd_header_bg_image', esc_url_raw($_POST['header_bg_image']));
            $message = "✅ General settings saved successfully!";
            break;
            
        case 'subdomain':
            update_option('cpd_dashboard_subdomain', sanitize_text_field($_POST['dashboard_subdomain']));
            update_option('cpd_editor_subdomain', sanitize_text_field($_POST['editor_subdomain']));
            $message = "✅ Subdomain settings saved successfully!";
            break;
            
        case 'colors':
            update_option('cpd_primary_color', sanitize_hex_color($_POST['primary_color']));
            update_option('cpd_secondary_color', sanitize_hex_color($_POST['secondary_color']));
            update_option('cpd_accent_color', sanitize_hex_color($_POST['accent_color']));
            update_option('cpd_text_color', sanitize_hex_color($_POST['text_color']));
            $message = "✅ Color settings saved successfully!";
            break;
            
        case 'labels':
            update_option('cpd_label_miet_balang', sanitize_text_field($_POST['label_miet_balang']));
            update_option('cpd_label_miet_balang_time', sanitize_text_field($_POST['label_miet_balang_time']));
            update_option('cpd_label_jingiaseng_samla', sanitize_text_field($_POST['label_jingiaseng_samla']));
            update_option('cpd_label_jingiaseng_samla_time', sanitize_text_field($_POST['label_jingiaseng_samla_time']));
            update_option('cpd_label_jingiaseng_1pm', sanitize_text_field($_POST['label_jingiaseng_1pm']));
            update_option('cpd_label_jingiaseng_1pm_time', sanitize_text_field($_POST['label_jingiaseng_1pm_time']));
            update_option('cpd_label_jingiaseng_khynnah', sanitize_text_field($_POST['label_jingiaseng_khynnah']));
            update_option('cpd_label_jingiaseng_khynnah_time', sanitize_text_field($_POST['label_jingiaseng_khynnah_time']));
            update_option('cpd_label_jingiaseng_iing', sanitize_text_field($_POST['label_jingiaseng_iing']));
            update_option('cpd_label_jingiaseng_iing_time', sanitize_text_field($_POST['label_jingiaseng_iing_time']));
            $message = "✅ Programme labels saved successfully!";
            break;
            
        case 'notice':
            update_option('cpd_notice_board_enabled', isset($_POST['notice_board_enabled']) ? '1' : '0');
            update_option('cpd_notice_board_title', sanitize_text_field($_POST['notice_board_title']));
            update_option('cpd_notice_board_content', wp_kses_post($_POST['notice_board_content']));
            $message = "✅ Notice board settings saved successfully!";
            break;
            
        case 'carousel':
            update_option('cpd_carousel_animation_type', sanitize_text_field($_POST['carousel_animation_type']));
            update_option('cpd_carousel_animation_speed', intval($_POST['carousel_animation_speed']));
            update_option('cpd_carousel_auto_interval', intval($_POST['carousel_auto_interval']));
            update_option('cpd_carousel_direction', sanitize_text_field($_POST['carousel_direction']));
            $message = "✅ Carousel settings saved successfully!";
            break;
            
        case 'cookie':
            update_option('cpd_cookie_consent_text', sanitize_text_field($_POST['cookie_consent_text']));
            update_option('cpd_cookie_consent_button', sanitize_text_field($_POST['cookie_consent_button']));
            $message = "✅ Cookie consent settings saved successfully!";
            break;
            
        case 'users':
            if (isset($_POST['add_user'])) {
                $username = sanitize_text_field($_POST['new_username']);
                $password = $_POST['new_password'];
                $role = sanitize_text_field($_POST['new_role']);
                $email = sanitize_email($_POST['new_email']);
                
                if (cpd_add_user($username, $password, $role, $email)) {
                    $message = "✅ User added successfully!";
                } else {
                    $message = "❌ User already exists!";
                }
            } elseif (isset($_POST['delete_user'])) {
                $username = sanitize_text_field($_POST['delete_username']);
                if (cpd_delete_user($username)) {
                    $message = "✅ User deleted successfully!";
                } else {
                    $message = "❌ Cannot delete this user!";
                }
            }
            break;
    }
    
    // Refresh current settings
    foreach($current_settings as $key => $value) {
        $current_settings[$key] = get_option('cpd_' . $key, $value);
    }
}

// Get all users for editor management
$all_users = get_users(array('role__in' => array('administrator', 'editor', 'author')));
$custom_users = get_option('cpd_custom_users', array());
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Secure Admin Panel - Church Programme Dashboard</title>
    <!-- Include jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f0f0f1;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: #1e73be;
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
        }
        .header h1 {
            margin: 0;
            font-size: 28px;
        }
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }
        .user-info {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(255,255,255,0.2);
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 14px;
        }
        .user-info a {
            color: white;
            text-decoration: none;
            margin-left: 10px;
        }
        .user-info a:hover {
            text-decoration: underline;
        }
        .tabs {
            background: #f6f7f7;
            border-bottom: 1px solid #c3c4c7;
            display: flex;
            flex-wrap: wrap;
        }
        .tab {
            padding: 15px 25px;
            background: transparent;
            border: none;
            border-bottom: 3px solid transparent;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            color: #2c3338;
            transition: all 0.3s ease;
        }
        .tab:hover {
            background: #e2e8f0;
        }
        .tab.active {
            background: white;
            border-bottom-color: #1e73be;
            color: #1e73be;
        }
        .tab-content {
            display: none;
            padding: 30px;
        }
        .tab-content.active {
            display: block;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            font-size: 14px;
        }
        input[type="text"], input[type="url"], input[type="number"], input[type="password"], input[type="email"], select, textarea {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #8c8f94;
            border-radius: 4px;
            font-size: 14px;
        }
        input[type="color"] {
            width: 60px;
            height: 40px;
            border: 1px solid #8c8f94;
            border-radius: 4px;
        }
        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .checkbox-group input[type="checkbox"] {
            width: auto;
        }
        button {
            background: #2271b1;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        button:hover {
            background: #135e96;
        }
        .message {
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .success {
            background: #d1e7dd;
            border: 1px solid #badbcc;
            color: #0f5132;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .setting-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 15px;
        }
        @media (max-width: 768px) {
            .setting-row {
                grid-template-columns: 1fr;
            }
        }
        .editor-toolbar {
            background: #f6f7f7;
            padding: 10px;
            border-bottom: 1px solid #c3c4c7;
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
        }
        .editor-toolbar button {
            background: #fff;
            color: #2c3338;
            border: 1px solid #c3c4c7;
            padding: 6px 12px;
            font-size: 14px;
            margin-right: 0;
        }
        .editor-toolbar button:hover {
            background: #f6f7f7;
        }
        .color-picker {
            position: relative;
            display: inline-block;
        }
        .color-picker input[type="color"] {
            position: absolute;
            opacity: 0;
            width: 100%;
            height: 100%;
            cursor: pointer;
        }
        .color-picker label {
            display: inline-block;
            padding: 6px 12px;
            background: #fff;
            border: 1px solid #c3c4c7;
            border-radius: 4px;
            cursor: pointer;
            margin: 0;
            font-size: 14px;
        }
        .color-picker label:hover {
            background: #f6f7f7;
        }
        .users-list {
            background: #f9f9f9;
            border: 1px solid #e2e8f0;
            border-radius: 4px;
            padding: 15px;
            margin-top: 10px;
        }
        .user-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            border-bottom: 1px solid #e2e8f0;
        }
        .user-item:last-child {
            border-bottom: none;
        }
        .delete-btn {
            background: #dc3545;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        .delete-btn:hover {
            background: #c82333;
        }
        .add-user-form {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 4px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Church Programme Dashboard - Secure Admin Panel</h1>
            <p>Manage all settings for your church programme dashboard</p>
            <div class="user-info">
                Logged in as: <strong><?php echo esc_html($_SESSION['cpd_username']); ?></strong>
                <a href="?logout=true">Logout</a>
            </div>
        </div>

        <?php if ($message): ?>
            <div class="message <?php echo strpos($message, '✅') !== false ? 'success' : 'error'; ?>">
                <?php echo esc_html($message); ?>
            </div>
        <?php endif; ?>

        <div class="tabs">
            <button class="tab <?php echo $active_tab === 'general' ? 'active' : ''; ?>" onclick="switchTab('general')">General</button>
            <button class="tab <?php echo $active_tab === 'subdomain' ? 'active' : ''; ?>" onclick="switchTab('subdomain')">Subdomain</button>
            <button class="tab <?php echo $active_tab === 'colors' ? 'active' : ''; ?>" onclick="switchTab('colors')">Colors & Styling</button>
            <button class="tab <?php echo $active_tab === 'labels' ? 'active' : ''; ?>" onclick="switchTab('labels')">Programme Labels</button>
            <button class="tab <?php echo $active_tab === 'programmes' ? 'active' : ''; ?>" onclick="switchTab('programmes')">Manage Programmes</button>
            <button class="tab <?php echo $active_tab === 'notice' ? 'active' : ''; ?>" onclick="switchTab('notice')">Notice Board</button>
            <button class="tab <?php echo $active_tab === 'carousel' ? 'active' : ''; ?>" onclick="switchTab('carousel')">Carousel</button>
            <button class="tab <?php echo $active_tab === 'cookie' ? 'active' : ''; ?>" onclick="switchTab('cookie')">Cookie Consent</button>
            <button class="tab <?php echo $active_tab === 'links' ? 'active' : ''; ?>" onclick="switchTab('links')">Quick Links</button>
            <button class="tab <?php echo $active_tab === 'users' ? 'active' : ''; ?>" onclick="switchTab('users')">Users</button>
        </div>

        <!-- General Tab -->
        <div class="tab-content <?php echo $active_tab === 'general' ? 'active' : ''; ?>" id="general-tab">
            <h2>General Settings</h2>
            <form method="post">
                <input type="hidden" name="tab" value="general">
                <div class="form-group">
                    <label for="dashboard_title">Dashboard Title:</label>
                    <input type="text" id="dashboard_title" name="dashboard_title" 
                           value="<?php echo esc_attr($current_settings['dashboard_title']); ?>" 
                           placeholder="Enter dashboard title">
                </div>
                <div class="form-group">
                    <label for="header_bg_color">Header Background Color:</label>
                    <input type="color" id="header_bg_color" name="header_bg_color" 
                           value="<?php echo esc_attr($current_settings['header_bg_color']); ?>">
                </div>
                <div class="form-group">
                    <label for="header_bg_image">Header Background Image URL:</label>
                    <input type="url" id="header_bg_image" name="header_bg_image" 
                           value="<?php echo esc_attr($current_settings['header_bg_image']); ?>" 
                           placeholder="https://example.com/image.jpg">
                </div>
                <button type="submit">Save General Settings</button>
            </form>
        </div>

        <!-- Subdomain Tab -->
        <div class="tab-content <?php echo $active_tab === 'subdomain' ? 'active' : ''; ?>" id="subdomain-tab">
            <h2>Subdomain Configuration</h2>
            <form method="post">
                <input type="hidden" name="tab" value="subdomain">
                <div class="form-group">
                    <label for="dashboard_subdomain">Dashboard Subdomain:</label>
                    <input type="text" id="dashboard_subdomain" name="dashboard_subdomain" 
                           value="<?php echo esc_attr($current_settings['dashboard_subdomain']); ?>" 
                           placeholder="dashboard">
                    <p style="margin-top: 5px; color: #666; font-size: 14px;">
                        Will be accessible at: https://<?php echo esc_attr($current_settings['dashboard_subdomain']); ?>.yoursite.com
                    </p>
                </div>
                <div class="form-group">
                    <label for="editor_subdomain">Editor Subdomain:</label>
                    <input type="text" id="editor_subdomain" name="editor_subdomain" 
                           value="<?php echo esc_attr($current_settings['editor_subdomain']); ?>" 
                           placeholder="editor">
                    <p style="margin-top: 5px; color: #666; font-size: 14px;">
                        Will be accessible at: https://<?php echo esc_attr($current_settings['editor_subdomain']); ?>.yoursite.com
                    </p>
                </div>
                <button type="submit">Save Subdomain Settings</button>
            </form>
        </div>

        <!-- Colors Tab -->
        <div class="tab-content <?php echo $active_tab === 'colors' ? 'active' : ''; ?>" id="colors-tab">
            <h2>Colors & Styling</h2>
            <form method="post">
                <input type="hidden" name="tab" value="colors">
                <div class="setting-row">
                    <div class="form-group">
                        <label for="primary_color">Primary Color:</label>
                        <input type="color" id="primary_color" name="primary_color" 
                               value="<?php echo esc_attr($current_settings['primary_color']); ?>">
                    </div>
                    <div class="form-group">
                        <label for="secondary_color">Secondary Color:</label>
                        <input type="color" id="secondary_color" name="secondary_color" 
                               value="<?php echo esc_attr($current_settings['secondary_color']); ?>">
                    </div>
                </div>
                <div class="setting-row">
                    <div class="form-group">
                        <label for="accent_color">Accent Color:</label>
                        <input type="color" id="accent_color" name="accent_color" 
                               value="<?php echo esc_attr($current_settings['accent_color']); ?>">
                    </div>
                    <div class="form-group">
                        <label for="text_color">Text Color:</label>
                        <input type="color" id="text_color" name="text_color" 
                               value="<?php echo esc_attr($current_settings['text_color']); ?>">
                    </div>
                </div>
                <button type="submit">Save Color Settings</button>
            </form>
        </div>

        <!-- Programme Labels Tab -->
        <div class="tab-content <?php echo $active_tab === 'labels' ? 'active' : ''; ?>" id="labels-tab">
            <h2>Programme Labels</h2>
            <form method="post">
                <input type="hidden" name="tab" value="labels">
                <div class="setting-row">
                    <div class="form-group">
                        <label for="label_miet_balang">Miet Balang Label:</label>
                        <input type="text" id="label_miet_balang" name="label_miet_balang" 
                               value="<?php echo esc_attr($current_settings['label_miet_balang']); ?>">
                    </div>
                    <div class="form-group">
                        <label for="label_miet_balang_time">Miet Balang Time:</label>
                        <input type="text" id="label_miet_balang_time" name="label_miet_balang_time" 
                               value="<?php echo esc_attr($current_settings['label_miet_balang_time']); ?>">
                    </div>
                </div>
                <div class="setting-row">
                    <div class="form-group">
                        <label for="label_jingiaseng_samla">Jingiaseng Samla Label:</label>
                        <input type="text" id="label_jingiaseng_samla" name="label_jingiaseng_samla" 
                               value="<?php echo esc_attr($current_settings['label_jingiaseng_samla']); ?>">
                    </div>
                    <div class="form-group">
                        <label for="label_jingiaseng_samla_time">Jingiaseng Samla Time:</label>
                        <input type="text" id="label_jingiaseng_samla_time" name="label_jingiaseng_samla_time" 
                               value="<?php echo esc_attr($current_settings['label_jingiaseng_samla_time']); ?>">
                    </div>
                </div>
                <div class="setting-row">
                    <div class="form-group">
                        <label for="label_jingiaseng_1pm">Jingiaseng 1PM Label:</label>
                        <input type="text" id="label_jingiaseng_1pm" name="label_jingiaseng_1pm" 
                               value="<?php echo esc_attr($current_settings['label_jingiaseng_1pm']); ?>">
                    </div>
                    <div class="form-group">
                        <label for="label_jingiaseng_1pm_time">Jingiaseng 1PM Time:</label>
                        <input type="text" id="label_jingiaseng_1pm_time" name="label_jingiaseng_1pm_time" 
                               value="<?php echo esc_attr($current_settings['label_jingiaseng_1pm_time']); ?>">
                    </div>
                </div>
                <div class="setting-row">
                    <div class="form-group">
                        <label for="label_jingiaseng_khynnah">Jingiaseng Khynnah Label:</label>
                        <input type="text" id="label_jingiaseng_khynnah" name="label_jingiaseng_khynnah" 
                               value="<?php echo esc_attr($current_settings['label_jingiaseng_khynnah']); ?>">
                    </div>
                    <div class="form-group">
                        <label for="label_jingiaseng_khynnah_time">Jingiaseng Khynnah Time:</label>
                        <input type="text" id="label_jingiaseng_khynnah_time" name="label_jingiaseng_khynnah_time" 
                               value="<?php echo esc_attr($current_settings['label_jingiaseng_khynnah_time']); ?>">
                    </div>
                </div>
                <div class="setting-row">
                    <div class="form-group">
                        <label for="label_jingiaseng_iing">Jingiaseng Iing Label:</label>
                        <input type="text" id="label_jingiaseng_iing" name="label_jingiaseng_iing" 
                               value="<?php echo esc_attr($
